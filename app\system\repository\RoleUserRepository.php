<?php
namespace app\system\repository;

use app\system\model\RoleUser as RoleUserModel;

/**
 * 角色用户关联数据仓库类
 * 负责系统角色与用户关联关系的数据访问和操作
 * <AUTHOR> <ch<PERSON><PERSON><PERSON>@live.com>
 */
class RoleUserRepository extends BaseRepository
{
    /**
     * 构造方法
     */
    public function __construct()
    {
        // 指定当前模型类
        parent::__construct(RoleUserModel::class);
    }

    /**
     * 设置用户权限组（可多个）
     * @param $user_id
     * @param $role_id
     * @return false|int
     */
    public function setRoles($user_id, $role_id)
    {
        if (is_array($role_id)) {
            $role_id_arr = $role_id;
        } else {
            $role_id_arr = explode(',', (string)$role_id);
        }
        $role_id_arr = $role_id_arr ? array_unique($role_id_arr) : [];

        $data = [];

        foreach ($role_id_arr as $key => $value) {
            $data[$key]['user_id']   = intval($user_id);
            $data[$key]['role_id']   = intval($value);
            $data[$key]['create_at'] = time();
        }

        $count = $this->model->where('user_id', $user_id)->count();

        // 如果原来该用户有对应的角色，将其删除
        if ($count > 0) {
            $handle = $this->model->where('user_id', $user_id)->delete();

            if ($handle == 0) {
                return false;
            }
        }

        return $this->model->insertAll($data);
    }

    /**
     * 根据用户id获取返回对应的角色组ids
     * @param int $user_id
     * @param int $is_active 是否正常角色
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return array
     */
    public function getRoleIdsByUserId(int $user_id = 0, int $is_active = 1): array
    {
        $data = $this->model->where('user_id', $user_id)->field('role_id')->select()->toArray();

        $role_ids_arr = $data ? array_unique(array_column($data, 'role_id')) : [];

        if (empty($role_ids_arr)) {
            return [];
        }

        if (!$is_active) {
            return $role_ids_arr;
        }

        // 根据上面筛选出来的用户id，过滤出正常角色ids
        return (new RoleRepository())->getActiveRoleIds($role_ids_arr);
    }

    /**
     * 根据角色ID获取返回对应的正常用户id数据
     * @param mixed $role_id
     * @param int $is_active 是否正常用户
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return array
     */
    public function getUserIdByRoleId(mixed $role_id, int $is_active = 1): array
    {
        $roles_id_arr = is_array($role_id) ? $role_id : explode(',', (string)$role_id);

        $data = $this->model->where([['role_id', 'IN', $roles_id_arr]])->field('user_id')->select()->toArray();

        $user_ids_arr = $data ? array_unique(array_column($data, 'user_id')) : [];

        if (empty($user_ids_arr)) {
            return [];
        }

        if (!$is_active) {
            return $user_ids_arr;
        }

        // 根据上面筛选出来的用户id，过滤出正常职员ids
        return (new UserRepository())->getActiveUserByIds($user_ids_arr);
    }

    /**
     * 根据角色ID获取返回对应的正常用户数量
     * @param mixed $role_id
     * @param int $is_active
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return int
     */
    public function getUserCountsByRoleId(mixed $role_id, int $is_active = 1): int
    {
        return count(array_unique($this->getUserIdByRoleId($role_id, $is_active)));
    }

    /**
     * 根据用户ID获取对应的角色组ids
     * @param int $user_id
     * @return array
     */
    public function getUserRoles(int $user_id = 0): array
    {
        // 获取用户对应的角色组（联合一次查询用户组信息，禁用的不查询）
        $userRoles = $this->model->alias('ru')
            ->join('system_role role', 'ru.role_id = role.id', 'LEFT')
            ->field('role.*')
            ->where('ru.user_id', $user_id)
            ->where('role.status', 1)
            ->select();

        // 转为数组 for tp
        return $userRoles->toArray();
    }
}
