<?php
declare(strict_types = 1);

namespace app\tenant\model;

use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

/**
 * 系统权限接口模型
 * <AUTHOR> <<EMAIL>>
 * @package app\tenant\model
 */
class PermissionApi extends BaseModel
{
    protected $name = 'tenant_permission_api';

    protected $field = [
        'id',
        'permission_id',
        'name',
        'url',
        'create_at',
        'update_at',
    ];

    /**
     * 获取指定权限节点的API列表
     * @param int $permission_id 权限节点ID
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @return array
     */
    public static function getApiList(int $permission_id): array
    {
        return self::where('permission_id', $permission_id)
            ->field(['name', 'url'])
            ->select()
            ->toArray();
    }

    /**
     * 批量保存权限节点的API列表
     * @param int $permission_id 权限节点ID
     * @param array $apiList API列表数据
     * @return bool
     */
    public static function saveApiList(int $permission_id, array $apiList): bool
    {
        // 删除原有的API记录
        self::where('permission_id', $permission_id)->delete();

        if (empty($apiList)) {
            return true;
        }

        $data = [];
        $now  = time();

        foreach ($apiList as $api) {
            if (empty($api['name']) || empty($api['url'])) {
                continue;
            }

            $data[] = [
                'permission_id' => $permission_id,
                'name'          => trim($api['name']),
                'url'           => trim($api['url']),
                'create_at'     => $now,
            ];
        }

        return empty($data) || (bool)(new self())->insertAll($data);
    }

    /**
     * 删除指定权限节点的所有API
     * @param int $permission_id 权限节点ID
     * @return bool
     */
    public static function deleteApisByPermissionId(int $permission_id): bool
    {
        return (bool)self::where('permission_id', $permission_id)->delete();
    }

    /**
     * 批量删除多个权限节点的API
     * @param array $permission_ids 权限节点ID数组
     * @return bool
     */
    public static function batchDeleteApisByPermissionIds(array $permission_ids): bool
    {
        if (empty($permission_ids)) {
            return true;
        }

        return (bool)self::where('permission_id', 'in', $permission_ids)->delete();
    }
}
