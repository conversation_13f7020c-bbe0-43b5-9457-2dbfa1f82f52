-- 表结构: system_configure_group
CREATE TABLE `system_configure_group` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `name` varchar(30) NOT NULL DEFAULT '' COMMENT '分类标签',
  `label` varchar(50) NOT NULL DEFAULT '' COMMENT '分类名称',
  `icon` varchar(50) NOT NULL DEFAULT '' COMMENT '图标',
  `sort` smallint(5) NOT NULL DEFAULT '0' COMMENT '排序',
  `hidden` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否隐藏',
  `is_system` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否系统自带',
  `description` varchar(255) NOT NULL DEFAULT '' COMMENT '描述',
  `create_at` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_at` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='系统配置分组表';
