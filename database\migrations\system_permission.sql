-- 表结构: system_permission
CREATE TABLE `system_permission` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `app_code` varchar(50) NOT NULL DEFAULT '' COMMENT '所属应用标识',
  `is_system` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否系统权限(1是 0否)',
  `pid` int(11) NOT NULL DEFAULT '0' COMMENT '父级权限id',
  `type` enum('menu','iframe','link','button') NOT NULL DEFAULT 'menu' COMMENT '节点类型',
  `title` varchar(50) NOT NULL DEFAULT '' COMMENT '显示名称',
  `name` varchar(50) NOT NULL DEFAULT '' COMMENT '菜单唯一名称',
  `path` varchar(255) NOT NULL DEFAULT '' COMMENT '路由路径',
  `redirect` varchar(255) NOT NULL DEFAULT '' COMMENT '重定向地址',
  `component` varchar(255) NOT NULL DEFAULT '' COMMENT '模板视图路径',
  `active` varchar(255) NOT NULL DEFAULT '' COMMENT '菜单高亮（子节点或详情页需要高亮的上级菜单路由地址）',
  `icon` varchar(100) NOT NULL DEFAULT '' COMMENT '字体图标样式',
  `color` varchar(20) NOT NULL DEFAULT '' COMMENT '颜色',
  `is_show` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否显示 1为显示 0为不显示',
  `affix` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否固定',
  `fullpage` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否整页路由',
  `fullscreen` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否全屏（和fullpage有区别，该选项会隐藏其它header等部分）',
  `minimizable` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '是否可以退出全屏',
  `not_on_tab` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '不显示在主tab上',
  `not_keepalive` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '不缓存页面状态(框架默认会缓存)',
  `tag` varchar(255) NOT NULL DEFAULT '' COMMENT '标签',
  `sort` smallint(4) NOT NULL DEFAULT '0' COMMENT '展示顺序规则',
  `create_at` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_at` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '权限状态  1为有效 0为无效',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='系统权限节点表';
