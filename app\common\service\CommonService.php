<?php
declare(strict_types = 1);

namespace app\common\service;

use think\facade\Cache;

/**
 * 全局公共服务类
 * <AUTHOR> <<EMAIL>>
 * @package app\common\service
 */
abstract class CommonService
{
    /**
     * 构造函数
     * 初始化服务类，用于子类继承时实现具体的仓储层依赖注入
     * 在子类中应通过parent::__construct()调用并初始化具体仓储层实例
     */
    public function __construct()
    {
    }

    /**
     * 获取Redis实例
     * @return object|null
     */
    protected static function getRedisHandler(): ?object
    {
        return Cache::store('redis')->handler();
    }

    /**
     * 获取Redis缓存键列表
     * 自动处理Redis前缀，确保与Cache::set使用相同的键名前缀
     * @param string $pattern 键名匹配模式
     * @return array 匹配的键名列表
     */
    protected function getRedisKeys(string $pattern): array
    {
        $config = config('cache.stores.redis');
        $prefix = $config['prefix'] ?? '';

        // 如果pattern不包含前缀，添加前缀
        if (!empty($prefix) && !str_starts_with($pattern, $prefix)) {
            $pattern = $prefix . $pattern;
        }

        $keys = self::getRedisHandler()->keys($pattern);

        // 如果有前缀，移除返回结果中的前缀
        if (!empty($prefix)) {
            return array_map(function($key) use ($prefix) {
                return substr($key, strlen($prefix));
            }, $keys);
        }

        return $keys;
    }
}
