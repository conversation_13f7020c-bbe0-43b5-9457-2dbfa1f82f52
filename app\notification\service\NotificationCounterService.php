<?php
declare(strict_types = 1);

namespace app\notification\service;

use app\notification\model\NotificationCounter as NotificationCounterModel;
use think\facade\Cache;
use think\facade\Log;

/**
 * 消息计数器服务
 * <AUTHOR> <<EMAIL>>
 * @package app\notification\service
 */
class NotificationCounterService extends BaseService
{
    private const CACHE_PREFIX = 'notification:';

    /**
     * 获取缓存键名
     * @param string ...$keys
     * @return string
     */
    private static function getCacheKey(string ...$keys): string
    {
        return self::CACHE_PREFIX . implode(':', $keys);
    }

    /**
     * 获取Redis实例
     * @return object
     */
    private function getRedis(): object
    {
        return Cache::store('redis')->handler();
    }

    /**
     * 初始化计数器
     * @param int $notificationId
     * @return bool
     */
    public function init(int $notificationId): bool
    {
        try {
            // 创建数据库记录
            $notificationCounter = NotificationCounterModel::create([
                'notification_id' => $notificationId,
                'read_count'      => 0,
                'like_count'      => 0,
                'dislike_count'   => 0,
            ]);

            if (isset($notificationCounter->id) && $notificationCounter->id > 0) {
                // 初始化Redis计数，注意将notificationId转为字符串
                $notificationIdStr = (string)$notificationId;
                $redis             = $this->getRedis();

                // 初始化各个计数器
                $redis->zAdd(self::getCacheKey('READ_COUNT'), 0, $notificationIdStr);
                $redis->zAdd(self::getCacheKey('LIKE_COUNT'), 0, $notificationIdStr);
                $redis->zAdd(self::getCacheKey('DISLIKE_COUNT'), 0, $notificationIdStr);
                $redis->hSet(self::getCacheKey('LAST_UPDATE'), $notificationIdStr, (string)time());

                return true;
            }

            return false;
        } catch (\Exception $e) {
            Log::error('计数器初始化失败: ' . $e->getMessage() . "\n通知ID: " . $notificationId);

            return false;
        }
    }

    /**
     * 判断是否需要同步到数据库
     * @param int $notificationId
     * @return bool
     */
    private function shouldUpdate(int $notificationId): bool
    {
        $redis = $this->getRedis();

        // 获取当前计数和上次更新时间
        $notificationIdStr = (string)$notificationId;
        $count             = (int)$redis->zScore(self::getCacheKey('READ_COUNT'), $notificationIdStr);
        $lastUpdateTime    = (int)$redis->hGet(self::getCacheKey('LAST_UPDATE'), $notificationIdStr);

        // 当计数大于10或者距离上次更新超过1小时时，需要同步到数据库
        return $count >= 10 || (time() - $lastUpdateTime) > 3600;
    }

    /**
     * 增加计数
     * @param int $notificationId
     * @param string $countType
     */
    private function incrCount(int $notificationId, string $countType): void
    {
        $redis             = $this->getRedis();
        $notificationIdStr = (string)$notificationId;

        // 增加计数
        $redis->zIncrBy(self::getCacheKey($countType), 1, $notificationIdStr);
        $redis->hSet(self::getCacheKey('LAST_UPDATE'), $notificationIdStr, (string)time());

        // 检查是否需要同步到数据库
        if ($this->shouldUpdate($notificationId)) {
            $this->syncCountsToDB($notificationId);
        }
    }

    /**
     * 减少计数
     * @param int $notificationId
     * @param string $countType
     */
    private function decrCount(int $notificationId, string $countType): void
    {
        $redis             = $this->getRedis();
        $notificationIdStr = (string)$notificationId;

        $currentScore = $redis->zScore(self::getCacheKey($countType), $notificationIdStr);

        // 只有当前分数大于0时才减少
        if ($currentScore > 0) {
            $redis->zIncrBy(self::getCacheKey($countType), -1, $notificationIdStr);
            $redis->hSet(self::getCacheKey('LAST_UPDATE'), $notificationIdStr, (string)time());

            // 检查是否需要同步到数据库
            if ($this->shouldUpdate($notificationId)) {
                $this->syncCountsToDB($notificationId);
            }
        }
    }

    /**
     * 增加阅读计数
     * @param int $notificationId
     */
    public function incrReadCount(int $notificationId): void
    {
        $this->incrCount($notificationId, 'READ_COUNT');
    }

    /**
     * 减少阅读计数
     * @param int $notificationId
     */
    public function decrReadCount(int $notificationId): void
    {
        $this->decrCount($notificationId, 'READ_COUNT');
    }

    /**
     * 获取互动键名
     * @param string $notificationIdStr
     * @param int $type
     * @return string
     */
    private function getInteractionKey(string $notificationIdStr, int $type): string
    {
        return self::getCacheKey('INTERACTION', "{$notificationIdStr}:{$type}");
    }

    /**
     * 获取互动用户键名
     * @param string $notificationIdStr
     * @param int $type
     * @return string
     */
    private function getInteractionUsersKey(string $notificationIdStr, int $type): string
    {
        return self::getCacheKey('INTERACTION', "users:{$notificationIdStr}:{$type}");
    }

    /**
     * 获取互动状态键名
     * @param int $notificationId
     * @param int $userId
     * @return string
     */
    private function getInteractionStatusKey(int $notificationId, int $userId): string
    {
        return self::getCacheKey('INTERACTION', "status:{$notificationId}:{$userId}");
    }

    /**
     * 缓存互动状态
     * @param int $notificationId
     * @param int $userId
     * @param int $status
     */
    public function cacheInteractionStatus(int $notificationId, int $userId, int $status): void
    {
        try {
            $redis = $this->getRedis();
            $key   = $this->getInteractionStatusKey($notificationId, $userId);
            $redis->set($key, $status);
            $redis->expire($key, 86400); // 24小时过期
        } catch (\Exception $e) {
            Log::error('缓存互动状态失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取缓存的互动状态
     * @param int $notificationId
     * @param int $userId
     * @return int|null
     */
    public function getCachedInteractionStatus(int $notificationId, int $userId): ?int
    {
        try {
            $redis  = $this->getRedis();
            $key    = $this->getInteractionStatusKey($notificationId, $userId);
            $status = $redis->get($key);

            return $status !== false ? (int)$status : null;
        } catch (\Exception $e) {
            Log::error('获取缓存互动状态失败: ' . $e->getMessage());

            return null;
        }
    }

    /**
     * 增加互动计数
     * @param int $notificationId
     * @param int $type
     */
    public function incrInteractCount(int $notificationId, int $type): void
    {
        if ($type === 1) {
            $counterKey = 'LIKE_COUNT';
        } elseif ($type === -1) {
            $counterKey = 'DISLIKE_COUNT';
        } else {
            return;
        }

        $this->incrCount($notificationId, $counterKey);
    }

    /**
     * 减少互动计数
     * @param int $notificationId
     * @param int $type
     */
    public function decrInteractCount(int $notificationId, int $type): void
    {
        if ($type === 1) {
            $counterKey = 'LIKE_COUNT';
        } elseif ($type === -1) {
            $counterKey = 'DISLIKE_COUNT';
        } else {
            return;
        }

        $this->decrCount($notificationId, $counterKey);
    }

    /**
     * 检查用户是否已经互动过
     * @param $notificationId
     * @param $userId
     * @param $type
     * @return bool
     */
    public function hasInteracted($notificationId, $userId, $type): bool
    {
        $redis             = $this->getRedis();
        $notificationIdStr = (string)$notificationId;
        $memberKey         = $this->getInteractionUsersKey($notificationIdStr, $type);

        return $redis->sIsMember($memberKey, $userId);
    }

    /**
     * 获取实时计数
     * @param int $notificationId
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return array|int[]
     */
    public function getRealTimeCount(int $notificationId): array
    {
        try {
            // 先尝试从数据库获取
            $counter = NotificationCounterModel::where('notification_id', $notificationId)->find();

            if (!$counter) {
                // 如果数据库中没有记录，尝试初始化
                if (!$this->init($notificationId)) {
                    return [
                        'read_count'    => 0,
                        'like_count'    => 0,
                        'dislike_count' => 0,
                    ];
                }
            }

            $redis             = $this->getRedis();
            $notificationIdStr = (string)$notificationId;
            $readCount         = (int)$redis->zScore(self::getCacheKey('READ_COUNT'), $notificationIdStr) ?: 0;
            $likeCount         = (int)$redis->zScore(self::getCacheKey('LIKE_COUNT'), $notificationIdStr) ?: 0;
            $dislikeCount      = (int)$redis->zScore(self::getCacheKey('DISLIKE_COUNT'), $notificationIdStr) ?: 0;

            return [
                'read_count'    => $readCount,
                'like_count'    => $likeCount,
                'dislike_count' => $dislikeCount,
            ];
        } catch (\Exception $e) {
            // 如果Redis出错，尝试从数据库获取
            $this->init($notificationId);

            $counter = NotificationCounterModel::where('notification_id', $notificationId)->find();

            if (!$counter) {
                return [
                    'read_count'    => 0,
                    'like_count'    => 0,
                    'dislike_count' => 0,
                ];
            }

            $model = NotificationCounterModel::where('notification_id', $notificationId);

            return [
                'read_count'    => $model->value('read_count') ?: 0,
                'like_count'    => $model->value('like_count') ?: 0,
                'dislike_count' => $model->value('dislike_count') ?: 0,
            ];
        }
    }

    /**
     * 同步计数到数据库
     * @param int $notificationId
     */
    private function syncCountsToDB(int $notificationId): void
    {
        try {
            $redis             = $this->getRedis();
            $notificationIdStr = (string)$notificationId;

            // 获取Redis中的计数
            $readCount    = (int)$redis->zScore(self::getCacheKey('READ_COUNT'), $notificationIdStr);
            $likeCount    = (int)$redis->zScore(self::getCacheKey('LIKE_COUNT'), $notificationIdStr);
            $dislikeCount = (int)$redis->zScore(self::getCacheKey('DISLIKE_COUNT'), $notificationIdStr);

            // 更新数据库
            $model = NotificationCounterModel::where('notification_id', $notificationId);
            $model->update([
                'read_count'    => $readCount,
                'like_count'    => $likeCount,
                'dislike_count' => $dislikeCount,
            ]);

            // 重置Redis计数
            $redis->zAdd(self::getCacheKey('READ_COUNT'), 0, $notificationIdStr);
            $redis->zAdd(self::getCacheKey('LIKE_COUNT'), 0, $notificationIdStr);
            $redis->zAdd(self::getCacheKey('DISLIKE_COUNT'), 0, $notificationIdStr);
            $redis->hSet(self::getCacheKey('LAST_UPDATE'), $notificationIdStr, (string)time());
        } catch (\Exception $e) {
            Log::error('同步计数到数据库失败: ' . $e->getMessage());
        }
    }
}
