<?php
declare(strict_types = 1);

use app\common\service\ClientService;
use app\common\service\FingerService;
use app\common\service\RequestService;
use app\system\service\AuthService;
use think\facade\Config;
use think\facade\Log;
use think\Response;

// 应用公共函数
if (!function_exists('value2Array')) {
    /**
     * 字符串转数组（常用于如ids，或者带分隔符的字符串转成为数组）
     * @param mixed $value
     * @param string $implode
     * @param callable|null $filter
     * @return array
     *               dump(value2Array(''));
     *               dump(value2Array(['a', 'b', 'c']));
     *               dump(value2Array('a,b,c'));
     *               dump(value2Array('a,b,c', '', function ($item) { return $item!== 'b'; }));
     */
    function value2Array(mixed $value, string $implode = ',', ?callable $filter = null): array
    {
        if (empty($value)) {
            return [];
        }

        if ($implode === '') {
            return [(string)$value];
        }

        $result = is_array($value) ? $value : explode($implode, trim((string)$value, " \t\n\r\0\x0B{$implode}"));

        return $filter ? array_filter($result, $filter) : array_filter($result);
    }
}

if (!function_exists('generateRandomPassword')) {
    /**
     * 生成随机密码
     * @param int $length
     * @return string
     */
    function generateRandomPassword(int $length = 8): string
    {
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ!@#$%^&*()';
        $password   = '';

        for ($i = 0; $i < $length; $i++) {
            $password .= $characters[rand(0, strlen($characters) - 1)];
        }

        return $password;
    }
}

if (!function_exists('resp_header')) {
    /**
     * 响应报文
     * @param Response|null $response
     * @param array $data
     * @param string $type
     * @param int $code
     * @param array $header
     * @param array $options
     * @return \think\Response
     */
    function resp_header(?Response $response = null, array $data = [], string $type = 'json', int $code = 200, array $header = [], array $options = []): Response
    {
        if ($response == null) {
            $response = Response::create($data, $type, $code);
        }

        if (!$header) {
            $allowHeaders = ['Content-Type'];

            $allowHeaders[] = config('session.name');
            $allowHeaders[] = AuthService::HEADER_AUTHORIZATION_KEY;
            $allowHeaders[] = ClientService::HEADER_CLIENT_ID_KEY;
            $allowHeaders[] = FingerService::HEADER_FINGER_ID_KEY;

            // 是否开启了用户请求追踪
            if (RequestService::ENABLE_REQUEST_ID) {
                $allowHeaders[] = RequestService::HEADER_REQUEST_ID_KEY;

                if (RequestService::ENABLE_SEQUENCE_NUM) {
                    $allowHeaders[] = RequestService::HEADER_SEQUENCE_NUM_KEY;
                }
            }

            $header = [
                'Access-Control-Allow-Origin'      => '*',
                'Access-Control-Allow-Methods'     => 'POST,GET,PUT,DELETE,OPTIONS',
                'Access-Control-Allow-Headers'     => implode(',', $allowHeaders),
                'Access-Control-Allow-Credentials' => 'false',
                'Access-Control-Max-Age'           => 1800,
            ];

            if (RequestService::ENABLE_SEQUENCE_NUM) {
                $exposeHeaders = [RequestService::HEADER_REQUEST_ID_KEY, RequestService::HEADER_SEQUENCE_NUM_KEY];

                // 控制响应时前端可以读取哪些自定义头
                $header[] = ['Access-Control-Expose-Headers' => implode(',', $exposeHeaders)];
            }
        }

        return $response->header($header)->options($options);
    }
}

if (!function_exists('is_email')) {
    /**
     * 判断是否是邮箱格式
     * @param $email
     * @return mixed
     */
    function is_email($email): mixed
    {
        return filter_var($email, FILTER_VALIDATE_EMAIL);
    }
}

if (!function_exists('is_mobile')) {
    /**
     * 判断是否是手机号
     * @param $phone
     * @return int|bool
     */
    function is_mobile($phone): int|bool
    {
        return preg_match("/^(?:\+?86)?0?1[3-9]\d{9}$/", $phone);
    }
}

if (!function_exists('format_bytes')) {
    /**
     * 格式化字节大小
     * @param mixed $size 字节数
     * @param string $delimiter 数字和单位分隔符
     * @return string 格式化后的带单位的大小
     */
    function format_bytes(mixed $size, string $delimiter = ''): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];

        for ($i = 0; $size >= 1024 && $i < 5; $i++) {
            $size /= 1024;
        }

        return round((float)$size, 2) . $delimiter . $units[$i];
    }
}

if (!function_exists('std_class_object_to_array')) {
    /**
     * PHP stdClass Object转array
     * @param $data
     * @return mixed
     */
    function std_class_object_to_array($data): mixed
    {
        if (is_object($data)) {
            $data = (array)$data;
        }

        if (is_array($data)) {
            foreach ($data as $key => $value) {
                $data[$key] = std_class_object_to_array($value);
            }
        }

        return $data;
    }
}

if (!function_exists('format_seconds')) {
    /**
     * 将秒数转换为最合适的时间单位表示
     * 自动选择最大的合适单位（秒、分、时、天、月、年）
     *
     * @param int $seconds 秒数
     * @param bool $short 是否使用短格式（如：1年 vs 1y）
     * @param int $precision 小数点精度
     * @return string 格式化后的时间
     *
     * 示例:
     * format_seconds(30)          // 返回 "30秒"
     * format_seconds(90)          // 返回 "1.5分钟"
     * format_seconds(3600)        // 返回 "1小时"
     * format_seconds(86400)       // 返回 "1天"
     * format_seconds(2592000)     // 返回 "1月"
     * format_seconds(31536000)    // 返回 "1年"
     * format_seconds(31536000, true) // 返回 "1y"
     */
    function format_seconds(int $seconds, bool $short = false, int $precision = 1): string
    {
        // 定义时间单位（秒、分、时、天、月、年）
        $units = [
            'second' => [1, $short ? 's' : '秒'],
            'minute' => [60, $short ? 'm' : '分钟'],
            'hour'   => [3600, $short ? 'h' : '小时'],
            'day'    => [86400, $short ? 'd' : '天'],
            'month'  => [2592000, $short ? 'mo' : '月'], // 按30天计算
            'year'   => [31536000, $short ? 'y' : '年'],  // 按365天计算
        ];

        // 从大到小排序单位
        $units = array_reverse($units, true);

        // 查找最大的合适单位
        foreach ($units as $unit => $value) {
            if ($seconds >= $value[0]) {
                $result = $seconds / $value[0];

                // 如果结果是整数，不显示小数点
                if ($result == (int)$result) {
                    return (int)$result . $value[1];
                }

                // 否则保留指定精度的小数
                return round($result, $precision) . $value[1];
            }
        }

        // 如果小于1秒，返回0秒
        return '0' . $units['second'][1];
    }
}

if (!function_exists('getAppConfig')) {
    /**
     * 获取某个应用的配置
     * @param string $name 配置名称
     * @param string $appName 应用名称
     * @param bool $merge 是否与全局配置合并
     * @return array
     */
    function getAppConfig(string $name, string $appName, bool $merge = true): array
    {
        // 获取全局配置
        $globalConfig = [];

        if ($merge) {
            $globalConfigPath = base_path() . 'common/config/' . $name . '.php';

            if (file_exists($globalConfigPath)) {
                $globalConfig = Config::load($globalConfigPath, $name);
            }
        }

        // 获取应用配置
        $appConfig = [];

        if (empty($appName)) {
            $appConfig = Config::load(base_path() . app('http')->getName() . '/config/' . $name . '.php', $name);
        } else {
            $appConfig = Config::load(base_path() . $appName . '/config/' . $name . '.php', $name);
        }

        // 合并配置，应用配置优先级高于全局配置
        return $merge ? array_merge($globalConfig, $appConfig) : $appConfig;
    }

    if (!function_exists('verifyCert')) {
        /**
         * 验证证书文件合法性
         * @param string $cert 证书地址
         * @return bool
         */
        function verifyCert(string $cert): bool
        {
            if (empty($cert)) {
                Log::warning('Empty client certificate');

                return false;
            }

            // 判断是否为文件路径
            if (is_file($cert)) {
                $certificate = file_get_contents($cert);

                if ($certificate === false) {
                    throw new \RuntimeException('Failed to read certificate file');
                }

                // 加载证书
                $cert_resource = openssl_x509_read($certificate);

                if (!$cert_resource) {
                    Log::warning('Failed to read client certificate');

                    return false;
                }

                // 解析证书信息
                $cert_info = openssl_x509_parse($cert_resource);

                if (!$cert_info) {
                    Log::warning('Failed to parse client certificate');

                    return false;
                }

                // 获取证书配置
                $cert_config = Config::get('cert.server_cert');
                $validation  = $cert_config['validation'];

                // 验证证书有效期
                if ($validation['check_validity']) {
                    $now = time();

                    if ($now < $cert_info['validFrom_time_t'] || $now > $cert_info['validTo_time_t']) {
                        Log::warning('Certificate is not valid at current time');

                        return false;
                    }
                }

                // 验证证书颁发者
                if (!empty($validation['issuer_dn'])) {
                    if ($cert_info['issuer']['DN'] !== $validation['issuer_dn']) {
                        Log::warning('Invalid certificate issuer');

                        return false;
                    }
                }

                // 验证证书主题
                if (!empty($validation['subject_dn'])) {
                    if ($cert_info['subject']['DN'] !== $validation['subject_dn']) {
                        Log::warning('Invalid certificate subject');

                        return false;
                    }
                }

                // 验证组织名称
                if (!empty($validation['allowed_organizations'])) {
                    $org = $cert_info['subject']['O'] ?? '';

                    if (!in_array($org, $validation['allowed_organizations'])) {
                        Log::warning('Invalid certificate organization', ['org' => $org]);

                        return false;
                    }
                }

                return true;
            }
        }
    }
}
