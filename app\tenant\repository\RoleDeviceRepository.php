<?php
declare(strict_types = 1);

namespace app\tenant\repository;

use app\tenant\model\Device as DeviceModel;
use app\tenant\model\Role as RoleModel;
use app\tenant\model\RoleDevice as RoleDeviceModel;
use think\facade\Db;

/**
 * 租户用户角色设备关联数据仓库类
 * 负责租户用户角色和设备之间关联数据的访问和操作
 * <AUTHOR> <<EMAIL>>
 * @package app\tenant\controller
 */
class RoleDeviceRepository extends BaseRepository
{
    public function __construct()
    {
        // 指定当前模型类名而非实例，避免条件累积问题
        parent::__construct(RoleDeviceModel::class);
    }

    /**
     * 分配设备到角色
     * @param int $roleId
     * @param array $deviceIds
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \Exception
     * @return bool
     */
    public function assignDevices(int $roleId, array $deviceIds): bool
    {
        $this->model = new RoleDeviceModel();

        $deviceIds = array_map('intval', array_unique($deviceIds));

        // 验证角色是否属于当前租户
        $role = RoleModel::where(['id' => $roleId, 'tenant_id' => $this->getTenantId(), 'status' => 1])->count();

        if (empty($role)) {
            throw new \Exception('无效的角色ID');
        }

        // 如果传过来设备ids为空，说明是删除原有的授权
        if (empty($deviceIds)) {
            $this->model->where(['role_id' => $roleId, 'tenant_id' => $this->getTenantId()])->delete();

            return true;
        }

        // 取出有效的设备ids
        $validDeviceIds = DeviceModel::whereIn('id', $deviceIds)->where(['tenant_id' => $this->getTenantId(), ['status', '>=', 0]])->column('id');

        if (empty($validDeviceIds)) {
            throw new \Exception('无效的设备IDS');
        }

        // 验证设备ids是否重复分配
        $existRoleDeviceIds = RoleDeviceModel::whereIn('device_id', $deviceIds)->whereNotIn('role_id', $roleId)->column('role_id,device_id');

        if (!empty($existRoleDeviceIds)) {
            throw new \Exception('设备ID ' . implode(',', array_column($existRoleDeviceIds, 'device_id')) . ' 已经分配给角色组ID ' . implode(',', array_unique(array_column($existRoleDeviceIds, 'role_id'))) . '，请在原角色组中删除');
        }

        $invalidDeviceIds = array_diff($deviceIds, $validDeviceIds);

        if (!empty($invalidDeviceIds)) {
            throw new \Exception('设备ID ' . implode(',', $invalidDeviceIds) . ' 无效');
        }

        Db::startTrans();

        try {
            // 先删除该角色的所有已分配设备
            $this->model->where(['role_id' => $roleId, 'tenant_id' => $this->getTenantId()])->delete();

            // 重新分配设备
            $data = [];

            foreach ($deviceIds as $deviceId) {
                // 检查设备是否已被分配给其他角色
                $exists = $this->model->where('device_id', $deviceId)->find();

                if ($exists) {
                    throw new \Exception("设备ID {$deviceId} 已分配给其他角色组");
                }

                $data[] = [
                    'tenant_id' => $this->getTenantId(),
                    'role_id'   => $roleId,
                    'device_id' => $deviceId,
                ];
            }

            if (!empty($data)) {
                $this->model->insertAll($data);
            }

            Db::commit();

            return true;
        } catch (\Exception $e) {
            Db::rollback();

            throw $e;
        }
    }

    /**
     * 获取角色已分配的设备ID列表
     * @param int $roleId
     * @return array
     */
    public function getAssignedDeviceIds(int $roleId): array
    {
        return (new RoleDeviceModel())->where(['tenant_id' => $this->getTenantId(), 'role_id' => $roleId])->column('device_id');
    }
}
