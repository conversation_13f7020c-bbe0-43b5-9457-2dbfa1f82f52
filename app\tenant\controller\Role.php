<?php
declare(strict_types = 1);

namespace app\tenant\controller;

use app\tenant\repository\RoleDeviceRepository;
use app\tenant\repository\RoleRepository;
use app\tenant\validate\Role as RoleValidate;
use think\App;
use think\Response;

/**
 * 租户角色控制器
 * <AUTHOR> <<EMAIL>>
 * @package app\tenant\controller
 */
class Role extends AuthBase
{
    public function __construct(App $app)
    {
        parent::__construct($app);
    }

    /**
     * 角色列表
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return \think\Response
     */
    public function list(): Response
    {
        $params   = $this->request->param();
        $pageNo   = $this->request->param('page', 1, 'intval');
        $pageSize = $this->request->param('pageSize', 20, 'intval');
        $userId   = $this->request->param('user_id', 0, 'intval');

        // 如果传入了用户ID，添加到查询参数中
        if ($userId > 0) {
            $params['user_id'] = $userId;
        }

        $result = (new RoleRepository())->getList($params, $pageNo, $pageSize);

        return $this->success($result);
    }

    /**
     * 新增角色
     * @throws \Exception
     * @return \think\Response
     */
    public function add(): Response
    {
        $params = $this->request->param();

        // 指定只能操作当租户ID
        $params['tenant_id'] = $this->getTenantId();

        // 参数验证
        $validate = new RoleValidate();

        if (!$validate->scene('add')->check($params)) {
            return $this->error($validate->getError());
        }

        // 创建角色
        $result = (new RoleRepository())->addInfo($params);

        // 说明添加成功
        if (!empty($result->id)) {
            // 记录操作日志
            $this->logService->recordOperationLog(
                '添加角色组',
                $result->toArray()
            );

            return $this->success('添加成功');
        }

        return $this->error('添加失败');
    }

    /**
     * 编辑角色
     * @return \think\Response
     */
    public function edit(): Response
    {
        $id     = $this->request->param('id/d');
        $params = $this->request->param();

        // 指定只能操作当租户ID
        $params['tenant_id'] = $this->getTenantId();

        // 参数验证
        $validate = new RoleValidate();

        if (!$validate->scene('edit')->check($params)) {
            return $this->error($validate->getError());
        }

        $roleRepository = new RoleRepository();

        // 检查角色是否存在
        $role = $roleRepository->getTenantRoleById($id);

        if (!$role) {
            return $this->error('角色组不存在');
        }

        // 更新角色信息
        $result = $roleRepository->updateInfo($id, $params);

        if ($result) {
            // 写入事件日志
            $this->logService->recordOperationLog(
                '编辑角色组',
                ['id' => $id]
            );

            return $this->success('编辑成功');
        }

        return $this->error('编辑失败');
    }

    /**
     * 更新角色状态
     * @return Response
     */
    public function status(): Response
    {
        $id     = $this->request->param('id/d');
        $status = $this->request->param('status', 0, 'intval');

        if (!in_array($status, [0, 1])) {
            return $this->error('状态值不正确');
        }

        $roleRepository = new RoleRepository();

        // 检查角色是否存在
        $role = $roleRepository->getTenantRoleById($id);

        if (!$role) {
            return $this->error('角色不存在');
        }

        // 更新状态
        if (!$roleRepository->setStatus($id, $status)) {
            return $this->error('更新状态失败');
        }

        // 写入事件日志
        $this->logService->recordOperationLog(
            '更新角色组状态',
            [
                'id'     => $id,
                'status' => $status,
            ]
        );

        return $this->success();
    }

    /**
     * 删除角色
     * @return Response
     */
    public function delete(): Response
    {
        $id = $this->request->param('id/d');

        $roleRepository = new RoleRepository();

        // 检查角色是否存在
        $role = $roleRepository->getTenantRoleById($id);

        if (!$role) {
            return $this->error('角色组不存在');
        }

        // 删除角色（软删除）
        if (!$roleRepository->recycle($id)) {
            return $this->error('删除角色失败');
        }

        // 写入事件日志
        $this->logService->recordOperationLog(
            '软删除角色组',
            ['id' => $id]
        );

        return $this->success();
    }

    /**
     * 获取角色已分配的设备ID列表
     * @return \think\Response
     */
    public function getDeviceIds(): Response
    {
        $roleId = $this->request->param('id/d');

        if (!$roleId) {
            return $this->error('参数错误');
        }

        try {
            // 获取角色已分配的设备ID列表
            $deviceIds = (new RoleDeviceRepository())->getAssignedDeviceIds($roleId);

            return $this->success(['device_ids' => $deviceIds]);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 分配设备到角色
     * @return \think\Response
     */
    public function assignDevices(): Response
    {
        $roleId    = $this->request->param('id/d');
        $deviceIds = $this->request->param('device_ids/a', []);

        if (!$roleId) {
            return $this->error('参数错误');
        }

        if (!is_array($deviceIds)) {
            return $this->error('参数device_ids必须为数组');
        }

        try {
            (new RoleDeviceRepository())->assignDevices($roleId, $deviceIds);

            return $this->success('分配成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}
