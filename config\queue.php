<?php

return [
    'default'     => 'redis',
    'connections' => [
        'redis' => [
            // 驱动方式
            'type' => env('REDIS.TYPE', 'redis'),
            // 服务器地址
            'host' => env('REDIS.HOST', '127.0.0.1'),
            // 端口
            'port' => env('REDIS.PORT', 6379),
            // 密码
            'password' => env('REDIS.PASSWORD', ''),
            // 超时
            'timeout' => env('REDIS.TIMEOUT', 0),
            // 缓存库
            'select' => env('REDIS.SELECT', 0),
            // 缓存前缀
            'prefix' => env('REDIS.PREFIX', ''),
            // 缓存有效期 0表示永久缓存
            'expire' => 0,
            // 队列名称
            'queue' => 'notification',
            // 是否长连接
            'persistent' => false,
        ],
    ],
];
