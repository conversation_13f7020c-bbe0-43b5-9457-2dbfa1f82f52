<?php
namespace app\system\repository;

use app\system\model\OperationLog as OperationLogModel;
use think\facade\Cache;

/**
 * 操作日志数据仓库类
 * 负责系统操作日志相关的数据访问和操作
 * <AUTHOR> <<EMAIL>>
 * @package app\system\repository
 */
class OperationLogRepository extends BaseRepository
{
    /**
     * 缓存前缀
     */
    public const CACHE_PREFIX = 'system:operation_log:';

    /**
     * 缓存时间（秒）
     */
    public const CACHE_TTL = 3600;

    /**
     * 构造方法
     */
    public function __construct()
    {
        // 指定当前模型类名而非实例，避免条件累积问题
        parent::__construct(OperationLogModel::class);
    }

    /**
     * 是否启用缓存
     */
    protected bool $enableCache = false;

    /**
     * 设置缓存开关
     * @param bool $enable
     * @return self
     */
    public function setEnableCache(bool $enable = true): self
    {
        $this->enableCache = $enable;

        return $this;
    }

    /**
     * 获取日志详情（带缓存）
     * @param int $id
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return array|null
     */
    public function getLogInfo(int $id): ?array
    {
        if (!$this->enableCache) {
            $info = $this->model->with(['user'])->find($id);

            return $info ? $info->toArray() : null;
        }

        $cacheKey = self::CACHE_PREFIX . $id;
        $info     = Cache::get($cacheKey);

        if ($info === null) {
            $info = $this->model->with(['user'])->find($id);

            if ($info) {
                $info = $info->toArray();
                Cache::set($cacheKey, $info, self::CACHE_TTL);
            }
        }

        return $info;
    }

    /**
     * 删除日志（同时删除缓存）
     * @param mixed $id
     * @return bool
     */
    public function deleteData(mixed $id): bool
    {
        $result = $this->model->where('id', $id)->delete();

        if ($result && $this->enableCache) {
            Cache::delete(self::CACHE_PREFIX . $id);
        }

        return $result;
    }

    /**
     * 清空所有日志
     * @return bool
     */
    public function deleteAll(): bool
    {
        $result = $this->model->where('1=1')->delete();

        if ($result) {
            Cache::tag(self::CACHE_PREFIX)->clear();
        }

        return $result !== false;
    }

    /**
     * 获取操作日志列表
     * @param array $userInfo 用户信息
     * @param array $params 查询参数
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return array
     */
    public function getList(array $userInfo, array $params = []): array
    {
        // 设置关联模型
        $this->setCondition(['user'], 'with');

        // 设置基础查询条件
        if (!empty($userInfo['is_super']) && !empty($params['user_id'])) {
            $this->setCondition([
                ['user_id', '=', $params['user_id']],
            ]);
        } else {
            $this->setCondition([
                ['user_id', '=', $userInfo['id']],
            ]);
        }

        // 设置搜索条件
        if (!empty($params['event'])) {
            $this->setCondition([
                ['event', 'LIKE', "%{$params['event']}%"],
            ]);
        }

        if (!empty($params['path'])) {
            $this->setCondition([
                ['path', 'LIKE', "%{$params['path']}%"],
            ]);
        }

        if (!empty($params['start_time'])) {
            $this->setCondition([
                ['create_at', '>=', strtotime($params['start_time'])],
            ]);
        }

        if (!empty($params['end_time'])) {
            $this->setCondition([
                ['create_at', '<=', strtotime($params['end_time'])],
            ]);
        }

        if (!empty($params['keyword'])) {
            $this->setCondition([
                function($query) use ($params) {
                    $query->whereOr([
                        ['event', 'LIKE', "%{$params['keyword']}%"],
                        ['path', 'LIKE', "%{$params['keyword']}%"],
                        ['params', 'LIKE', "%{$params['keyword']}%"],
                        ['user_agent', 'LIKE', "%{$params['keyword']}%"],
                        ['description', 'LIKE', "%{$params['keyword']}%"],
                    ]);
                },
            ]);
        }

        // 获取分页参数
        $page     = !empty($params['page']) ? intval($params['page']) : 1;
        $pageSize = !empty($params['pageSize']) ? intval($params['pageSize']) : 20;

        // 根据缓存开关决定是否使用缓存
        if (!$this->enableCache) {
            return $this->pageList($params, $page, $pageSize);
        }

        // 使用缓存
        $cacheKey = self::CACHE_PREFIX . md5(json_encode([$userInfo['id'], $params, $page, $pageSize]));
        $result   = Cache::get($cacheKey);

        if ($result === null) {
            $result = $this->pageList($params, $page, $pageSize);
            Cache::set($cacheKey, $result, self::CACHE_TTL);
        }

        return $result;
    }

    /**
     * 批量删除日志
     * @param array $ids
     * @return bool
     */
    public function deleteBatch(array $ids): bool
    {
        $result = $this->model->whereIn('id', $ids)->delete();

        if ($result) {
            foreach ($ids as $id) {
                Cache::delete(self::CACHE_PREFIX . $id);
            }
        }

        return $result !== false;
    }

    /**
     * 统计日志数量
     * @param array $condition
     * @return int
     */
    public function count(array $condition = []): int
    {
        return $this->model->where($condition)->count();
    }
}
