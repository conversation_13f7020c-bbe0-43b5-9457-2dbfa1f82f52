-- 表结构: tenant
CREATE TABLE `tenant` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '租户ID',
  `name` varchar(50) NOT NULL DEFAULT '' COMMENT '租户名称',
  `code` varchar(20) NOT NULL DEFAULT '' COMMENT '租户编码',
  `description` varchar(255) NOT NULL DEFAULT '' COMMENT '租户描述',
  `domain` varchar(100) NOT NULL DEFAULT '' COMMENT '租户域名',
  `contact_person` varchar(20) NOT NULL DEFAULT '' COMMENT '联系人',
  `contact_phone` varchar(20) NOT NULL DEFAULT '' COMMENT '联系电话',
  `expire_time` int(11) NOT NULL DEFAULT '-1' COMMENT '过期时间（-1时永不过期）',
  `max_users` int(11) NOT NULL DEFAULT '-1' COMMENT '最大用户数（-1时不限制）',
  `sort` smallint(4) NOT NULL DEFAULT '0' COMMENT '序号',
  `create_at` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_at` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  `delete_at` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '删除时间',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态(1:正常,0:禁用)',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_code` (`code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='租户信息表';
