<?php
declare(strict_types = 1);

namespace app\system\model;

use think\db\Query;
use think\model\relation\BelongsTo;
use think\model\relation\BelongsToMany;
use think\model\relation\HasMany;

/**
 * 部门模型
 * <AUTHOR> <<EMAIL>>
 * @package app\system\model
 */
class Department extends BaseModel
{
    protected $name = 'system_department';

    protected $field = [];

    /**
     * 获取部门下的所有子部门
     * @return HasMany
     */
    public function children(): HasMany
    {
        return $this->hasMany(self::class, 'pid', 'id');
    }

    /**
     * 获取部门的父部门
     * @return BelongsTo
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(self::class, 'pid', 'id');
    }

    /**
     * 获取部门下的所有用户
     * @return BelongsToMany
     */
    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, DepartmentUser::class, 'user_id', 'department_id');
    }

    /**
     * 查询启用状态的部门
     * @param Query $query
     * @return \think\db\Query
     */
    public function scopeActive(Query $query): Query
    {
        return $query->where('status', 1);
    }

    /**
     * 按名称搜索部门
     * @param Query $query
     * @param string $name
     * @return \think\db\Query
     */
    public function scopeByName(Query $query, string $name): Query
    {
        return $query->where('name', 'like', "%{$name}%");
    }
}
