<?php
// +----------------------------------------------------------------------
// | Token配置
// +----------------------------------------------------------------------
return [
    // Token风格配置，支持md5、uuid、jwt三种方式
    'token_style' => env('TOKEN.STYLE', 'jwt'),

    // 各种Token风格的配置
    'styles' => [
        // MD5风格配置（默认）
        'md5' => [
            // 是否添加随机数前缀
            'use_random_prefix' => true,
        ],

        // UUID风格配置
        'uuid' => [
            // UUID版本，支持1-5
            'version' => 4,
        ],

        // JWT风格配置
        'jwt' => [
            // JWT签名算法
            'algorithm' => env('TOKEN.JWT_ALG', 'HS256'),
            // 签发者
            'issuer' => env('TOKEN.JWT_ISSUER', 'tenant'),
            // 接收者
            'audience' => env('TOKEN.JWT_AUDIENCE', 'user'),
            // 是否在payload中包含用户IP
            'include_ip' => env('TOKEN.JWT_INCLUDE_IP', false),
            // 是否在payload中包含设备信息
            'include_device' => env('TOKEN.JWT_INCLUDE_DEVICE', false),
        ],
    ],

    // Token过期时间配置（秒）
    'expire_time' => [
        'access_token'  => (int)env('TOKEN.ACCESS_TOKEN_EXPIRE', 2 * 24 * 60 * 60),  // 默认48小时
        'refresh_token' => (int)env('TOKEN.REFRESH_TOKEN_EXPIRE', 7 * 24 * 60 * 60), // 默认7天
    ],

    // JWT密钥配置
    'jwt_key' => env('TOKEN.JWT_KEY', 'a666a7aa-0c4c-4602-5f8c-82cc9e18c9ca'),
];
