<?php
declare(strict_types = 1);
namespace app\common\constant;

/**
 * 基础常量类
 * 系统常量号段划分：
 * - 0-9999: 基础系统常量【200：全局成功标识码，201：全局默认异常标识码】
 * - 10000-19999: 认证鉴权相关常量【拦截器相关常量 (10010-10099)，账号验证相关常量 (10100-10199)，token相关常量 (10200-10299)，权限验证相关常量 (10300-10399)】
 * - 20000-29999: 应用模块相关常量
 * - 30000-39999: 业务模块相关常量
 * - 40000-49999: 三方服务相关常量
 * <AUTHOR> <<EMAIL>>
 * @package app\common\constant
 */
abstract class CommonConstant
{
    /**
     * 获取常量信息
     * @param int|string $code 常量标识
     * @return string
     */
    abstract public static function getMessage(int|string $code): string;

    /**
     * 获取常量标签
     * @param int|string $code 常量标识
     * @return string
     */
    public static function getLabel(int|string $code): string
    {
        return '';
    }

    /**
     * 检查常量是否存在
     * @param int|string $code 常量标识
     * @return bool
     */
    public static function hasConstant(int|string $code): bool
    {
        $ref = new \ReflectionClass(static::class);

        return in_array($code, $ref->getConstants());
    }
}
