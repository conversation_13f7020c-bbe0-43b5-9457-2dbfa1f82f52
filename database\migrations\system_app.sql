-- 表结构: system_app
CREATE TABLE `system_app` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '应用ID',
  `name` varchar(50) NOT NULL DEFAULT '' COMMENT '应用名称',
  `code` varchar(50) NOT NULL DEFAULT '' COMMENT '应用标识',
  `version` varchar(20) NOT NULL DEFAULT '' COMMENT '版本号',
  `description` varchar(255) NOT NULL DEFAULT '' COMMENT '应用描述',
  `icon` varchar(100) NOT NULL DEFAULT '' COMMENT '应用图标',
  `author` varchar(50) NOT NULL DEFAULT '' COMMENT '作者',
  `website` varchar(100) NOT NULL DEFAULT '' COMMENT '官网',
  `menu_id` int(11) NOT NULL DEFAULT '0' COMMENT '关联的顶级菜单ID',
  `config` text COMMENT '应用配置JSON',
  `is_system` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否系统应用(1是 0否)',
  `create_at` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_at` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态(1:启用,0:禁用)',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_code` (`code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统应用表';
