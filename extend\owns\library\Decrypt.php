<?php
declare(strict_types = 1);

namespace owns\library;

class Decrypt
{
    // 秘钥
    public const KEY = '5584A3A8-21F1-4DAE-806A-FB367A362FE8';

    // 加密方法
    public const CIPHER = 'aes-128-cbc';

    // 数据格式选项
    public const OPTION = 0;

    // 密初始化向量
    public const IV = 'zAvR2NI87bBx746n';

    /**
     * 解密
     * @param string $str 加密字符串
     * @return string
     */
    public static function encrypt($str): string
    {
        if (!$str) {
            return '';
        }

        return openssl_encrypt($str, self::CIPHER, self::KEY, self::OPTION, self::IV);
    }

    /**
     * 解密
     * @param string $str 解密字符串
     * @return string
     */
    public static function decrypt($str): string
    {
        if (!$str) {
            return '';
        }

        return openssl_decrypt($str, self::CIPHER, self::KEY, self::OPTION, self::IV);
    }
}
