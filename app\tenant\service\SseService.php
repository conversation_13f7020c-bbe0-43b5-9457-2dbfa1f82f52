<?php
declare(strict_types = 1);

namespace app\tenant\service;

use app\common\service\mqtt\MqttService;
use think\facade\Log;

/**
 * SSE服务类
 * 用于管理SSE连接和消息发送，提供给其他服务调用
 * <AUTHOR> <<EMAIL>>
 * @package app\tenant\service
 */
class SseService extends BaseService
{
    /**
     * 单例实例
     * @var SseService
     */
    private static ?SseService $instance = null;

    /**
     * MQTT服务实例
     * @var MqttService
     */
    protected MqttService $mqttService;

    /**
     * MQTT主题前缀
     * @var string
     */
    protected string $topicPrefix = 'helio/tenant/';

    /**
     * 构造函数
     */
    private function __construct()
    {
        $this->mqttService = MqttService::getInstance();
    }

    /**
     * 获取单例实例
     * @return SseService
     */
    public static function getInstance(): self
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }

        return self::$instance;
    }

    /**
     * 发送SSE消息到指定租户
     * @param string $tenantCode 租户代码
     * @param string $event 事件名称
     * @param array $data 消息数据
     * @return bool
     */
    public function sendToTenant(string $tenantCode, string $event, array $data): bool
    {
        try {
            $topic   = $this->topicPrefix . $tenantCode . '/sse/notification';
            $message = [
                'type'      => 'tenant',
                'event'     => $event,
                'data'      => $data,
                'timestamp' => time(),
            ];

            return $this->mqttService->publish($topic, json_encode($message, JSON_UNESCAPED_UNICODE));
        } catch (\Exception $e) {
            Log::error('发送SSE消息到租户失败: ' . $e->getMessage());

            return false;
        }
    }

    /**
     * 发送SSE消息到指定用户
     * @param string $tenantCode 租户代码
     * @param int $userId 用户ID
     * @param string $event 事件名称
     * @param array $data 消息数据
     * @return bool
     */
    public function sendToUser(string $tenantCode, int $userId, string $event, array $data): bool
    {
        try {
            $topic   = $this->topicPrefix . $tenantCode . '/sse/notification/user/' . $userId;
            $message = [
                'type'      => 'user',
                'userId'    => $userId,
                'event'     => $event,
                'data'      => $data,
                'timestamp' => time(),
            ];

            return $this->mqttService->publish($topic, json_encode($message, JSON_UNESCAPED_UNICODE));
        } catch (\Exception $e) {
            Log::error('发送SSE消息到用户失败: ' . $e->getMessage());

            return false;
        }
    }

    /**
     * 发送系统通知
     * @param string $tenantCode 租户代码
     * @param string $title 通知标题
     * @param string $content 通知内容
     * @param string $level 通知级别 (info, warning, error)
     * @return bool
     */
    public function sendSystemNotification(string $tenantCode, string $title, string $content, string $level = 'info'): bool
    {
        return $this->sendToTenant($tenantCode, 'system_notification', [
            'title'   => $title,
            'content' => $content,
            'level'   => $level,
        ]);
    }

    /**
     * 发送设备状态变更通知
     * @param string $tenantCode 租户代码
     * @param int $deviceId 设备ID
     * @param string $status 设备状态
     * @param array $extraData 额外数据
     * @return bool
     */
    public function sendDeviceStatusNotification(string $tenantCode, int $deviceId, string $status, array $extraData = []): bool
    {
        $data = [
            'deviceId'  => $deviceId,
            'status'    => $status,
            'timestamp' => time(),
        ];

        if (!empty($extraData)) {
            $data = array_merge($data, $extraData);
        }

        return $this->sendToTenant($tenantCode, 'device_status', $data);
    }

    /**
     * 发送告警通知
     * @param string $tenantCode 租户代码
     * @param string $alarmType 告警类型
     * @param string $alarmContent 告警内容
     * @param int $deviceId 设备ID
     * @param string $level 告警级别
     * @return bool
     */
    public function sendAlarmNotification(string $tenantCode, string $alarmType, string $alarmContent, int $deviceId, string $level = 'warning'): bool
    {
        return $this->sendToTenant($tenantCode, 'alarm', [
            'type'      => $alarmType,
            'content'   => $alarmContent,
            'deviceId'  => $deviceId,
            'level'     => $level,
            'timestamp' => time(),
        ]);
    }
}
