# setCondition 方法使用说明

`setCondition` 是一个用于设置全局查询条件的静态方法,可以灵活地设置数据查询的各种条件。

## 方法签名

```php
public static function setCondition(array $conditions, string $type = 'where'): void
```

## 参数说明

- `conditions`: 查询条件数组
- `type`: 条件类型,可选值:
  - `where`: WHERE 查询条件
  - `order`: 排序条件
  - `field`: 字段选择
  - `limit`: LIMIT 限制

## 使用示例

### 1. 设置 WHERE 条件

```php
// 设置简单的等值查询
User::setCondition(['status' => 1]);

// 设置复杂的条件查询
User::setCondition([
    ['status', '=', 1],
    ['age', '>', 18],
    ['create_time', 'between', ['2023-01-01', '2023-12-31']]
]);

// 使用闭包设置复杂条件
User::setCondition([
    function($query) {
        $query->where('status', 1)->whereOr('is_vip', 1);
    }
]);
```

### 2. 设置排序条件

```php
// 单字段排序
User::setCondition(['create_time' => 'desc'], 'order');

// 多字段排序
User::setCondition([
    'age' => 'asc',
    'create_time' => 'desc'
], 'order');
```

### 3. 设置字段选择

```php
// 选择指定字段
User::setCondition(['id', 'username', 'email'], 'field');
```

### 4. 设置 LIMIT 限制

```php
// 限制返回前10条记录
User::setCondition([0, 10], 'limit');
```

### 5. 高级查询示例

```php
// 使用闭包实现复杂的 OR 条件查询
User::setCondition([
    function($query) use ($keyword) {
        $query->whereOr([
            ['username', 'LIKE', "%{$keyword}%"],
            ['nickname', 'LIKE', "%{$keyword}%"],
            ['email', 'LIKE', "%{$keyword}%"]
        ]);
    }
]);

// 组合 AND 和 OR 条件
User::setCondition([
    ['status', '=', 1],  // AND status = 1
    function($query) {   // AND (age >= 18 OR vip_level > 0)
        $query->whereOr([
            ['age', '>=', 18],
            ['vip_level', '>', 0]
        ]);
    }
]);

// 日期范围查询
User::setCondition([
    ['create_time', 'between', ['2023-01-01', '2023-12-31']],
    ['update_time', '>', date('Y-m-d')]
]);

// IN 查询
User::setCondition([
    ['status', 'in', [1, 2, 3]],
    ['type', 'not in', ['deleted', 'banned']]
]);

// NULL 值查询
User::setCondition([
    ['delete_time', 'null'],
    ['parent_id', 'not null']
]);

// 复杂的组合查询示例
User::setCondition([
    ['status', '=', 1],
    function($query) use ($params) {
        $query->where(function($q) use ($params) {
            $q->whereOr([
                ['username', 'LIKE', "%{$params['keyword']}%"],
                ['nickname', 'LIKE', "%{$params['keyword']}%"]
            ]);
        })->where('age', '>=', 18);
    }
]);

// 子查询条件
User::setCondition([
    ['department_id', 'in', function($query) {
        $query->name('department')->where('status', 1)->field('id');
    }]
]);

// JSON 字段查询
User::setCondition([
    ['extra->gender', '=', 'male'],
    ['settings->notifications->email', '=', 1]
]);
```

### 6. 排序高级用法

```php
// 组合多个排序规则
User::setCondition([
    'is_vip' => 'desc',
    'points' => 'desc',
    'id' => 'asc'
], 'order');

// 使用 SQL 表达式排序
User::setCondition([
    'FIELD(status,1,2,3,0)' => '',  // 自定义字段顺序
    'rand()' => ''  // 随机排序
], 'order');
```

### 7. 字段选择高级用法

```php
// 字段别名
User::setCondition([
    'id',
    'username',
    'create_time as join_time',
    'CONCAT(first_name, " ", last_name) as full_name'
], 'field');

// 使用函数
User::setCondition([
    'id',
    'username',
    'FROM_UNIXTIME(create_time, "%Y-%m-%d") as date'
], 'field');
```

## 完整示例

```php
// 组合使用多种条件
User::setCondition(['status' => 1]); // WHERE 条件
User::setCondition(['create_time' => 'desc'], 'order'); // 排序条件
User::setCondition(['id', 'username', 'email'], 'field'); // 字段选择
User::setCondition([0, 10], 'limit'); // LIMIT 限制

// 执行查询
$users = User::select();
```

## 注意事项

1. 同一类型的条件可以多次调用 setCondition，新的条件会追加到已有条件中
2. 可以使用 clearConditions() 方法清空已设置的条件
3. 条件会影响后续所有的查询,直到被清空或重置
4. WHERE 条件支持多种格式:
   - 键值对格式: `['field' => 'value']`
   - 三元数组: `['field', 'operator', 'value']`
   - 闭包: `function($query) { ... }`

## 相关方法

- `clearConditions(?string $type = null)`: 清空指定类型或所有查询条件
- `applyConditions()`: 应用设置的查询条件
