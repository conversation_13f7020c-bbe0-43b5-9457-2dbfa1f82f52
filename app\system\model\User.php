<?php
declare(strict_types = 1);

namespace app\system\model;

use think\db\Query;
use think\model\relation\BelongsToMany;

/**
 * 用户模型
 * <AUTHOR> <<EMAIL>>
 * @package app\system\model
 */
class User extends BaseModel
{
    protected $name = 'system_user';

    /**
     * 关联角色
     * @return \think\model\relation\BelongsToMany
     */
    public function roles(): BelongsToMany
    {
        return $this->belongsToMany(Role::class, RoleUser::class, 'role_id', 'user_id');
    }

    /**
     * 关联部门
     * @return \think\model\relation\BelongsToMany
     */
    public function departments(): BelongsToMany
    {
        return $this->belongsToMany(Department::class, DepartmentUser::class, 'dept_id', 'user_id');
    }

    /**
     * 关联权限
     * @return \think\model\relation\BelongsToMany
     */
    public function permissions(): BelongsToMany
    {
        return $this->belongsToMany(Permission::class, PermissionRole::class, 'permission_id', 'role_id', 'roles.id');
    }

    /**
     * 关联用户组
     * @return \think\model\relation\BelongsToMany
     */
    public function groups(): BelongsToMany
    {
        return $this->belongsToMany(UserGroup::class, UserGroup::class, 'group_id', 'user_id');
    }

    /**
     * 查询作用域：启用状态的用户
     * @param Query $query
     * @return \think\db\Query
     */
    public function scopeEnabled(Query $query): Query
    {
        $query->where('status', 1);
    }

    /**
     * 获取器：上次登录时间
     * @param $value
     * @return string
     */
    public function getLastAtAttr($value): string
    {
        return $value ? date('Y-m-d H:i:s', $value) : '';
    }

    /**
     * 获取器：上次登录IP
     * @param $value
     * @return string
     */
    public function getLastIpAttr($value): string
    {
        return $value ? long2ip((int)$value) : '';
    }
}
