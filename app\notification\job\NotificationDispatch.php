<?php
declare(strict_types = 1);

namespace app\notification\job;

use app\notification\repository\NotificationRepository;
use think\queue\Job;

/**
 * 消息通知分发任务
 * 相关服务：
 * 启动消息通知分发任务：php think queue:work --queue notification
 * 后面需要将{同步计数到数据库（syncCountsToDB）做成任务放到计划任务或者消息队列中}
 * <AUTHOR> <<EMAIL>>
 * @package app\notification\job
 */
class NotificationDispatch
{
    /**
     * @var NotificationRepository
     */
    protected NotificationRepository $notificationRepository;

    public function __construct()
    {
        $this->notificationRepository = new NotificationRepository();
    }

    /**
     * 执行任务
     * @param \think\queue\Job $job
     * @param array $data
     * @return void
     */
    public function fire(Job $job, array $data): void
    {
        try {
            $this->notificationRepository->dispatchNotification($data);
            $job->delete();
        } catch (\Exception $e) {
            $job->release();
        }
    }
}
