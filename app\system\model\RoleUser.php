<?php
declare(strict_types = 1);

namespace app\system\model;

use think\model\relation\BelongsTo;

/**
 * 用户角色关联模型
 * <AUTHOR> <<EMAIL>>
 * @package app\system\model
 */
class RoleUser extends BaseModel
{
    protected $name = 'system_role_user';

    protected $field = [];

    /**
     * 获取关联的用户信息
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    /**
     * 获取关联的角色信息
     * @return BelongsTo
     */
    public function role(): BelongsTo
    {
        return $this->belongsTo(Role::class, 'role_id', 'id');
    }
}
