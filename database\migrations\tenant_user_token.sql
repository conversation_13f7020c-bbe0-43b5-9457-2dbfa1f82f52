-- 表结构: tenant_user_token
CREATE TABLE `tenant_user_token` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户ID',
  `user_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '用户ID',
  `token` varchar(500) NOT NULL DEFAULT '' COMMENT '用户token',
  `refresh_token` varchar(500) NOT NULL DEFAULT '' COMMENT '刷新token',
  `device` varchar(200) NOT NULL DEFAULT '' COMMENT '设备信息',
  `device_id` varchar(100) NOT NULL DEFAULT '' COMMENT '设备ID',
  `expire_time` int(10) NOT NULL DEFAULT '0' COMMENT '过期时间',
  `create_time` int(10) NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user` (`user_id`) USING BTREE,
  KEY `idx_token` (`token`(191)) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='租户用户Token表';
