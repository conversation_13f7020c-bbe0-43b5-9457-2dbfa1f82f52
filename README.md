合力云项目后端服务
===============

> 运行环境推荐PHP8.1+，基于thinkphp8.x前后端分离架构，开箱即用
> 后端仓库： https://gitee.com/teng315/helioCloud-backend.git
> 运维后台前端仓库： https://gitee.com/teng315/helioCloud-devops-frontend.git
> 客户后台前端仓库： https://gitee.com/teng315/helioCloud-client-frontend.git
> 客户桌面端（electron）仓库： https://gitee.com/teng315/helioCloud-desktop-client.git

## 简介
~~~~
* 后端采用 thinkphp8 + mysql8 + redis 等技术
* 完善的权限认证：菜单、按钮、api、登录授权
* 日志：登录日志、操作日志、系统日志
* thinkphp8.x 使用手册（https://doc.thinkphp.cn/v8_0/preface.html）
* SCUI Admin 使用手册 (https://lolicode.gitee.io/scui-doc/)


* 基于redis消息队列，启动队列：php think queue:listen --queue=owns_tp_queue
* 基于workerman定时任务( * * * * * )，启动定时任务：php think worker:server

## 运行项目
``` sh
# 克隆项目
git clone https://gitee.com/teng315/helioCloud-backend.git

# 创建数据库并把databases.sql导入数据库中

# 进入项目目录
cd helioCloud-backend

# 安装依赖
composer update

# 启动项目(开发模式)
php think run
```
启动完成后浏览器访问 http://localhost:8000

## 扩展依赖

需要安装redis、swoole扩展

需要删除proc_open、putenv、proc_get_status函数

## 特别鸣谢

* 感谢 [thinkphp] (http://thinkphp.cn)
* 感谢 [SCUI Admin] (https://gitee.com/lolicode/scui)
