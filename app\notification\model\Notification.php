<?php
declare(strict_types = 1);

namespace app\notification\model;

/**
 * 消息通知模型
 * <AUTHOR> <<EMAIL>>
 * @package app\notification\model
 */
class Notification extends BaseModel
{
    protected $name = 'notification';

    // 默认租户ID
    public const DEFAULT_TENANT_ID = 0;

    // 消息类型
    public const TYPE_SYSTEM = 1;  // 系统消息

    public const TYPE_NOTICE = 2;  // 公告通知

    public const TYPE_MESSAGE = 3;  // 私信

    // 接收者类型
    public const RECEIVE_ALL = 1;        // 全部用户

    public const RECEIVE_DEPARTMENT = 2;  // 指定部门

    public const RECEIVE_USER = 3;        // 指定用户

    /**
     * 关联计数器
     * @return \think\model\relation\HasOne
     */
    public function counter()
    {
        return $this->hasOne(NotificationCounter::class, 'notification_id', 'id');
    }

    /**
     * 关联队列
     * @return \think\model\relation\HasOne
     */
    public function queue()
    {
        return $this->hasOne(NotificationQueue::class, 'notification_id', 'id');
    }

    /**
     * 获取阅读状态表名
     * @param int $notificationId
     * @return string
     */
    private function getReadTableName(int $notificationId): string
    {
        $tableIndex = floor($notificationId / 1000);

        return 'notification_reads_' . $tableIndex;
    }

    /**
     * 获取互动表名
     * @param int $notificationId
     * @return string
     */
    private function getInteractionTableName(int $notificationId): string
    {
        $tableIndex = floor($notificationId / 1000);

        return 'notification_interactions_' . $tableIndex;
    }
}
