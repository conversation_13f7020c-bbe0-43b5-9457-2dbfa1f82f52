<?php
namespace app\system\repository;

use app\system\model\Department as DepartmentModel;
use owns\library\NodeTree;

/**
 * 部门数据仓储类
 * 负责系统部门相关的数据访问和操作，提供部门信息的CRUD功能
 * 主要职责：
 * 1. 管理部门基础信息的增删改查
 * 2. 提供部门数据的搜索功能
 * 3. 维护部门状态管理
 * 4. 处理部门相关的数据验证
 * <AUTHOR> <<EMAIL>>
 * @package app\system\repository
 */
class DepartmentRepository extends BaseRepository
{
    /**
     * 构造方法
     */
    public function __construct()
    {
        // 指定当前模型类
        parent::__construct(DepartmentModel::class);
    }

    /**
     * 获取部门列表
     * @param array $params 查询参数
     * @param int $pageNo 页码
     * @param int $pageSize 每页数量
     * @param bool $treeMode 是否返回树形结构
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return array
     */
    public function getList(array $params, int $pageNo = 1, int $pageSize = 20, bool $treeMode = true): array
    {
        // 生成缓存键，基于查询参数
        $cacheKey = 'list:' . md5(json_encode($params) . ":{$pageNo}:{$pageSize}:{$treeMode}");

        // 使用缓存特性获取数据
        return $this->remember($cacheKey, function() use ($params, $pageNo, $pageSize, $treeMode) {
            // 设置基础查询条件
            $this->setCondition([
                ['status', '>=', 0], // 基础状态筛选
            ]);

            // 名称筛选
            if (!empty($params['name'])) {
                $this->setCondition([
                    ['name', 'LIKE', '%' . trim($params['name']) . '%'],
                ]);
            }

            // 上级部门筛选
            if (isset($params['pid'])) {
                $this->setCondition([
                    ['pid', '=', intval($params['pid'])],
                ]);
            }

            // 状态筛选
            if (isset($params['status'])) {
                $this->setCondition([
                    ['status', '=', intval($params['status'])],
                ]);
            }

            // 关键字搜索
            if (!empty($params['keyword'])) {
                $keyword = trim($params['keyword']);
                $this->setCondition([
                    function($query) use ($keyword) {
                        $query->whereOr([
                            ['name', 'LIKE', "%{$keyword}%"],
                            ['description', 'LIKE', "%{$keyword}%"],
                        ]);
                    },
                ]);
            }

            // 获取分页数据
            $result = $this->pageList($params, $pageNo, $pageSize);

            // 获取列表数据
            $list = $result['list']->toArray();

            // 处理部门路径
            $nodeTree = new NodeTree([
                'id'    => 'id',
                'pid'   => 'pid',
                'title' => 'name',
                'child' => 'children',
            ]);

            foreach ($list as &$item) {
                $item['depts'] = $nodeTree->getParentIds($list, $item['id'], true, false);
            }

            // 转换为树形结构
            if ($treeMode && empty($params['keyword'])) {
                $list = $nodeTree->toLayer($list);
            }

            return [
                'total'    => $result['total'],
                'list'     => $list,
                'page'     => $pageNo,
                'pageSize' => $pageSize,
            ];
        });
    }

    /**
     * 获取有效部门列表（用于发送消息）
     */
    public function getActiveDepartments(): array
    {
        // 使用缓存特性获取数据，缓存时间设置为30分钟
        return $this->remember('active_departments', function() {
            return $this->model->field('id, name, pid')
                ->where('status', 1)
                ->select()
                ->toArray();
        }, 1800); // 缓存30分钟
    }

    /**
     * 处理部门数据
     * @param array $params 原始参数
     * @param bool $isUpdate 是否为更新操作
     * @return array
     */
    protected function processDepartmentData(array $params, bool $isUpdate = false): array
    {
        $data = [
            'pid'         => isset($params['pid']) ? intval($params['pid']) : 0,
            'name'        => isset($params['name']) ? trim($params['name']) : '',
            'description' => isset($params['description']) ? trim($params['description']) : '',
            'sort'        => isset($params['sort']) ? intval($params['sort']) : 0,
            'status'      => isset($params['status']) ? intval($params['status']) : 1,
        ];

        if ($isUpdate) {
            $data['update_at'] = time();
        } else {
            $data['create_at'] = time();
        }

        return $data;
    }

    /**
     * 添加部门
     * @param array $params 部门数据
     * @return bool
     */
    public function addDepartment(array $params): bool
    {
        // 处理部门数据
        $data   = $this->processDepartmentData($params);
        $result = $this->model->save($data);

        // 如果添加成功，清除相关缓存
        if ($result) {
            $this->clearCache();
        }

        return $result;
    }

    /**
     * 更新部门
     * @param int $id 部门ID
     * @param array $params 更新数据
     * @throws \Exception
     * @return bool
     */
    public function updateDepartment(int $id, array $params): bool
    {
        // 开启事务
        $this->model->startTrans();

        try {
            // 处理部门数据
            $data   = $this->processDepartmentData($params, true);
            $result = $this->model->where('id', $id)->update($data) != false;

            // 如果更新成功，清除相关缓存
            if ($result) {
                $this->clearCache();
            }

            // 提交事务
            $this->model->commit();

            return $result;
        } catch (\Exception $e) {
            // 回滚事务
            $this->model->rollback();

            throw $e;
        }
    }

    /**
     * 设置部门状态（支持批量）
     * @param mixed $ids
     * @param int $status
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return bool
     */
    public function setStatus(mixed $ids, int $status): bool
    {
        // 开启事务
        $this->model->startTrans();

        try {
            // 如果要禁用或者删除部门单位时，遍历子部门单位都需要设置相同的状态
            if ($status <= 0) {
                // 获取指定id的子数据集，包括当前
                $ids = $this->getChildIds($ids);
            }

            $result = (bool)$this->model->update(['status' => $status, 'update_at' => time()], [['id', 'IN', $ids], ['is_system', '=', 0], ['status', '>=', 0]]);

            // 如果状态更新成功，清除相关缓存
            if ($result) {
                $this->clearCache();
            }

            // 提交事务
            $this->model->commit();

            return $result;
        } catch (\Exception $e) {
            // 回滚事务
            $this->model->rollback();

            throw $e;
        }
    }

    /**
     * 获取指定id的子数据集，包括当前
     * @param array $ids
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return array
     */
    private function getChildIds(array $ids = []): array
    {
        // 生成缓存键，基于ID数组
        $cacheKey = 'child_ids:' . md5(json_encode($ids));

        // 使用缓存特性获取数据
        return $this->remember($cacheKey, function() use ($ids) {
            $retIds = $ids;

            $list = $this->model->where([['is_system', '=', 0], ['status', '>=', 0]])->field(['id','pid','name'])->select()->toArray();

            $nodeTree = new NodeTree([
                'id'    => 'id', // id名称
                'pid'   => 'pid', // pid名称
                'title' => 'name', // 标题名称
                'child' => 'children', // 子元素键名
            ]);

            if (!empty($list)) {
                foreach ($ids as $id) {
                    $child_ids_arr = $nodeTree->getChildIds($list, $id);
                    $retIds        = array_merge($retIds, $child_ids_arr);
                }
            }

            $array_map = array_map('intval', array_unique($retIds));

            sort($array_map);

            return $array_map;
        });
    }
}
