<?php
namespace app\system\repository;

use app\system\model\ConfigureGroup as ConfigureGroupModel;

/**
 * 配置组数据仓库类
 * 负责系统配置组的数据访问和操作
 * <AUTHOR> <<EMAIL>>
 * @package app\system\repository
 */
class ConfigureGroupRepository extends BaseRepository
{
    /**
     * 构造方法
     */
    public function __construct()
    {
        // 指定当前模型类
        parent::__construct(ConfigureGroupModel::class);
    }

    /**
     * 添加配置分组
     * @param array $data
     * @return int
     */
    public function addGroup(array $data): int
    {
        if ($res = $this->model->create($data)) {
            return $res->id;
        }

        return 0;
    }

    /**
     * 更新配置分组
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function editGroup(int $id, array $data): bool
    {
        return (bool)$this->model->update($data, ['id' => $id]);
    }

    /**
     * 设置配置分组隐藏状态
     * @param int|array $id 配置分组ID或ID数组
     * @param int $hidden 隐藏状态：0显示，1隐藏
     * @return bool
     */
    public function setHidden(int|array $id, int $hidden): bool
    {
        // 开启事务
        $this->model->startTrans();

        try {
            $result = $this->model->where('id', is_array($id) ? 'in' : '=', $id)
                ->update(['hidden' => $hidden, 'update_at' => time()]);

            // 提交事务
            $this->model->commit();

            return (bool)$result;
        } catch (\Exception $e) {
            // 回滚事务
            $this->model->rollback();

            return false;
        }
    }
}
