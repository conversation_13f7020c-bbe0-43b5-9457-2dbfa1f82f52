-- 表结构: notification_counter
CREATE TABLE `notification_counter` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `notification_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '消息ID',
  `like_count` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '点赞数',
  `dislike_count` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '踩数',
  `read_count` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '已读数',
  `create_at` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_at` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_notification` (`notification_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='消息计数器表';
