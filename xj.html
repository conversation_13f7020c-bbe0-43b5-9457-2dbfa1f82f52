<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1,minimum-scale=1,user-scalable=no">
    <title>开放平台</title>
    <link rel="stylesheet" href="https://am.didistatic.com/static/am/amonline_fe/fde-deployer-scmpf/8.0.0/normalize.min.css">
    <link href="https://am.didistatic.com/static/am/amoperation-fe/Midway/midway-energy-open/midway-base-open-energy/0.0.1-rc.f3b3e69/index.chunk.css" rel="stylesheet">
</head>
<body>
<div class="doc-detail-wrap">
    <div class="doc-detail-left-wrap">
        <div class="doc-detail-path">文档中心 / 加油渠道API+H5 / 能力文档 / 基础能力<span> / 服务端文档</span>
        </div>
        <div class="doc-detail-content-wrap">
            <h1 id="1、接入准备">1、接入准备</h1>
            <ol>
                <li>本文主要介绍如何快速接入通过API+H5接入方式获取小桔加油能力。详细介绍 <a href="https://open-energy.xiaojukeji.com/home/<USER>/22">加油API接入方案</a>
                </li>
                <li>请首先仔细阅读<a href="https://open-energy.xiaojukeji.com/home/<USER>/22/152">接入必读</a> ，确定已创建正确的加油应用，并且可以获取到应用的测试密钥。</li>
                <li>
                    <a href="https://open-energy.xiaojukeji.com/home/<USER>">管理中心</a>页面上，提供了基本的接入信息，对照文档，理解他们的作用。
                </li>
            </ol>
            <h3 id="1.1 名词解释">1.1 名词解释</h3>
            <pre><code>  1. A类接口：是小桔提供的接口。
2. B类接口：需要合作方实现的接口。
3. appId：合作方的唯一标识，小桔会用于区分合作方。又称为appKey，两者是同样的。
4. appKey：合作方的唯一标识，小桔会用于区分合作方。又称为appId，两者是同样的。
</code></pre>
            <h1 id="2、服务端开发推荐步骤">2、服务端开发推荐步骤</h1>
            <ol>
                <li>根据接口列表，逐一测试：</li>
            </ol>
            <ul>
                <li>A类接口，是小桔提供的接口，接入方可以使用分配的 appKey 及秘钥，使用联调环境地址进行测试。</li>
                <li>B类接口，需要接入方按照文档要求予以实现，小桔会在特点场景下回调该类接口。请将服务的域名补充到"管理中心/应用信息"页面上的 回调域名 字段（注意，domain与 <strong>notifyOrderInfo</strong> 拼接后，应该是完整的接口调用地址）</li>
            </ul>
            <ol start="2">
                <li>
                    <p>接口调试阶段，可以使用<a href="https://open-energy.xiaojukeji.com/home/<USER>">管理中心/应用信息</a> 页面提供的功能。建议搭配SDK和加油沙箱共同联调，将会提升联调效率50%以上。</p>
                </li>
                <li>
                    <p>联调完成且<strong>验收通过</strong>后， 即可在 <a href="https://open-energy.xiaojukeji.com/home/<USER>">管理中心/应用信息</a> 查看线上环境密钥信息，<strong>接入方将密钥信息替换为正式密钥，小桔测试域名替换为线上域名</strong>，补充正式环境的回调地址后即可完成上线操作。</p>
                </li>
            </ol>
            <h1 id="3、系统交互流程">3、系统交互流程</h1>
            <h3 id="3.1 用户交易阶段接口交互图">3.1 用户交易阶段接口交互图</h3>
            <ol>
                <li>接入方可按需选择性组合使用（建议参考"用户交易阶段接口交互图"，该方式是大多数渠道采用的方式。</li>
                <li>每个接口数据非特殊标记，字段均为必填。</li>
                <li>文档请求均为 POST 类型接口，请求使用 application/json,utf-8 编码，请求返回皆为 json 对象。</li>
            </ol>
            <p>
                <img src="https://view.didistatic.com/static/dcms/egbk8xb5lakvwyln_1270x2488.png" alt="iShot_2022-11-17_17.37.03.png">
            </p>
            <h3 id="3.2 小桔详情页登录状态">3.2 小桔详情页登录状态</h3>
            <ol>
                <li>油站详情页小桔H5维护Ticket，来进行是否显示登录。只要Ticket未失效并合法在一定时间内就不需要登录。</li>
            </ol>
            <ul>
                <li>
                    <p>如ticket已过期或没有ticket，则进行passport登录，登录完成后保存ticket（有效期3个月）至本地，进入正常业务流程。</p>
                </li>
                <li>
                    <p>如存在有效ticket，则用当前ticket获取新的ticket进行替换，即刷新ticket有效期，保存新的ticket至本地，进入正常业务流程。</p>
                </li>
            </ul>
            <h3 id="3.3 退款流程">3.3 退款流程</h3>
            <ol>
                <li>用户完成支付后如果支付错油站、支付了未加油、支付错误金额等原因需要退款，用户可致电客服进行退款。</li>
                <li>小桔客服会和用户和油站确认，确认成功后即会发起退款。订单退款完成后即会通过notifyOrderInfo通知渠道订单状态。</li>
            </ol>
            <h1 id="4、数据加密、验签、token">4、数据加密、验签、token</h1>
            <ol>
                <li>账号秘钥隔离，每一家对接的平台都由小桔统一分配唯一的账号(接入方标识，即：appKey，也即：appId)、平台秘钥(appSecret)、数据秘钥(dataSecret)、签名秘钥(sigSecret)。合作方可在"应用管理"页面查看。</li>
                <li>为了数据交互的安全，每一次请求都需要经过加密、加签、token。建议采用我司SDK，不需要关心加密等步骤了，详见<a href="https://open-energy.xiaojukeji.com/home/<USER>/20/168">SDK使用说明</a>。</li>
            </ol>
            <pre><code>最终的请求为(因为时间戳的问题，该curl命令不可执行需要合作方自行拼接参数)：
curl --location --request POST 'https://gw.am.xiaojukeji.com/online/energy/v1/queryStorePrice' \
--header 'Content-Type: application/json' \
--header 'Accept: application/json' \
--header 'Authorization: Bearer b6356753edd4451c84d65d5a71077fdd' \
--data-raw '{
"timeStamp": "20221024173703",
"sig": "FFB8E1D2DBCE55653B2EF998438C7BFF",
"appKey": "xiongtest",
"data": "Uu+odc976f+vlm7BX+Wj7ABVNLir5JH7GBX2dyLfs3Gb8Q1h/KdZXNgRbDTvaI1dWT7n5fPzRLJpL82f6Bo80RV0KqCX2NwBVPtt4mODCgZpJkCGy0v2D89QclkOxrEjzmIBloDbutILF4b/Dln3luqYpsvM7DeO5jiLUh5ik4GCwdvUB9SQKza4z6AhD0UKWVlA19lXF912VS0hRGC4unzfVFDz9j93fYTdE6QDKzneazlicERkbt2Qkjvp9LfsTBhXRB/ItiYkhAdZAH6eO1zdT2wJDluuatQchdH+d5gHMXOU3JjVv1/jISjWs+SY"
}'
</code></pre>
            <ul>
                <li>TOKEN：需要放在HTTP的Header里面，详见 "7.4 Token"</li>
                <li>加密：针对请求的业务参数、响应的业务数据， json 化后，使用AES 128位算法加解密，加密模式采用CBC，填充模式PKCS5Padding，得到加密后的 data 字段。详见 "7.1 加密"</li>
                <li>时间戳：防止重放攻击，<strong>请求参数</strong>必须传递时间戳，小桔收到请求时校验时间戳与当前时间，时间差超过一定的范围被认为是非法请求，时间戳格式"<strong>yyyyMMddHHmmss</strong>"。。详见 "7.2 时间戳"</li>
                <li>验签：对请求、响应需按照约定的顺序拼接起来，采用HMac-MD5不可逆算法加小桔分配的签名秘钥进行签名计算，请求时将签名值带上，收到请求后小桔会按相同逻辑进行签名校验，参数签名要求大写。详见 "7.3 验签"</li>
            </ul>
            <h1 id="5、接口列表（A.小桔实现）">5、接口列表（A.小桔实现）</h1>
            <table>
                <thead>
                <tr>
                    <th style="text-align: left;">接口名称</th>
                    <th style="text-align: left;">Path</th>
                    <th style="text-align: left;">入参字段 (所有参数都必填 )</th>
                    <th style="text-align: left;">出参核心字段</th>
                    <th style="text-align: left;">备注</th>
                </tr>
                </thead>
                <tbody>
                <tr>
                    <td style="text-align: left;">Token 查询</td>
                    <td style="text-align: left;">/energy/v1/queryToken</td>
                    <td style="text-align: left;">平台密钥</td>
                    <td style="text-align: left;">Token、 有效期</td>
                    <td style="text-align: left;">1. 有效期单位：秒 2. 返回的 token 在有效期内可以缓存复用 3. 每次请求，服务端都会刷新 token，调用方在token 失效时，要加锁控制调用并发度</td>
                </tr>
                <tr>
                    <td style="text-align: left;">油站列表查询</td>
                    <td style="text-align: left;">/energy/v1/queryStoreList</td>
                    <td style="text-align: left;">页码、页数</td>
                    <td style="text-align: left;">油站主键、 油站名称、 油站logo、 城市、 省份、 详细地址、 经度、 纬度、 门店优惠标签、 油品列表、 挂牌价、 发改委价， 品牌名称</td>
                    <td style="text-align: left;">1. 该接口返回的数据，接入方可以缓存到本地数据库，每天调用一次同步数据即可 2. 分页接口，请求完成标志：用已经获取的条数，与响应参数里的 totalSize 比较 3. 相邻两次分页请求，时间间隔控制在 150 秒<strong>以内</strong>，尽量<strong>连续请求</strong>，不要停顿 4. 接口有限流设定，每天同步一次数据即可 5. 该接口不提供过滤、排序功能，接入方拉取全量数据后，自己实现相应的功能</td>
                </tr>
                <tr>
                    <td style="text-align: left;">油站实时价格批量查询</td>
                    <td style="text-align: left;">/energy/v1/queryStorePrice</td>
                    <td style="text-align: left;">用户实时经度、 用户实时纬度、 用户手机号 、 开放渠道 (小桔提供)、 油品名称、 油站 id 列表</td>
                    <td style="text-align: left;">油站id、 发改委价、 油品名称、 挂牌价格、 小桔折扣价格</td>
                    <td style="text-align: left;">1. 开放渠道，默认设置为 1 即可 2. 入参油站id 列表，最大数量 20 3. 返回的小桔折扣价格是用户粒度的价格，即：不同用户看到的折扣价格是不一样的 4. 接口有限流设置，5 QPS 5. 接口使用建议：由于存在 QPS 限制，接入方需要采用”<strong>最小化</strong>“调用原则，只针对给用户<strong>展示</strong>的油站查询折扣价格</td>
                </tr>
                <tr>
                    <td style="text-align: left;">订单查询</td>
                    <td style="text-align: left;">/energy/v1/queryOrderInfo</td>
                    <td style="text-align: left;">订单号</td>
                    <td style="text-align: left;">接入方平台UserId、 订单号、 油站主键 、 油站名称、 城市、 枪号、 油品、 加油升数、 支付金额(实付金额) 、 订单金额(总金额) 、 支付状态、 支付时间、 退款状态、 退款时间</td>
                    <td style="text-align: left;">1. 接口只返回已支付、已退款的订单信息</td>
                </tr>
                <tr>
                    <td style="text-align: left;">跳转地址查询</td>
                    <td style="text-align: left;">/energy/v1/queryEnergyUrl</td>
                    <td style="text-align: left;">用户实时经度、 用户实时维度、 开放渠道（小桔提供）、 用户手机号、 接入方平台UserId、 油站主键、 油品名称</td>
                    <td style="text-align: left;">油站详情链接地址</td>
                    <td style="text-align: left;">1. 开放渠道，默认设置为 1 即可</td>
                </tr>
                <tr>
                    <td style="text-align: left;">订单批量查询</td>
                    <td style="text-align: left;">/energy/v1/queryOrderList</td>
                    <td style="text-align: left;">开始时间(支付)、 结束时间(支付)、 页码、 页数</td>
                    <td style="text-align: left;">第三方平台UserId、 订单号、 油站主键 、 油站名称、 城市、 枪号、 油品、 加油升数、 支付金额(实付金额) 、 订单金额(总金额) 、 支付状态、 支付时间、 退款状态、 退款时间</td>
                    <td style="text-align: left;">1. 只返回已支付、退款的订单； 2. 入参时间参数含义：支付时间 3. 参数限制：7天内订单，间隔24小时以内，分页最大100</td>
                </tr>
                </tbody>
            </table>
            <h3 id="A1. 查询 Token">A1. 查询 Token</h3>
            <ul>
                <li>
                    <p>接口说明：获取token，其余接口进行调用时都需要带上token来防止CSRF攻击。</p>
                </li>
                <li>
                    <p>调用关系：</p>
                    <ul>
                        <li>接口提供方：小桔</li>
                        <li>接口调用方：合作方</li>
                    </ul>
                </li>
                <li>
                    <p>接口 path: /energy/v1/queryToken。</p>
                </li>
                <li>
                    <p>请求类型：POST。</p>
                </li>
                <li>
                    <p>请求参数字段说明</p>
                    <table>
                        <thead>
                        <tr>
                            <th style="text-align: left;">参数名称</th>
                            <th style="text-align: left;">字段说明</th>
                            <th style="text-align: left;">类型</th>
                            <th style="text-align: left;">父级</th>
                            <th style="text-align: left;">备注</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td style="text-align: left;">appSecret</td>
                            <td style="text-align: left;">秘钥</td>
                            <td style="text-align: left;">string</td>
                            <td style="text-align: left;">/</td>
                            <td style="text-align: left;">小桔分配的平台秘钥</td>
                        </tr>
                        </tbody>
                    </table>
                </li>
                <li>
                    <p>请求参数示例</p>
                </li>
            </ul>
            <pre><code>  {
 "appSecret": "HlqAnnonTUxDr51AynjlNYzdt+OYXaqceMI70CskDis=",
}
</code></pre>
            <ul>
                <li>
                    <p>响应内容字段说明</p>
                    <table>
                        <thead>
                        <tr>
                            <th style="text-align: left;">字段名称</th>
                            <th style="text-align: left;">字段说明</th>
                            <th style="text-align: left;">类型</th>
                            <th style="text-align: left;">父级</th>
                            <th style="text-align: left;">备注</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td style="text-align: left;">accessToken</td>
                            <td style="text-align: left;">token</td>
                            <td style="text-align: left;">string</td>
                            <td style="text-align: left;">/</td>
                            <td style="text-align: left;">有效期内可复用</td>
                        </tr>
                        <tr>
                            <td style="text-align: left;">availableTime</td>
                            <td style="text-align: left;">有效期</td>
                            <td style="text-align: left;">Int</td>
                            <td style="text-align: left;">/</td>
                            <td style="text-align: left;">单位，秒</td>
                        </tr>
                        </tbody>
                    </table>
                </li>
                <li>
                    <p>响应内容示例</p>
                </li>
            </ul>
            <pre><code>  {
 "accessToken": "1d79ec49f7604ed589821403ca08e21f",
 "availableTime": 7200
}
</code></pre>
            <ul>
                <li>
                    <p>备注：</p>
                    <ul>
                        <li>返回的 token 有效期内可以复用。 一般是2小时过期，并且每一次创建token都会返回失效时间。在临近token失效前1分钟的queryToken才会创建新的token。</li>
                    </ul>
                </li>
            </ul>
            <h3 id="A2. 油站列表查询">A2. 油站列表查询</h3>
            <ul>
                <li>
                    <p>接口说明：获取token，其余接口进行调用时都需要带上token来防止CSRF攻击。</p>
                </li>
                <li>
                    <p>调用关系：</p>
                    <ul>
                        <li>接口提供方：小桔</li>
                        <li>接口调用方：合作方</li>
                    </ul>
                </li>
                <li>
                    <p>接口 path: /energy/v1/queryStoreList</p>
                </li>
                <li>
                    <p>请求类型：POST。</p>
                </li>
                <li>
                    <p>请求参数字段说明</p>
                    <table>
                        <thead>
                        <tr>
                            <th style="text-align: left;">参数名称</th>
                            <th style="text-align: left;">字段说明</th>
                            <th style="text-align: left;">类型</th>
                            <th style="text-align: left;">父级</th>
                            <th style="text-align: left;">备注</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td style="text-align: left;">pageIndex</td>
                            <td style="text-align: left;">页码</td>
                            <td style="text-align: left;">Integer</td>
                            <td style="text-align: left;">/</td>
                            <td style="text-align: left;">页码从1开始</td>
                        </tr>
                        <tr>
                            <td style="text-align: left;">pageSize</td>
                            <td style="text-align: left;">单页条数</td>
                            <td style="text-align: left;">Integer</td>
                            <td style="text-align: left;">/</td>
                            <td style="text-align: left;">单页上限100条</td>
                        </tr>
                        </tbody>
                    </table>
                </li>
                <li>
                    <p>请求参数示例</p>
                </li>
            </ul>
            <pre><code>  {
 "pageIndex": 1, //页码从1开始
 "pageSize": 5 //单页上限100条
}
</code></pre>
            <ul>
                <li>
                    <p>响应内容字段说明</p>
                    <table>
                        <thead>
                        <tr>
                            <th style="text-align: left;">字段名称</th>
                            <th style="text-align: left;">字段说明</th>
                            <th style="text-align: left;">类型</th>
                            <th style="text-align: left;">父级</th>
                            <th style="text-align: left;">备注</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td style="text-align: left;">pageIndex</td>
                            <td style="text-align: left;">页码</td>
                            <td style="text-align: left;">Integer</td>
                            <td style="text-align: left;">/</td>
                            <td style="text-align: left;"></td>
                        </tr>
                        <tr>
                            <td style="text-align: left;">pageSize</td>
                            <td style="text-align: left;">每页最大数量</td>
                            <td style="text-align: left;">Integer</td>
                            <td style="text-align: left;">/</td>
                            <td style="text-align: left;"></td>
                        </tr>
                        <tr>
                            <td style="text-align: left;">totalSize</td>
                            <td style="text-align: left;">所有数量</td>
                            <td style="text-align: left;">Integer</td>
                            <td style="text-align: left;">/</td>
                            <td style="text-align: left;"></td>
                        </tr>
                        <tr>
                            <td style="text-align: left;">storeInfoList</td>
                            <td style="text-align: left;">门店列表</td>
                            <td style="text-align: left;">Object</td>
                            <td style="text-align: left;">/</td>
                            <td style="text-align: left;"></td>
                        </tr>
                        <tr>
                            <td style="text-align: left;">storeId</td>
                            <td style="text-align: left;">门店id</td>
                            <td style="text-align: left;">String</td>
                            <td style="text-align: left;">storeInfoList</td>
                            <td style="text-align: left;">油站ID</td>
                        </tr>
                        <tr>
                            <td style="text-align: left;">storeName</td>
                            <td style="text-align: left;">门店名称</td>
                            <td style="text-align: left;">String</td>
                            <td style="text-align: left;">storeInfoList</td>
                            <td style="text-align: left;"></td>
                        </tr>
                        <tr>
                            <td style="text-align: left;">storeLogo</td>
                            <td style="text-align: left;">门店logo</td>
                            <td style="text-align: left;">String</td>
                            <td style="text-align: left;">storeInfoList</td>
                            <td style="text-align: left;"></td>
                        </tr>
                        <tr>
                            <td style="text-align: left;">address</td>
                            <td style="text-align: left;">地址</td>
                            <td style="text-align: left;">String</td>
                            <td style="text-align: left;">storeInfoList</td>
                            <td style="text-align: left;"></td>
                        </tr>
                        <tr>
                            <td style="text-align: left;">lat</td>
                            <td style="text-align: left;">纬度</td>
                            <td style="text-align: left;">Float</td>
                            <td style="text-align: left;">storeInfoList</td>
                            <td style="text-align: left;">火星坐标系</td>
                        </tr>
                        <tr>
                            <td style="text-align: left;">lon</td>
                            <td style="text-align: left;">经度</td>
                            <td style="text-align: left;">Flaot</td>
                            <td style="text-align: left;">storeInfoList</td>
                            <td style="text-align: left;">火星坐标系</td>
                        </tr>
                        <tr>
                            <td style="text-align: left;">brandName</td>
                            <td style="text-align: left;">品牌名称</td>
                            <td style="text-align: left;">String</td>
                            <td style="text-align: left;">storeInfoList</td>
                            <td style="text-align: left;"></td>
                        </tr>
                        <tr>
                            <td style="text-align: left;">itemInfoList</td>
                            <td style="text-align: left;">油品信息</td>
                            <td style="text-align: left;">Object</td>
                            <td style="text-align: left;">storeInfoList</td>
                            <td style="text-align: left;"></td>
                        </tr>
                        <tr>
                            <td style="text-align: left;">huiLabels</td>
                            <td style="text-align: left;">优惠标签</td>
                            <td style="text-align: left;">String</td>
                            <td style="text-align: left;">storeInfoList</td>
                            <td style="text-align: left;">壳牌净擎汽油，清洁积碳，一路畅行</td>
                        </tr>
                        <tr>
                            <td style="text-align: left;">invoiceManner</td>
                            <td style="text-align: left;">开票方式</td>
                            <td style="text-align: left;">Integer</td>
                            <td style="text-align: left;">storeInfoList</td>
                            <td style="text-align: left;">1-油站开票、2-合作方开票、3-不可开票</td>
                        </tr>
                        <tr>
                            <td style="text-align: left;">itemId</td>
                            <td style="text-align: left;">油品id</td>
                            <td style="text-align: left;">String</td>
                            <td style="text-align: left;">itemInfoList</td>
                            <td style="text-align: left;"></td>
                        </tr>
                        <tr>
                            <td style="text-align: left;">itemName</td>
                            <td style="text-align: left;">油品名称</td>
                            <td style="text-align: left;">String</td>
                            <td style="text-align: left;">itemInfoList</td>
                            <td style="text-align: left;"></td>
                        </tr>
                        <tr>
                            <td style="text-align: left;">cityPricetel</td>
                            <td style="text-align: left;">国标价、发改委价油站电话</td>
                            <td style="text-align: left;">IntString</td>
                            <td style="text-align: left;">itemInfoList</td>
                            <td style="text-align: left;">单位分</td>
                        </tr>
                        <tr>
                            <td style="text-align: left;">storePrice</td>
                            <td style="text-align: left;">门店价、油枪价、油机价</td>
                            <td style="text-align: left;">Int</td>
                            <td style="text-align: left;">itemInfoList</td>
                            <td style="text-align: left;">单位分</td>
                        </tr>
                        </tbody>
                    </table>
                </li>
                <li>
                    <p>响应内容示例</p>
                </li>
            </ul>
            <pre><code>  {
  "pageIndex": 1,
  "pageSize": 5,
  "totalSize": 98,
  "storeInfoList": [
      {
          "storeId": "5131588228085193415",
          "storeName": "合同优化门店1",
          "storeLogo": "http://***********:8052/static/anything/b57f62fc-e845-4c2a-9bfa-28bd4d76a845.png",
          "cityName": "杭州市",
          "provinceName": "浙江省",
          "address": "西溪水岸花苑",
          "lon": 120.04969,
          "lat": 30.28198,
          "brandName": "中国石化",
          "itemInfoList": [
              {
                  "itemName": "95#",
                  "cityPrice": 790,
                  "storePrice": 780
              }
          ],
          "huiLabels": [
              "壳牌净擎汽油，清洁积碳，一路畅行"
          ]
      },
      {
          "storeId": "5131634411818195846",
          "storeName": "专用门店测试",
          "storeLogo": "http://***********:8052/static/anything/b57f62fc-e845-4c2a-9bfa-28bd4d76a845.png",
          "cityName": "杭州市",
          "provinceName": "浙江省",
          "address": "余杭区九橙创投中心",
          "brandName": "中国石油",
          "lon": 116.290276,
          "lat": 40.043476,
          "itemInfoList": [
              {
                  "itemName": "92#",
                  "cityPrice": 690,
                  "storePrice": 680
              }
          ],
          "huiLabels": [
              "加油送免费洗车、免费加玻璃水服务"
          ]
      }
  ]
}
</code></pre>
            <ul>
                <li>备注：
                    - 该接口仅支持单线程的分页查询，不可并发请求。
                    &ZeroWidthSpace; - 请根据实际返回storeInfoList的size &lt; 入参pageSize来判断是否查询完所有油站。
                </li>
            </ul>
            <h3 id="A3. 油站实时价格批量查询">A3. 油站实时价格批量查询</h3>
            <ul>
                <li>
                    <p>接口说明：批量油站实时价格批量查询</p>
                </li>
                <li>
                    <p>调用关系：</p>
                    <ul>
                        <li>接口提供方：小桔</li>
                        <li>接口调用方：合作方</li>
                    </ul>
                </li>
                <li>
                    <p>接口 path: /energy/v1/queryStorePrice</p>
                </li>
                <li>
                    <p>请求类型：POST。</p>
                </li>
                <li>
                    <p>请求参数字段说明</p>
                    <table>
                        <thead>
                        <tr>
                            <th style="text-align: left;">参数名称</th>
                            <th style="text-align: left;">字段说明</th>
                            <th style="text-align: left;">类型</th>
                            <th style="text-align: left;">父级</th>
                            <th style="text-align: left;">备注</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td style="text-align: left;">lat</td>
                            <td style="text-align: left;">纬度</td>
                            <td style="text-align: left;">Float</td>
                            <td style="text-align: left;">/</td>
                            <td style="text-align: left;">火星坐标系</td>
                        </tr>
                        <tr>
                            <td style="text-align: left;">lon</td>
                            <td style="text-align: left;">经度</td>
                            <td style="text-align: left;">Flaot</td>
                            <td style="text-align: left;">/</td>
                            <td style="text-align: left;">火星坐标系</td>
                        </tr>
                        <tr>
                            <td style="text-align: left;">mobile</td>
                            <td style="text-align: left;">用户手机号</td>
                            <td style="text-align: left;">String</td>
                            <td style="text-align: left;">/</td>
                            <td style="text-align: left;"></td>
                        </tr>
                        <tr>
                            <td style="text-align: left;">openChannel</td>
                            <td style="text-align: left;">openChannel</td>
                            <td style="text-align: left;">Integer</td>
                            <td style="text-align: left;">/</td>
                            <td style="text-align: left;">固定为1</td>
                        </tr>
                        <tr>
                            <td style="text-align: left;">itemName</td>
                            <td style="text-align: left;">油品名称</td>
                            <td style="text-align: left;">String</td>
                            <td style="text-align: left;">/</td>
                            <td style="text-align: left;">92#</td>
                        </tr>
                        <tr>
                            <td style="text-align: left;">storeIdList</td>
                            <td style="text-align: left;">油站Id列表</td>
                            <td style="text-align: left;">String[]</td>
                            <td style="text-align: left;">/</td>
                            <td style="text-align: left;">多个油站Id，单次最多20个</td>
                        </tr>
                        </tbody>
                    </table>
                </li>
                <li>
                    <p>请求参数示例</p>
                </li>
            </ul>
            <pre><div style="color: black; background: rgb(245, 242, 240); text-shadow: white 0px 1px; font-family: Consolas, Monaco, &quot;Andale Mono&quot;, &quot;Ubuntu Mono&quot;, monospace; font-size: 1em; text-align: left; white-space: pre; word-spacing: normal; word-break: normal; overflow-wrap: normal; line-height: 1.5; tab-size: 4; hyphens: none; padding: 1em; margin: 0.5em 0px; overflow: auto;"><code class="language-json" style="white-space: pre; color: black; background: none; text-shadow: white 0px 1px; font-family: Consolas, Monaco, &quot;Andale Mono&quot;, &quot;Ubuntu Mono&quot;, monospace; font-size: 1em; text-align: left; word-spacing: normal; word-break: normal; overflow-wrap: normal; line-height: 1.5; tab-size: 4; hyphens: none;"><span>  </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span>
</span><span>     </span><span class="token" style="color: rgb(153, 0, 85);">"lon"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">116.290276</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span>
</span><span>     </span><span class="token" style="color: rgb(153, 0, 85);">"lat"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">40.043476</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span>
</span><span>     </span><span class="token" style="color: rgb(153, 0, 85);">"mobile"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"13728655788"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span>
</span><span>     </span><span class="token" style="color: rgb(153, 0, 85);">"openChannel"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">1</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span>
</span><span>     </span><span class="token" style="color: rgb(153, 0, 85);">"itemName"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span class="token" style="color: rgb(102, 153, 0);">"92#"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span>
</span><span>     </span><span class="token" style="color: rgb(153, 0, 85);">"storeIdList"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">[</span><span>
</span><span>       </span><span class="token" style="color: rgb(102, 153, 0);">"5131634411818195846"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span>
</span><span>       </span><span class="token" style="color: rgb(102, 153, 0);">"5131588228085193415"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span>
</span><span>     </span><span class="token" style="color: rgb(153, 153, 153);">]</span><span>
</span><span>  </span><span class="token" style="color: rgb(153, 153, 153);">}</span></code></div></pre>
            <ul>
                <li>
                    <p>响应内容字段说明</p>
                    <table>
                        <thead>
                        <tr>
                            <th style="text-align: left;">字段名称</th>
                            <th style="text-align: left;">字段说明</th>
                            <th style="text-align: left;">类型</th>
                            <th style="text-align: left;">父级</th>
                            <th style="text-align: left;">备注</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td style="text-align: left;">itemInfoList</td>
                            <td style="text-align: left;">商品列表</td>
                            <td style="text-align: left;">Object</td>
                            <td style="text-align: left;">/</td>
                            <td style="text-align: left;"></td>
                        </tr>
                        <tr>
                            <td style="text-align: left;">storeId</td>
                            <td style="text-align: left;">门店id</td>
                            <td style="text-align: left;">String</td>
                            <td style="text-align: left;">storeInfoList</td>
                            <td style="text-align: left;">油站ID</td>
                        </tr>
                        <tr>
                            <td style="text-align: left;">storeName</td>
                            <td style="text-align: left;">门店名称</td>
                            <td style="text-align: left;">String</td>
                            <td style="text-align: left;">storeInfoList</td>
                            <td style="text-align: left;"></td>
                        </tr>
                        <tr>
                            <td style="text-align: left;">storePrice</td>
                            <td style="text-align: left;">门店价、油枪价、油机价</td>
                            <td style="text-align: left;">Int</td>
                            <td style="text-align: left;">itemInfoList</td>
                            <td style="text-align: left;">单位分</td>
                        </tr>
                        <tr>
                            <td style="text-align: left;">vipPrice</td>
                            <td style="text-align: left;">小桔价</td>
                            <td style="text-align: left;">Int</td>
                            <td style="text-align: left;">itemInfoList</td>
                            <td style="text-align: left;">单位分</td>
                        </tr>
                        <tr>
                            <td style="text-align: left;">cityPrice</td>
                            <td style="text-align: left;">发改委价</td>
                            <td style="text-align: left;">Int</td>
                            <td style="text-align: left;">itemInfoList</td>
                            <td style="text-align: left;">单位分</td>
                        </tr>
                        </tbody>
                    </table>
                </li>
                <li>
                    <p>响应内容示例</p>
                </li>
            </ul>
            <pre><div style="color: black; background: rgb(245, 242, 240); text-shadow: white 0px 1px; font-family: Consolas, Monaco, &quot;Andale Mono&quot;, &quot;Ubuntu Mono&quot;, monospace; font-size: 1em; text-align: left; white-space: pre; word-spacing: normal; word-break: normal; overflow-wrap: normal; line-height: 1.5; tab-size: 4; hyphens: none; padding: 1em; margin: 0.5em 0px; overflow: auto;"><code class="language-json" style="white-space: pre; color: black; background: none; text-shadow: white 0px 1px; font-family: Consolas, Monaco, &quot;Andale Mono&quot;, &quot;Ubuntu Mono&quot;, monospace; font-size: 1em; text-align: left; word-spacing: normal; word-break: normal; overflow-wrap: normal; line-height: 1.5; tab-size: 4; hyphens: none;"><span>  </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span>
</span><span>     </span><span class="token" style="color: rgb(153, 0, 85);">"itemInfoList"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">[</span><span>
</span><span>      </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span>
</span><span>         </span><span class="token" style="color: rgb(153, 0, 85);">"storeId"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"5131634411818195846"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span>
</span><span>         </span><span class="token" style="color: rgb(153, 0, 85);">"itemName"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"92#"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span>
</span><span>         </span><span class="token" style="color: rgb(153, 0, 85);">"storePrice"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">680</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: slategray;">//单位是分</span><span>
</span><span>         </span><span class="token" style="color: rgb(153, 0, 85);">"vipPrice"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">580</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: slategray;">//单位是分</span><span>
</span><span>        </span><span class="token" style="color: rgb(153, 0, 85);">"cityPrice"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">690</span><span> </span><span class="token" style="color: slategray;">//发改委价，单位是分</span><span>
</span><span>      </span><span class="token" style="color: rgb(153, 153, 153);">}</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span>
</span><span>      </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span>
</span><span>         </span><span class="token" style="color: rgb(153, 0, 85);">"storeId"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"5131588228085193415"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span>
</span><span>         </span><span class="token" style="color: rgb(153, 0, 85);">"itemName"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"92#"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span>
</span><span>         </span><span class="token" style="color: rgb(153, 0, 85);">"storePrice"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">680</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: slategray;">//挂牌价，单位是分</span><span>
</span><span>         </span><span class="token" style="color: rgb(153, 0, 85);">"vipPrice"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">580</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: slategray;">//小桔价，单位是分</span><span>
</span><span>         </span><span class="token" style="color: rgb(153, 0, 85);">"cityPrice"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">690</span><span> </span><span class="token" style="color: slategray;">//发改委价，单位是分</span><span>
</span><span>      </span><span class="token" style="color: rgb(153, 153, 153);">}</span><span>
</span><span>    </span><span class="token" style="color: rgb(153, 153, 153);">]</span><span>
</span><span>  </span><span class="token" style="color: rgb(153, 153, 153);">}</span></code></div></pre>
            <ul>
                <li>备注</li>
            </ul>
            <h3 id="A4.  跳转地址查询">A4. 跳转地址查询</h3>
            <ul>
                <li>
                    <p>接口说明：油站详情页跳转地址</p>
                </li>
                <li>
                    <p>调用关系：</p>
                    <ul>
                        <li>接口提供方：小桔</li>
                        <li>接口调用方：合作方</li>
                    </ul>
                </li>
                <li>
                    <p>接口 path: /energy/v1/queryEnergyUrl</p>
                </li>
                <li>
                    <p>请求类型：POST</p>
                </li>
                <li>
                    <p>请求参数字段说明</p>
                    <table>
                        <thead>
                        <tr>
                            <th style="text-align: left;">参数名称</th>
                            <th style="text-align: left;">字段说明</th>
                            <th style="text-align: left;">类型</th>
                            <th style="text-align: left;">父级</th>
                            <th style="text-align: left;">备注</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td style="text-align: left;">lat</td>
                            <td style="text-align: left;">纬度</td>
                            <td style="text-align: left;">Float</td>
                            <td style="text-align: left;">/</td>
                            <td style="text-align: left;">火星坐标系</td>
                        </tr>
                        <tr>
                            <td style="text-align: left;">lon</td>
                            <td style="text-align: left;">经度</td>
                            <td style="text-align: left;">Flaot</td>
                            <td style="text-align: left;">/</td>
                            <td style="text-align: left;">火星坐标系</td>
                        </tr>
                        <tr>
                            <td style="text-align: left;">mobile</td>
                            <td style="text-align: left;">用户手机号</td>
                            <td style="text-align: left;">String</td>
                            <td style="text-align: left;">/</td>
                            <td style="text-align: left;"></td>
                        </tr>
                        <tr>
                            <td style="text-align: left;">outUserId</td>
                            <td style="text-align: left;">外部用户Id</td>
                            <td style="text-align: left;">String</td>
                            <td style="text-align: left;">/</td>
                            <td style="text-align: left;"></td>
                        </tr>
                        <tr>
                            <td style="text-align: left;">openChannel</td>
                            <td style="text-align: left;">openChannel</td>
                            <td style="text-align: left;">Integer</td>
                            <td style="text-align: left;">/</td>
                            <td style="text-align: left;">固定为1</td>
                        </tr>
                        <tr>
                            <td style="text-align: left;">itemName</td>
                            <td style="text-align: left;">油品名称</td>
                            <td style="text-align: left;">String</td>
                            <td style="text-align: left;">/</td>
                            <td style="text-align: left;">92# ,进入详情页用户也可自行修改油品</td>
                        </tr>
                        <tr>
                            <td style="text-align: left;">storeId</td>
                            <td style="text-align: left;">油站Id</td>
                            <td style="text-align: left;">String</td>
                            <td style="text-align: left;">/</td>
                            <td style="text-align: left;">油站Id</td>
                        </tr>
                        </tbody>
                    </table>
                </li>
                <li>
                    <p>请求参数示例</p>
                </li>
            </ul>
            <pre><div style="color: black; background: rgb(245, 242, 240); text-shadow: white 0px 1px; font-family: Consolas, Monaco, &quot;Andale Mono&quot;, &quot;Ubuntu Mono&quot;, monospace; font-size: 1em; text-align: left; white-space: pre; word-spacing: normal; word-break: normal; overflow-wrap: normal; line-height: 1.5; tab-size: 4; hyphens: none; padding: 1em; margin: 0.5em 0px; overflow: auto;"><code class="language-json" style="white-space: pre; color: black; background: none; text-shadow: white 0px 1px; font-family: Consolas, Monaco, &quot;Andale Mono&quot;, &quot;Ubuntu Mono&quot;, monospace; font-size: 1em; text-align: left; word-spacing: normal; word-break: normal; overflow-wrap: normal; line-height: 1.5; tab-size: 4; hyphens: none;"><span>  </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span>
</span><span>     </span><span class="token" style="color: rgb(153, 0, 85);">"lon"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">116.290276</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span>
</span><span>     </span><span class="token" style="color: rgb(153, 0, 85);">"lat"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">40.043476</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span>
</span><span>     </span><span class="token" style="color: rgb(153, 0, 85);">"openChannel"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">1</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span class="token" style="color: slategray;">//小桔指定</span><span>
</span><span>     </span><span class="token" style="color: rgb(153, 0, 85);">"mobile"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"13728655788"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span>
</span><span>     </span><span class="token" style="color: rgb(153, 0, 85);">"outUserId"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span class="token" style="color: rgb(102, 153, 0);">"out123456789"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: slategray;">//接入方平台UserId</span><span>
</span><span>     </span><span class="token" style="color: rgb(153, 0, 85);">"storeId"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"5105493875147414159"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span>
</span><span>     </span><span class="token" style="color: rgb(153, 0, 85);">"itemName"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span class="token" style="color: rgb(102, 153, 0);">"92#"</span><span>
</span><span>  </span><span class="token" style="color: rgb(153, 153, 153);">}</span></code></div></pre>
            <ul>
                <li>
                    <p>响应内容字段说明</p>
                    <table>
                        <thead>
                        <tr>
                            <th style="text-align: left;">字段名称</th>
                            <th style="text-align: left;">字段说明</th>
                            <th style="text-align: left;">类型</th>
                            <th style="text-align: left;">父级</th>
                            <th style="text-align: left;">备注</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td style="text-align: left;">link</td>
                            <td style="text-align: left;">该油站详情页</td>
                            <td style="text-align: left;">String</td>
                            <td style="text-align: left;">/</td>
                            <td style="text-align: left;"></td>
                        </tr>
                        </tbody>
                    </table>
                </li>
                <li>
                    <p>响应内容示例</p>
                </li>
            </ul>
            <pre><div style="color: black; background: rgb(245, 242, 240); text-shadow: white 0px 1px; font-family: Consolas, Monaco, &quot;Andale Mono&quot;, &quot;Ubuntu Mono&quot;, monospace; font-size: 1em; text-align: left; white-space: pre; word-spacing: normal; word-break: normal; overflow-wrap: normal; line-height: 1.5; tab-size: 4; hyphens: none; padding: 1em; margin: 0.5em 0px; overflow: auto;"><code class="language-json" style="white-space: pre; color: black; background: none; text-shadow: white 0px 1px; font-family: Consolas, Monaco, &quot;Andale Mono&quot;, &quot;Ubuntu Mono&quot;, monospace; font-size: 1em; text-align: left; word-spacing: normal; word-break: normal; overflow-wrap: normal; line-height: 1.5; tab-size: 4; hyphens: none;"><span>  </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span>
</span><span>     </span><span class="token" style="color: rgb(153, 0, 85);">"link"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"https://static.am.xiaojukeji.com/cf-terminal/oil/thanos-fe-oil/pages/gas-detail/index.html?storeId=5105493875147414159&amp;oauthCode=26ceg246682y01"</span><span>
</span><span>  </span><span class="token" style="color: rgb(153, 153, 153);">}</span></code></div></pre>
            <ul>
                <li>备注</li>
            </ul>
            <h3 id="A5. 订单批量查询">A5. 订单批量查询</h3>
            <ul>
                <li>
                    <p>接口说明：油站详情页跳转地址</p>
                </li>
                <li>
                    <p>调用关系：</p>
                    <ul>
                        <li>接口提供方：小桔</li>
                        <li>接口调用方：合作方</li>
                    </ul>
                </li>
                <li>
                    <p>接口 path：/energy/v1/queryOrderList</p>
                </li>
                <li>
                    <p>请求类型：POST</p>
                </li>
                <li>
                    <p>请求参数字段说明</p>
                    <table>
                        <thead>
                        <tr>
                            <th style="text-align: left;">参数名称</th>
                            <th style="text-align: left;">字段说明</th>
                            <th style="text-align: left;">类型</th>
                            <th style="text-align: left;">父级</th>
                            <th style="text-align: left;">备注</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td style="text-align: left;">pageIndex</td>
                            <td style="text-align: left;">页码</td>
                            <td style="text-align: left;">Float</td>
                            <td style="text-align: left;">/</td>
                            <td style="text-align: left;">火星坐标系</td>
                        </tr>
                        <tr>
                            <td style="text-align: left;">pageSize</td>
                            <td style="text-align: left;">每页最大数量</td>
                            <td style="text-align: left;">Flaot</td>
                            <td style="text-align: left;">/</td>
                            <td style="text-align: left;">火星坐标系</td>
                        </tr>
                        <tr>
                            <td style="text-align: left;">queryStartTime</td>
                            <td style="text-align: left;">订单创建开启时间</td>
                            <td style="text-align: left;">String</td>
                            <td style="text-align: left;">/</td>
                            <td style="text-align: left;">"2019-12-11 07:03:50"</td>
                        </tr>
                        <tr>
                            <td style="text-align: left;">queryEndTime</td>
                            <td style="text-align: left;">订单创建结束时间</td>
                            <td style="text-align: left;">String</td>
                            <td style="text-align: left;">/</td>
                            <td style="text-align: left;">"2019-12-12 07:03:50",//时间间隔上限24小时</td>
                        </tr>
                        </tbody>
                    </table>
                </li>
                <li>
                    <p>请求参数示例</p>
                </li>
            </ul>
            <pre><div style="color: black; background: rgb(245, 242, 240); text-shadow: white 0px 1px; font-family: Consolas, Monaco, &quot;Andale Mono&quot;, &quot;Ubuntu Mono&quot;, monospace; font-size: 1em; text-align: left; white-space: pre; word-spacing: normal; word-break: normal; overflow-wrap: normal; line-height: 1.5; tab-size: 4; hyphens: none; padding: 1em; margin: 0.5em 0px; overflow: auto;"><code class="language-json" style="white-space: pre; color: black; background: none; text-shadow: white 0px 1px; font-family: Consolas, Monaco, &quot;Andale Mono&quot;, &quot;Ubuntu Mono&quot;, monospace; font-size: 1em; text-align: left; word-spacing: normal; word-break: normal; overflow-wrap: normal; line-height: 1.5; tab-size: 4; hyphens: none;"><span>  </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span>
</span><span>      </span><span class="token" style="color: rgb(153, 0, 85);">"pageIndex"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">1</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span class="token" style="color: slategray;">//从1开始</span><span>
</span><span>      </span><span class="token" style="color: rgb(153, 0, 85);">"pageSize"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">10</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span class="token" style="color: slategray;">//单次查询上限100条</span><span>
</span><span>      </span><span class="token" style="color: rgb(153, 0, 85);">"queryEndTime"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"2019-12-12 07:03:50"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span class="token" style="color: slategray;">//时间间隔上限24小时</span><span>
</span><span>      </span><span class="token" style="color: rgb(153, 0, 85);">"queryStartTime"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"2019-12-11 07:03:50"</span><span>
</span><span>  </span><span class="token" style="color: rgb(153, 153, 153);">}</span></code></div></pre>
            <ul>
                <li>
                    <p>响应内容字段说明</p>
                    <table>
                        <thead>
                        <tr>
                            <th style="text-align: left;">字段名称</th>
                            <th style="text-align: left;">字段说明</th>
                            <th style="text-align: left;">类型</th>
                            <th style="text-align: left;">父级</th>
                            <th style="text-align: left;">备注</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td style="text-align: left;">pageIndex</td>
                            <td style="text-align: left;">页码</td>
                            <td style="text-align: left;">Integer</td>
                            <td style="text-align: left;">/</td>
                            <td style="text-align: left;">从1开始</td>
                        </tr>
                        <tr>
                            <td style="text-align: left;">pageSize</td>
                            <td style="text-align: left;">分页大小</td>
                            <td style="text-align: left;">Integer</td>
                            <td style="text-align: left;">/</td>
                            <td style="text-align: left;">单次查询上限100条</td>
                        </tr>
                        <tr>
                            <td style="text-align: left;">totalSize</td>
                            <td style="text-align: left;">所有数量</td>
                            <td style="text-align: left;">Integer</td>
                            <td style="text-align: left;">/</td>
                            <td style="text-align: left;">所有订单数量</td>
                        </tr>
                        <tr>
                            <td style="text-align: left;">orderInfoList</td>
                            <td style="text-align: left;">订单详情</td>
                            <td style="text-align: left;">Object[]</td>
                            <td style="text-align: left;">/</td>
                            <td style="text-align: left;"></td>
                        </tr>
                        <tr>
                            <td style="text-align: left;">outUserId</td>
                            <td style="text-align: left;">外部用户id</td>
                            <td style="text-align: left;">String</td>
                            <td style="text-align: left;">orderInfoList</td>
                            <td style="text-align: left;">接入方平台UserId</td>
                        </tr>
                        <tr>
                            <td style="text-align: left;">orderId</td>
                            <td style="text-align: left;">订单Id</td>
                            <td style="text-align: left;">String</td>
                            <td style="text-align: left;">orderInfoList</td>
                            <td style="text-align: left;">小桔订单号</td>
                        </tr>
                        <tr>
                            <td style="text-align: left;">storeId</td>
                            <td style="text-align: left;">门店Id</td>
                            <td style="text-align: left;">String</td>
                            <td style="text-align: left;">orderInfoList</td>
                            <td style="text-align: left;"></td>
                        </tr>
                        <tr>
                            <td style="text-align: left;">storeName</td>
                            <td style="text-align: left;">门店名称</td>
                            <td style="text-align: left;">String</td>
                            <td style="text-align: left;">orderInfoList</td>
                            <td style="text-align: left;"></td>
                        </tr>
                        <tr>
                            <td style="text-align: left;">cityName</td>
                            <td style="text-align: left;">城市名称</td>
                            <td style="text-align: left;">String</td>
                            <td style="text-align: left;">orderInfoList</td>
                            <td style="text-align: left;"></td>
                        </tr>
                        <tr>
                            <td style="text-align: left;">gunNo</td>
                            <td style="text-align: left;">枪号</td>
                            <td style="text-align: left;">String</td>
                            <td style="text-align: left;">orderInfoList</td>
                            <td style="text-align: left;"></td>
                        </tr>
                        <tr>
                            <td style="text-align: left;">itemName</td>
                            <td style="text-align: left;">商品名称</td>
                            <td style="text-align: left;">String</td>
                            <td style="text-align: left;">orderInfoList</td>
                            <td style="text-align: left;">95#</td>
                        </tr>
                        <tr>
                            <td style="text-align: left;">quantity</td>
                            <td style="text-align: left;">加油容量</td>
                            <td style="text-align: left;">Int</td>
                            <td style="text-align: left;">orderInfoList</td>
                            <td style="text-align: left;">单位升</td>
                        </tr>
                        <tr>
                            <td style="text-align: left;">totalMoney</td>
                            <td style="text-align: left;">订单金额</td>
                            <td style="text-align: left;">Int</td>
                            <td style="text-align: left;">orderInfoList</td>
                            <td style="text-align: left;">单位分</td>
                        </tr>
                        <tr>
                            <td style="text-align: left;">realMoney</td>
                            <td style="text-align: left;">实付金额</td>
                            <td style="text-align: left;">Int</td>
                            <td style="text-align: left;">orderInfoList</td>
                            <td style="text-align: left;">单位分</td>
                        </tr>
                        <tr>
                            <td style="text-align: left;">payStatus</td>
                            <td style="text-align: left;">订单状态</td>
                            <td style="text-align: left;">Int</td>
                            <td style="text-align: left;">orderInfoList</td>
                            <td style="text-align: left;">0未支付 1已支付</td>
                        </tr>
                        <tr>
                            <td style="text-align: left;">payTime</td>
                            <td style="text-align: left;">支付时间</td>
                            <td style="text-align: left;">String</td>
                            <td style="text-align: left;">orderInfoList</td>
                            <td style="text-align: left;"></td>
                        </tr>
                        <tr>
                            <td style="text-align: left;">refundStatus</td>
                            <td style="text-align: left;">退款状态</td>
                            <td style="text-align: left;">Int</td>
                            <td style="text-align: left;">orderInfoList</td>
                            <td style="text-align: left;">0未退款 1已退款</td>
                        </tr>
                        <tr>
                            <td style="text-align: left;">refundTime</td>
                            <td style="text-align: left;">退款时间</td>
                            <td style="text-align: left;">String</td>
                            <td style="text-align: left;">orderInfoList</td>
                            <td style="text-align: left;">未退款时，无退款时间</td>
                        </tr>
                        </tbody>
                    </table>
                </li>
                <li>
                    <p>响应内容示例</p>
                </li>
            </ul>
            <pre><div style="color: black; background: rgb(245, 242, 240); text-shadow: white 0px 1px; font-family: Consolas, Monaco, &quot;Andale Mono&quot;, &quot;Ubuntu Mono&quot;, monospace; font-size: 1em; text-align: left; white-space: pre; word-spacing: normal; word-break: normal; overflow-wrap: normal; line-height: 1.5; tab-size: 4; hyphens: none; padding: 1em; margin: 0.5em 0px; overflow: auto;"><code class="language-json" style="white-space: pre; color: black; background: none; text-shadow: white 0px 1px; font-family: Consolas, Monaco, &quot;Andale Mono&quot;, &quot;Ubuntu Mono&quot;, monospace; font-size: 1em; text-align: left; word-spacing: normal; word-break: normal; overflow-wrap: normal; line-height: 1.5; tab-size: 4; hyphens: none;"><span>   </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span>
</span><span>      </span><span class="token" style="color: rgb(153, 0, 85);">"orderInfoList"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">[</span><span class="token" style="color: rgb(153, 153, 153);">{</span><span>
</span><span>          </span><span class="token" style="color: rgb(153, 0, 85);">"outUserId"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"out123456789"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span class="token" style="color: slategray;">//</span><span>
</span><span>          </span><span class="token" style="color: rgb(153, 0, 85);">"orderId"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"3467771714635066313"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span>
</span><span>          </span><span class="token" style="color: rgb(153, 0, 85);">"storeId"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"5105493875147414159"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span>
</span><span>          </span><span class="token" style="color: rgb(153, 0, 85);">"storeName"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"自动化测试门店4"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span>
</span><span>          </span><span class="token" style="color: rgb(153, 0, 85);">"cityName"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"杭州市"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span>
</span><span>          </span><span class="token" style="color: rgb(153, 0, 85);">"gunNo"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"2"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span class="token" style="color: slategray;">//枪号</span><span>
</span><span>          </span><span class="token" style="color: rgb(153, 0, 85);">"itemName"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"92#"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span>
</span><span>          </span><span class="token" style="color: rgb(153, 0, 85);">"quantity"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">1.2</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span class="token" style="color: slategray;">//商品升数</span><span>
</span><span>          </span><span class="token" style="color: rgb(153, 0, 85);">"totalMoney"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">800</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span class="token" style="color: slategray;">//订单金额、单位是分</span><span>
</span><span>          </span><span class="token" style="color: rgb(153, 0, 85);">"realMoney"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">800</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span class="token" style="color: slategray;">//实付金额、单位是分</span><span>
</span><span>          </span><span class="token" style="color: rgb(153, 0, 85);">"payStatus"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">1</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span class="token" style="color: slategray;">//0未支付 1已支付</span><span>
</span><span>          </span><span class="token" style="color: rgb(153, 0, 85);">"payTime"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"2019-11-28 07:03:50"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span>
</span><span>          </span><span class="token" style="color: rgb(153, 0, 85);">"refundStatus"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">0</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span class="token" style="color: slategray;">//0未退款 1已退款</span><span>
</span><span>          </span><span class="token" style="color: rgb(153, 0, 85);">"refundTime"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token null" style="color: rgb(0, 119, 170);">null</span><span> </span><span class="token" style="color: slategray;">//未退款、无退款时间</span><span>
</span><span>       </span><span class="token" style="color: rgb(153, 153, 153);">}</span><span>
</span><span>     </span><span class="token" style="color: rgb(153, 153, 153);">]</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span>
</span><span>      </span><span class="token" style="color: rgb(153, 0, 85);">"pageIndex"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">1</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span>
</span><span>      </span><span class="token" style="color: rgb(153, 0, 85);">"pageSize"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">10</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span>
</span><span>      </span><span class="token" style="color: rgb(153, 0, 85);">"totalSize"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">155</span><span>
</span><span>   </span><span class="token" style="color: rgb(153, 153, 153);">}</span></code></div></pre>
            <ul>
                <li>备注</li>
            </ul>
            <h3 id="A6. 订单查询">A6. 订单查询</h3>
            <ul>
                <li>
                    <p>接口说明：油站详情页跳转地址</p>
                </li>
                <li>
                    <p>调用关系：</p>
                    <ul>
                        <li>接口提供方：小桔</li>
                        <li>接口调用方：合作方</li>
                    </ul>
                </li>
                <li>
                    <p>接口 path：/energy/v1/queryOrderList</p>
                </li>
                <li>
                    <p>请求类型：POST</p>
                </li>
                <li>
                    <p>请求参数字段说明</p>
                    <table>
                        <thead>
                        <tr>
                            <th style="text-align: left;">参数名称</th>
                            <th style="text-align: left;">字段说明</th>
                            <th style="text-align: left;">类型</th>
                            <th style="text-align: left;">父级</th>
                            <th style="text-align: left;">备注</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td style="text-align: left;">orderId</td>
                            <td style="text-align: left;">订单Id</td>
                            <td style="text-align: left;">String</td>
                            <td style="text-align: left;">orderInfoList</td>
                            <td style="text-align: left;">小桔订单号</td>
                        </tr>
                        </tbody>
                    </table>
                </li>
                <li>
                    <p>请求参数示例</p>
                </li>
            </ul>
            <pre><div style="color: black; background: rgb(245, 242, 240); text-shadow: white 0px 1px; font-family: Consolas, Monaco, &quot;Andale Mono&quot;, &quot;Ubuntu Mono&quot;, monospace; font-size: 1em; text-align: left; white-space: pre; word-spacing: normal; word-break: normal; overflow-wrap: normal; line-height: 1.5; tab-size: 4; hyphens: none; padding: 1em; margin: 0.5em 0px; overflow: auto;"><code class="language-json" style="white-space: pre; color: black; background: none; text-shadow: white 0px 1px; font-family: Consolas, Monaco, &quot;Andale Mono&quot;, &quot;Ubuntu Mono&quot;, monospace; font-size: 1em; text-align: left; word-spacing: normal; word-break: normal; overflow-wrap: normal; line-height: 1.5; tab-size: 4; hyphens: none;"><span>  </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span>
</span><span>     </span><span class="token" style="color: rgb(153, 0, 85);">"orderId"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"3467771714635066313"</span><span>
</span><span>  </span><span class="token" style="color: rgb(153, 153, 153);">}</span></code></div></pre>
            <ul>
                <li>
                    <p>响应内容字段说明</p>
                    <table>
                        <thead>
                        <tr>
                            <th style="text-align: left;">字段名称</th>
                            <th style="text-align: left;">字段说明</th>
                            <th style="text-align: left;">类型</th>
                            <th style="text-align: left;">父级</th>
                            <th style="text-align: left;">备注</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td style="text-align: left;">outUserId</td>
                            <td style="text-align: left;">外部用户id</td>
                            <td style="text-align: left;">String</td>
                            <td style="text-align: left;">orderInfoList</td>
                            <td style="text-align: left;">接入方平台UserId</td>
                        </tr>
                        <tr>
                            <td style="text-align: left;">orderId</td>
                            <td style="text-align: left;">订单Id</td>
                            <td style="text-align: left;">String</td>
                            <td style="text-align: left;">orderInfoList</td>
                            <td style="text-align: left;">小桔订单号</td>
                        </tr>
                        <tr>
                            <td style="text-align: left;">storeId</td>
                            <td style="text-align: left;">门店Id</td>
                            <td style="text-align: left;">String</td>
                            <td style="text-align: left;">orderInfoList</td>
                            <td style="text-align: left;"></td>
                        </tr>
                        <tr>
                            <td style="text-align: left;">storeName</td>
                            <td style="text-align: left;">门店名称</td>
                            <td style="text-align: left;">String</td>
                            <td style="text-align: left;">orderInfoList</td>
                            <td style="text-align: left;"></td>
                        </tr>
                        <tr>
                            <td style="text-align: left;">cityName</td>
                            <td style="text-align: left;">城市名称</td>
                            <td style="text-align: left;">String</td>
                            <td style="text-align: left;">orderInfoList</td>
                            <td style="text-align: left;"></td>
                        </tr>
                        <tr>
                            <td style="text-align: left;">gunNo</td>
                            <td style="text-align: left;">枪号</td>
                            <td style="text-align: left;">String</td>
                            <td style="text-align: left;">orderInfoList</td>
                            <td style="text-align: left;"></td>
                        </tr>
                        <tr>
                            <td style="text-align: left;">itemName</td>
                            <td style="text-align: left;">商品名称</td>
                            <td style="text-align: left;">String</td>
                            <td style="text-align: left;">orderInfoList</td>
                            <td style="text-align: left;">95#</td>
                        </tr>
                        <tr>
                            <td style="text-align: left;">quantity</td>
                            <td style="text-align: left;">加油容量</td>
                            <td style="text-align: left;">Int</td>
                            <td style="text-align: left;">orderInfoList</td>
                            <td style="text-align: left;">单位升</td>
                        </tr>
                        <tr>
                            <td style="text-align: left;">totalMoney</td>
                            <td style="text-align: left;">订单金额</td>
                            <td style="text-align: left;">Int</td>
                            <td style="text-align: left;">orderInfoList</td>
                            <td style="text-align: left;">单位分</td>
                        </tr>
                        <tr>
                            <td style="text-align: left;">realMoney</td>
                            <td style="text-align: left;">实付金额</td>
                            <td style="text-align: left;">Int</td>
                            <td style="text-align: left;">orderInfoList</td>
                            <td style="text-align: left;">单位分</td>
                        </tr>
                        <tr>
                            <td style="text-align: left;">payStatus</td>
                            <td style="text-align: left;">订单状态</td>
                            <td style="text-align: left;">Int</td>
                            <td style="text-align: left;">orderInfoList</td>
                            <td style="text-align: left;">0未支付 1已支付</td>
                        </tr>
                        <tr>
                            <td style="text-align: left;">payTime</td>
                            <td style="text-align: left;">支付时间</td>
                            <td style="text-align: left;">String</td>
                            <td style="text-align: left;">orderInfoList</td>
                            <td style="text-align: left;">"2019-11-28 07:03:50"</td>
                        </tr>
                        <tr>
                            <td style="text-align: left;">refundStatus</td>
                            <td style="text-align: left;">退款状态</td>
                            <td style="text-align: left;">Int</td>
                            <td style="text-align: left;">orderInfoList</td>
                            <td style="text-align: left;">0未退款 1已退款</td>
                        </tr>
                        <tr>
                            <td style="text-align: left;">refundTime</td>
                            <td style="text-align: left;">退款时间</td>
                            <td style="text-align: left;">String</td>
                            <td style="text-align: left;">orderInfoList</td>
                            <td style="text-align: left;">未退款时，无退款时间</td>
                        </tr>
                        </tbody>
                    </table>
                </li>
                <li>
                    <p>响应内容示例</p>
                </li>
            </ul>
            <pre><div style="color: black; background: rgb(245, 242, 240); text-shadow: white 0px 1px; font-family: Consolas, Monaco, &quot;Andale Mono&quot;, &quot;Ubuntu Mono&quot;, monospace; font-size: 1em; text-align: left; white-space: pre; word-spacing: normal; word-break: normal; overflow-wrap: normal; line-height: 1.5; tab-size: 4; hyphens: none; padding: 1em; margin: 0.5em 0px; overflow: auto;"><code class="language-json" style="white-space: pre; color: black; background: none; text-shadow: white 0px 1px; font-family: Consolas, Monaco, &quot;Andale Mono&quot;, &quot;Ubuntu Mono&quot;, monospace; font-size: 1em; text-align: left; word-spacing: normal; word-break: normal; overflow-wrap: normal; line-height: 1.5; tab-size: 4; hyphens: none;"><span>  </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span>
</span><span>     </span><span class="token" style="color: rgb(153, 0, 85);">"outUserId"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"out123456789"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span class="token" style="color: slategray;">//第三方平台UserId</span><span>
</span><span>     </span><span class="token" style="color: rgb(153, 0, 85);">"orderId"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"3467771714635066313"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span>
</span><span>     </span><span class="token" style="color: rgb(153, 0, 85);">"storeId"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"5105493875147414159"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span>
</span><span>     </span><span class="token" style="color: rgb(153, 0, 85);">"storeName"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"自动化测试门店4"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span>
</span><span>     </span><span class="token" style="color: rgb(153, 0, 85);">"cityName"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"杭州市"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span>
</span><span>     </span><span class="token" style="color: rgb(153, 0, 85);">"gunNo"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"2"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span class="token" style="color: slategray;">//枪号</span><span>
</span><span>     </span><span class="token" style="color: rgb(153, 0, 85);">"itemName"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"92#"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span>
</span><span>     </span><span class="token" style="color: rgb(153, 0, 85);">"quantity"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">1.2</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span class="token" style="color: slategray;">//商品升数</span><span>
</span><span>     </span><span class="token" style="color: rgb(153, 0, 85);">"totalMoney"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">800</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span class="token" style="color: slategray;">//订单金额、单位是分</span><span>
</span><span>     </span><span class="token" style="color: rgb(153, 0, 85);">"realMoney"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">800</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span class="token" style="color: slategray;">//实付金额、单位是分</span><span>
</span><span>     </span><span class="token" style="color: rgb(153, 0, 85);">"payStatus"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">1</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span class="token" style="color: slategray;">//0未支付 1已支付</span><span>
</span><span>     </span><span class="token" style="color: rgb(153, 0, 85);">"payTime"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"2019-11-28 07:03:50"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span>
</span><span>     </span><span class="token" style="color: rgb(153, 0, 85);">"refundStatus"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">0</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span class="token" style="color: slategray;">//0未退款 1已退款</span><span>
</span><span>     </span><span class="token" style="color: rgb(153, 0, 85);">"refundTime"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token null" style="color: rgb(0, 119, 170);">null</span><span> </span><span class="token" style="color: slategray;">//未退款、无退款时间</span><span>
</span><span>  </span><span class="token" style="color: rgb(153, 153, 153);">}</span></code></div></pre>
            <ul>
                <li>备注</li>
            </ul>
            <h1 id="6、接口列表(B. 接入方实现)">6、接口列表(B. 接入方实现)</h1>
            <p>注：以下接口描述中的请求参数、响应数据， 均<strong>只包含业务参数</strong>，不涉及公有参数，所有参数都必填。</p>
            <h3 id="B1. 订单回调通知">B1. 订单回调通知</h3>
            <ul>
                <li>
                    <p>接口说明：订单回调通知</p>
                </li>
                <li>
                    <p>调用关系：</p>
                    <ul>
                        <li>接口提供方：合作方</li>
                        <li>接口调用方：小桔</li>
                    </ul>
                </li>
                <li>
                    <p>接口 path：合作方domain + notifyOrderInfo</p>
                </li>
                <li>
                    <p>请求类型：POST</p>
                </li>
                <li>
                    <p>请求参数字段说明</p>
                </li>
            </ul>
            <table>
                <thead>
                <tr>
                    <th style="text-align: left;">字段名称</th>
                    <th style="text-align: left;">字段说明</th>
                    <th style="text-align: left;">类型</th>
                    <th style="text-align: left;">父级</th>
                    <th style="text-align: left;">备注</th>
                </tr>
                </thead>
                <tbody>
                <tr>
                    <td style="text-align: left;">outUserId</td>
                    <td style="text-align: left;">外部用户id</td>
                    <td style="text-align: left;">String</td>
                    <td style="text-align: left;">orderInfoList</td>
                    <td style="text-align: left;">接入方平台UserId</td>
                </tr>
                <tr>
                    <td style="text-align: left;">orderId</td>
                    <td style="text-align: left;">订单Id</td>
                    <td style="text-align: left;">String</td>
                    <td style="text-align: left;">orderInfoList</td>
                    <td style="text-align: left;">小桔订单号</td>
                </tr>
                <tr>
                    <td style="text-align: left;">storeId</td>
                    <td style="text-align: left;">门店Id</td>
                    <td style="text-align: left;">String</td>
                    <td style="text-align: left;">orderInfoList</td>
                    <td style="text-align: left;"></td>
                </tr>
                <tr>
                    <td style="text-align: left;">storeName</td>
                    <td style="text-align: left;">门店名称</td>
                    <td style="text-align: left;">String</td>
                    <td style="text-align: left;">orderInfoList</td>
                    <td style="text-align: left;"></td>
                </tr>
                <tr>
                    <td style="text-align: left;">cityName</td>
                    <td style="text-align: left;">城市名称</td>
                    <td style="text-align: left;">String</td>
                    <td style="text-align: left;">orderInfoList</td>
                    <td style="text-align: left;"></td>
                </tr>
                <tr>
                    <td style="text-align: left;">gunNo</td>
                    <td style="text-align: left;">枪号</td>
                    <td style="text-align: left;">String</td>
                    <td style="text-align: left;">orderInfoList</td>
                    <td style="text-align: left;"></td>
                </tr>
                <tr>
                    <td style="text-align: left;">itemName</td>
                    <td style="text-align: left;">商品名称</td>
                    <td style="text-align: left;">String</td>
                    <td style="text-align: left;">orderInfoList</td>
                    <td style="text-align: left;">95#</td>
                </tr>
                <tr>
                    <td style="text-align: left;">quantity</td>
                    <td style="text-align: left;">加油容量</td>
                    <td style="text-align: left;">Float</td>
                    <td style="text-align: left;">orderInfoList</td>
                    <td style="text-align: left;">单位升</td>
                </tr>
                <tr>
                    <td style="text-align: left;">totalMoney</td>
                    <td style="text-align: left;">订单金额</td>
                    <td style="text-align: left;">Int</td>
                    <td style="text-align: left;">orderInfoList</td>
                    <td style="text-align: left;">单位分</td>
                </tr>
                <tr>
                    <td style="text-align: left;">realMoney</td>
                    <td style="text-align: left;">实付金额</td>
                    <td style="text-align: left;">Int</td>
                    <td style="text-align: left;">orderInfoList</td>
                    <td style="text-align: left;">单位分</td>
                </tr>
                <tr>
                    <td style="text-align: left;">payStatus</td>
                    <td style="text-align: left;">订单状态</td>
                    <td style="text-align: left;">Int</td>
                    <td style="text-align: left;">orderInfoList</td>
                    <td style="text-align: left;">0未支付 1已支付</td>
                </tr>
                <tr>
                    <td style="text-align: left;">payTime</td>
                    <td style="text-align: left;">支付时间</td>
                    <td style="text-align: left;">String</td>
                    <td style="text-align: left;">orderInfoList</td>
                    <td style="text-align: left;">"2019-11-28 07:03:50"</td>
                </tr>
                <tr>
                    <td style="text-align: left;">refundStatus</td>
                    <td style="text-align: left;">退款状态</td>
                    <td style="text-align: left;">Int</td>
                    <td style="text-align: left;">orderInfoList</td>
                    <td style="text-align: left;">0未退款 1已退款</td>
                </tr>
                <tr>
                    <td style="text-align: left;">refundTime</td>
                    <td style="text-align: left;">退款时间</td>
                    <td style="text-align: left;">String</td>
                    <td style="text-align: left;">orderInfoList</td>
                    <td style="text-align: left;">未退款时，无退款时间</td>
                </tr>
                </tbody>
            </table>
            <ul>
                <li>请求参数示例</li>
            </ul>
            <pre><div style="color: black; background: rgb(245, 242, 240); text-shadow: white 0px 1px; font-family: Consolas, Monaco, &quot;Andale Mono&quot;, &quot;Ubuntu Mono&quot;, monospace; font-size: 1em; text-align: left; white-space: pre; word-spacing: normal; word-break: normal; overflow-wrap: normal; line-height: 1.5; tab-size: 4; hyphens: none; padding: 1em; margin: 0.5em 0px; overflow: auto;"><code class="language-json" style="white-space: pre; color: black; background: none; text-shadow: white 0px 1px; font-family: Consolas, Monaco, &quot;Andale Mono&quot;, &quot;Ubuntu Mono&quot;, monospace; font-size: 1em; text-align: left; word-spacing: normal; word-break: normal; overflow-wrap: normal; line-height: 1.5; tab-size: 4; hyphens: none;"><span>   </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span>
</span><span>      </span><span class="token" style="color: rgb(153, 0, 85);">"outUserId"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"out123456789"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span class="token" style="color: slategray;">//第三方平台UserId</span><span>
</span><span>      </span><span class="token" style="color: rgb(153, 0, 85);">"orderId"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"3467771714635066313"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span>
</span><span>      </span><span class="token" style="color: rgb(153, 0, 85);">"storeId"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"5105493875147414159"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span>
</span><span>      </span><span class="token" style="color: rgb(153, 0, 85);">"storeName"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"自动化测试门店4"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span>
</span><span>      </span><span class="token" style="color: rgb(153, 0, 85);">"cityName"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"杭州市"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span>
</span><span>      </span><span class="token" style="color: rgb(153, 0, 85);">"gunNo"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"2"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span class="token" style="color: slategray;">//枪号</span><span>
</span><span>      </span><span class="token" style="color: rgb(153, 0, 85);">"itemName"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"92#"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span>
</span><span>      </span><span class="token" style="color: rgb(153, 0, 85);">"quantity"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">1.2</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span class="token" style="color: slategray;">//商品升数</span><span>
</span><span>      </span><span class="token" style="color: rgb(153, 0, 85);">"totalMoney"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">800</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span class="token" style="color: slategray;">//订单金额、单位是分</span><span>
</span><span>      </span><span class="token" style="color: rgb(153, 0, 85);">"realMoney"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">800</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span class="token" style="color: slategray;">//实付 金额、单位是分</span><span>
</span><span>      </span><span class="token" style="color: rgb(153, 0, 85);">"payStatus"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">1</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span class="token" style="color: slategray;">//0未支付 1已支付</span><span>
</span><span>      </span><span class="token" style="color: rgb(153, 0, 85);">"payTime"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"2019-11-28 07:03:50"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span>
</span><span>      </span><span class="token" style="color: rgb(153, 0, 85);">"refundStatus"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">0</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span class="token" style="color: slategray;">//0未退款 1已退款</span><span>
</span><span>      </span><span class="token" style="color: rgb(153, 0, 85);">"refundTime"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token null" style="color: rgb(0, 119, 170);">null</span><span> </span><span class="token" style="color: slategray;">//未退款、无退款时间</span><span>
</span><span>   </span><span class="token" style="color: rgb(153, 153, 153);">}</span></code></div></pre>
            <ul>
                <li>
                    <p>响应内容字段说明</p>
                    <table>
                        <thead>
                        <tr>
                            <th style="text-align: left;">字段名称</th>
                            <th style="text-align: left;">字段说明</th>
                            <th style="text-align: left;">类型</th>
                            <th style="text-align: left;">父级</th>
                            <th style="text-align: left;">备注</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td style="text-align: left;">state</td>
                            <td style="text-align: left;">通知结果状态</td>
                            <td style="text-align: left;">Integer</td>
                            <td style="text-align: left;">/</td>
                            <td style="text-align: left;">0不成功 1成功</td>
                        </tr>
                        <tr>
                            <td style="text-align: left;">message</td>
                            <td style="text-align: left;">错误信息</td>
                            <td style="text-align: left;">String</td>
                            <td style="text-align: left;">/</td>
                            <td style="text-align: left;">错误信息</td>
                        </tr>
                        </tbody>
                    </table>
                </li>
                <li>
                    <p>响应内容示例</p>
                </li>
            </ul>
            <pre><div style="color: black; background: rgb(245, 242, 240); text-shadow: white 0px 1px; font-family: Consolas, Monaco, &quot;Andale Mono&quot;, &quot;Ubuntu Mono&quot;, monospace; font-size: 1em; text-align: left; white-space: pre; word-spacing: normal; word-break: normal; overflow-wrap: normal; line-height: 1.5; tab-size: 4; hyphens: none; padding: 1em; margin: 0.5em 0px; overflow: auto;"><code class="language-json" style="white-space: pre; color: black; background: none; text-shadow: white 0px 1px; font-family: Consolas, Monaco, &quot;Andale Mono&quot;, &quot;Ubuntu Mono&quot;, monospace; font-size: 1em; text-align: left; word-spacing: normal; word-break: normal; overflow-wrap: normal; line-height: 1.5; tab-size: 4; hyphens: none;"><span>  完整响应数据
</span><span>   </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span>
</span><span>      </span><span class="token" style="color: rgb(153, 0, 85);">"appKey"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span class="token" style="color: rgb(102, 153, 0);">"xiongtest"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span>
</span><span>      </span><span class="token" style="color: rgb(153, 0, 85);">"data"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span class="token" style="color: rgb(102, 153, 0);">"wNUe7CjwjtxfQ6B2Qu3sElY/il5D7mGn8fWpGEXdFATRZ7lUzS0TPLTq2z/fV2op"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span>
</span><span>      </span><span class="token" style="color: rgb(153, 0, 85);">"code"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span class="token" style="color: rgb(102, 153, 0);">"0"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span>
</span><span>      </span><span class="token" style="color: rgb(153, 0, 85);">"msg"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span class="token" style="color: rgb(102, 153, 0);">"请求成功"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span>
</span><span>      </span><span class="token" style="color: rgb(153, 0, 85);">"sig"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span class="token" style="color: rgb(102, 153, 0);">"D8FFCBCDCFF7CDC15188D7A9958DBA25"</span><span>
</span><span>   </span><span class="token" style="color: rgb(153, 153, 153);">}</span><span>
</span>
解密后的 data 字段
<span>   </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span>
</span><span>      </span><span class="token" style="color: rgb(153, 0, 85);">"state"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span class="token" style="color: rgb(153, 0, 85);">0</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: slategray;">//0不成功 1成功</span><span>
</span><span>      </span><span class="token" style="color: rgb(153, 0, 85);">"message"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span class="token" style="color: rgb(102, 153, 0);">"金额字段缺失"</span><span>
</span><span>   </span><span class="token" style="color: rgb(153, 153, 153);">}</span></code></div></pre>
            <ul>
                <li>
                    <p>备注：</p>
                    <ul>
                        <li>接口调用时机：当订单状态为支付完成、退款完成两种操作时，小桔会调用该接口，将订单信息推送给接入方 2. 出参”接收状态“枚举值：0-不成功； 1-成功</li>
                        <li>
                            <strong>需要对数据进行加密和加签。</strong>
                        </li>
                    </ul>
                </li>
            </ul>
            <h1 id="7、请求数据处理">7、请求数据处理</h1>
            <h3 id="7.1 加密">7.1 加密</h3>
            <ol>
                <li>为了防止业务数据泄漏，针对所有接口的<strong>请求</strong>和<strong>响应</strong>约定两层数据结构，外层数据结构固定，如下图：</li>
            </ol>
            <ul>
                <li>请求参数格式</li>
            </ul>
            <pre><div style="color: black; background: rgb(245, 242, 240); text-shadow: white 0px 1px; font-family: Consolas, Monaco, &quot;Andale Mono&quot;, &quot;Ubuntu Mono&quot;, monospace; font-size: 1em; text-align: left; white-space: pre; word-spacing: normal; word-break: normal; overflow-wrap: normal; line-height: 1.5; tab-size: 4; hyphens: none; padding: 1em; margin: 0.5em 0px; overflow: auto;"><code class="language-json" style="white-space: pre; color: black; background: none; text-shadow: white 0px 1px; font-family: Consolas, Monaco, &quot;Andale Mono&quot;, &quot;Ubuntu Mono&quot;, monospace; font-size: 1em; text-align: left; word-spacing: normal; word-break: normal; overflow-wrap: normal; line-height: 1.5; tab-size: 4; hyphens: none;"><span class="token" style="color: rgb(153, 153, 153);">{</span><span>
</span><span>  </span><span class="token" style="color: rgb(153, 0, 85);">"appKey"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span class="token" style="color: rgb(102, 153, 0);">"jiayoudev"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: slategray;">// 接入方标识符</span><span>
</span><span>  </span><span class="token" style="color: rgb(153, 0, 85);">"data"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span class="token" style="color: rgb(102, 153, 0);">"...."</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span>     </span><span class="token" style="color: slategray;">// 加密后的业务参数</span><span>
</span><span>  </span><span class="token" style="color: rgb(153, 0, 85);">"timeStamp"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span class="token" style="color: rgb(102, 153, 0);">"20191129123456"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: slategray;">//时间戳，格式：yyyyMMddHHmmss</span><span>
</span><span>  </span><span class="token" style="color: rgb(153, 0, 85);">"sig"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span class="token" style="color: rgb(102, 153, 0);">"5837DE192FF7D3A0D9454DDACA387DB3"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span>  </span><span class="token" style="color: slategray;">// 签名字段，签名方式： </span><span>
</span><span>  </span><span class="token" style="color: rgb(153, 0, 85);">"requestId"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span class="token" style="color: rgb(102, 153, 0);">"13fjiheifj"</span><span> </span><span class="token" style="color: slategray;">//可选：调用发起方选择性赋值，标记接入方调用链路，便于后期问题定位，不参与签名计算</span><span>
</span><span></span><span class="token" style="color: rgb(153, 153, 153);">}</span></code></div></pre>
            <ul>
                <li>响应数据格式</li>
            </ul>
            <pre><div style="color: black; background: rgb(245, 242, 240); text-shadow: white 0px 1px; font-family: Consolas, Monaco, &quot;Andale Mono&quot;, &quot;Ubuntu Mono&quot;, monospace; font-size: 1em; text-align: left; white-space: pre; word-spacing: normal; word-break: normal; overflow-wrap: normal; line-height: 1.5; tab-size: 4; hyphens: none; padding: 1em; margin: 0.5em 0px; overflow: auto;"><code class="language-json" style="white-space: pre; color: black; background: none; text-shadow: white 0px 1px; font-family: Consolas, Monaco, &quot;Andale Mono&quot;, &quot;Ubuntu Mono&quot;, monospace; font-size: 1em; text-align: left; word-spacing: normal; word-break: normal; overflow-wrap: normal; line-height: 1.5; tab-size: 4; hyphens: none;"><span class="token" style="color: rgb(153, 153, 153);">{</span><span>
</span><span>  </span><span class="token" style="color: rgb(153, 0, 85);">"appKey"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"jiayoudev"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span>  </span><span class="token" style="color: slategray;">// 接入方标识符</span><span>
</span><span>  </span><span class="token" style="color: rgb(153, 0, 85);">"data"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"...."</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span>  </span><span class="token" style="color: slategray;">// 加密后的业务数据</span><span>
</span><span>  </span><span class="token" style="color: rgb(153, 0, 85);">"code"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"0"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span>
</span><span>  </span><span class="token" style="color: rgb(153, 0, 85);">"msg"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"请求成功"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span>
</span><span>  </span><span class="token" style="color: rgb(153, 0, 85);">"sig"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"5837DE192FF7D3A0D9454DDACA387DB3"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span>
</span><span>  </span><span class="token" style="color: rgb(153, 0, 85);">"requestId"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span class="token" style="color: rgb(102, 153, 0);">"xi3495fjdhe1"</span><span> </span><span class="token" style="color: slategray;">// 小桔请求链路标记，便于问题定位，不参与签名计算</span><span>
</span><span></span><span class="token" style="color: rgb(153, 153, 153);">}</span></code></div></pre>
            <ul>
                <li>针对请求的业务参数、响应的业务数据， json 化后，使用AES 128位算法加解密，加密模式采用CBC，填充模式PKCS5Padding，得到加密后的 data 字段。</li>
                <li>数据加密方式</li>
            </ul>
            <pre><div style="color: black; background: rgb(245, 242, 240); text-shadow: white 0px 1px; font-family: Consolas, Monaco, &quot;Andale Mono&quot;, &quot;Ubuntu Mono&quot;, monospace; font-size: 1em; text-align: left; white-space: pre; word-spacing: normal; word-break: normal; overflow-wrap: normal; line-height: 1.5; tab-size: 4; hyphens: none; padding: 1em; margin: 0.5em 0px; overflow: auto;"><code class="language-java" style="white-space: pre; color: black; background: none; text-shadow: white 0px 1px; font-family: Consolas, Monaco, &quot;Andale Mono&quot;, &quot;Ubuntu Mono&quot;, monospace; font-size: 1em; text-align: left; word-spacing: normal; word-break: normal; overflow-wrap: normal; line-height: 1.5; tab-size: 4; hyphens: none;"><span class="token" style="color: rgb(0, 119, 170);">public</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">static</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">String</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">encrypt</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(221, 74, 104);">String</span><span> originDataStr</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">String</span><span> dataSecret</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">throws</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">Exception</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span>
</span><span>        </span><span class="token" style="color: rgb(0, 119, 170);">byte</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span> raw </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> dataSecret</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">getBytes</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(102, 153, 0);">"UTF-8"</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>        </span><span class="token" style="color: rgb(221, 74, 104);">SecretKeySpec</span><span> skeySpec </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">new</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">SecretKeySpec</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span>raw</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"AES"</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span>
<span>        </span><span class="token" style="color: rgb(221, 74, 104);">Cipher</span><span> cipher </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">Cipher</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">getInstance</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(102, 153, 0);">"AES/CBC/PKCS5Padding"</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>        </span><span class="token" style="color: rgb(221, 74, 104);">IvParameterSpec</span><span> iv </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">new</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">IvParameterSpec</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span>dataSecret</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">getBytes</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>        cipher</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">init</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(153, 0, 85);">1</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> skeySpec</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> iv</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span>
<span>        </span><span class="token" style="color: rgb(0, 119, 170);">byte</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span> encryptedBytes </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> cipher</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">doFinal</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span>originDataStr</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">getBytes</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>        </span><span class="token" style="color: rgb(221, 74, 104);">String</span><span> str </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(0, 119, 170);">new</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">BASE64Encoder</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">encode</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span>encryptedBytes</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>        str </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> str</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">replaceAll</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(102, 153, 0);">"\r"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">""</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">replaceAll</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(102, 153, 0);">"\n"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">""</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span>
<span>        </span><span class="token" style="color: rgb(0, 119, 170);">return</span><span> str</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>    </span><span class="token" style="color: rgb(153, 153, 153);">}</span></code></div></pre>
            <h3 id="7.2 时间戳">7.2 时间戳</h3>
            <ol>
                <li>防止重放攻击，<strong>请求参数</strong>必须传递时间戳，小桔收到请求时校验时间戳与当前时间，时间差超过一定的范围被认为是非法请求，时间戳格式"<strong>yyyyMMddHHmmss</strong>"。</li>
            </ol>
            <h3 id="7.3 验签">7.3 验签</h3>
            <ol>
                <li>为了防止数据被篡改，对请求、响应需按照约定的顺序拼接起来，采用HMac-MD5不可逆算法加小桔分配的签名秘钥进行签名计算，请求时将签名值带上，收到请求后小桔会按相同逻辑进行签名校验，参数签名要求大写。
                    <ul>
                        <li>请求小桔接口的数据需要加签，<strong>返回到小桔的返回值也是需要加签的</strong>。</li>
                    </ul>
                </li>
                <li>具体生成方式如下：
                    <ul>
                        <li>appKey 和 sigSecret可以在应用管理页面上获取。</li>
                        <li>data是加密后的数据。</li>
                        <li>timestamp是时间搓。</li>
                    </ul>
                </li>
            </ol>
            <pre><div style="color: black; background: rgb(245, 242, 240); text-shadow: white 0px 1px; font-family: Consolas, Monaco, &quot;Andale Mono&quot;, &quot;Ubuntu Mono&quot;, monospace; font-size: 1em; text-align: left; white-space: pre; word-spacing: normal; word-break: normal; overflow-wrap: normal; line-height: 1.5; tab-size: 4; hyphens: none; padding: 1em; margin: 0.5em 0px; overflow: auto;"><code class="language-java" style="white-space: pre; color: black; background: none; text-shadow: white 0px 1px; font-family: Consolas, Monaco, &quot;Andale Mono&quot;, &quot;Ubuntu Mono&quot;, monospace; font-size: 1em; text-align: left; word-spacing: normal; word-break: normal; overflow-wrap: normal; line-height: 1.5; tab-size: 4; hyphens: none;"><span class="token" style="color: slategray;">// 请求小桔的数据加签方式</span><span>
</span><span></span><span class="token" style="color: rgb(0, 119, 170);">public</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">static</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">String</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">signRequest</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(221, 74, 104);">String</span><span> appKey</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">String</span><span> sigSecret</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">String</span><span> data</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">String</span><span> timestamp</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">throws</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">Exception</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span>
</span><span>        </span><span class="token" style="color: rgb(221, 74, 104);">String</span><span> strToBeSigned </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> appKey </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">+</span><span> data </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">+</span><span> timestamp</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>        </span><span class="token" style="color: rgb(0, 119, 170);">return</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">getHmacMd5Str</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span>sigSecret</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> strToBeSigned</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>    </span><span class="token" style="color: rgb(153, 153, 153);">}</span><span>
</span>
<span></span><span class="token" style="color: slategray;">// 返回到小桔的数据加签方式</span><span>
</span><span></span><span class="token" style="color: rgb(0, 119, 170);">public</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">static</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">String</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">signResponse</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(221, 74, 104);">String</span><span> appKey</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">String</span><span> sigSecret</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">String</span><span> data</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">String</span><span> code</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">String</span><span> msg</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">throws</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">Exception</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span>
</span><span>  </span><span class="token" style="color: rgb(221, 74, 104);">String</span><span> strToBeSigned </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> appKey </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">+</span><span> data </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">+</span><span> code </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">+</span><span> msg</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>  </span><span class="token" style="color: rgb(0, 119, 170);">return</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">getHmacMd5Str</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span>sigSecret</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> strToBeSigned</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span></span><span class="token" style="color: rgb(153, 153, 153);">}</span><span>
</span>
<span></span><span class="token" style="color: rgb(0, 119, 170);">public</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">static</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">String</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">getHmacMd5Str</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(221, 74, 104);">String</span><span> key</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">String</span><span> data</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">throws</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">Exception</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span>
</span><span>  </span><span class="token" style="color: rgb(0, 119, 170);">byte</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span> keyByte </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> key</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">getBytes</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(102, 153, 0);">"UTF-8"</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>  </span><span class="token" style="color: rgb(0, 119, 170);">byte</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span> dataByte </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> data</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">getBytes</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(102, 153, 0);">"UTF-8"</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>  </span><span class="token" style="color: rgb(0, 119, 170);">byte</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span> hmacMd5Byte </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">getHmacMd5Bytes</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span>keyByte</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> dataByte</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>  </span><span class="token" style="color: rgb(221, 74, 104);">StringBuffer</span><span> md5StrBuff </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">new</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">StringBuffer</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span>
<span>  </span><span class="token" style="color: rgb(0, 119, 170);">for</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(0, 119, 170);">int</span><span> i </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">0</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span> i </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">&lt;</span><span> hmacMd5Byte</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span>length</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span> </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">++</span><span>i</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span>
</span><span>    </span><span class="token" style="color: rgb(0, 119, 170);">if</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(221, 74, 104);">Integer</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">toHexString</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(153, 0, 85);">255</span><span> </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">&amp;</span><span> hmacMd5Byte</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span>i</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">length</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span> </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">==</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">1</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span>
</span><span>      md5StrBuff</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">append</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(102, 153, 0);">"0"</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">append</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(221, 74, 104);">Integer</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">toHexString</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(153, 0, 85);">255</span><span> </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">&amp;</span><span> hmacMd5Byte</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span>i</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>    </span><span class="token" style="color: rgb(153, 153, 153);">}</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">else</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span>
</span><span>      md5StrBuff</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">append</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(221, 74, 104);">Integer</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">toHexString</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(153, 0, 85);">255</span><span> </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">&amp;</span><span> hmacMd5Byte</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span>i</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>    </span><span class="token" style="color: rgb(153, 153, 153);">}</span><span>
</span><span>  </span><span class="token" style="color: rgb(153, 153, 153);">}</span><span>
</span>
<span>  </span><span class="token" style="color: rgb(0, 119, 170);">return</span><span> md5StrBuff</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">toString</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">toUpperCase</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span></span><span class="token" style="color: rgb(153, 153, 153);">}</span><span>
</span>
<span></span><span class="token" style="color: rgb(0, 119, 170);">public</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">static</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">byte</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">getHmacMd5Bytes</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(0, 119, 170);">byte</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span> key</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">byte</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span> data</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">throws</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">NoSuchAlgorithmException</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span>
</span><span>  </span><span class="token" style="color: rgb(0, 119, 170);">int</span><span> length </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">64</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>  </span><span class="token" style="color: rgb(0, 119, 170);">byte</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span> ipad </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">new</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">byte</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span>length</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>  </span><span class="token" style="color: rgb(0, 119, 170);">byte</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span> opad </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">new</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">byte</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span>length</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span>
<span>  </span><span class="token" style="color: rgb(0, 119, 170);">for</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(0, 119, 170);">int</span><span> i </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">0</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span> i </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">&lt;</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">64</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span> </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">++</span><span>i</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span>
</span><span>    ipad</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span>i</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span> </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">54</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>    opad</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span>i</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span> </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">92</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>  </span><span class="token" style="color: rgb(153, 153, 153);">}</span><span>
</span>
<span>  </span><span class="token" style="color: rgb(0, 119, 170);">byte</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span> actualKey </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> key</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>  </span><span class="token" style="color: rgb(0, 119, 170);">byte</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span> keyArr </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">new</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">byte</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span>length</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>  </span><span class="token" style="color: rgb(0, 119, 170);">if</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">(</span><span>key</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span>length </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">&gt;</span><span> length</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span>
</span><span>    actualKey </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">md5</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span>key</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>  </span><span class="token" style="color: rgb(153, 153, 153);">}</span><span>
</span>
<span>  </span><span class="token" style="color: rgb(0, 119, 170);">int</span><span> i</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>  </span><span class="token" style="color: rgb(0, 119, 170);">for</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">(</span><span>i </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">0</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span> i </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">&lt;</span><span> actualKey</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span>length</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span> </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">++</span><span>i</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span>
</span><span>    keyArr</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span>i</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span> </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> actualKey</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span>i</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>  </span><span class="token" style="color: rgb(153, 153, 153);">}</span><span>
</span>
<span>  </span><span class="token" style="color: rgb(0, 119, 170);">if</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">(</span><span>actualKey</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span>length </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">&lt;</span><span> length</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span>
</span><span>    </span><span class="token" style="color: rgb(0, 119, 170);">for</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">(</span><span>i </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> actualKey</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span>length</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span> i </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">&lt;</span><span> keyArr</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span>length</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span> </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">++</span><span>i</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span>
</span><span>      keyArr</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span>i</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span> </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">0</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>    </span><span class="token" style="color: rgb(153, 153, 153);">}</span><span>
</span><span>  </span><span class="token" style="color: rgb(153, 153, 153);">}</span><span>
</span>
<span>  </span><span class="token" style="color: rgb(0, 119, 170);">byte</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span> kIpadXorResult </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">new</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">byte</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span>length</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span>
<span>  </span><span class="token" style="color: rgb(0, 119, 170);">for</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">(</span><span>i </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">0</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span> i </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">&lt;</span><span> length</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span> </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">++</span><span>i</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span>
</span><span>    kIpadXorResult</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span>i</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span> </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(0, 119, 170);">byte</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">(</span><span>keyArr</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span>i</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span> </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">^</span><span> ipad</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span>i</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>  </span><span class="token" style="color: rgb(153, 153, 153);">}</span><span>
</span>
<span>  </span><span class="token" style="color: rgb(0, 119, 170);">byte</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span> firstAppendResult </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">new</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">byte</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span>kIpadXorResult</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span>length </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">+</span><span> data</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span>length</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span>
<span>  </span><span class="token" style="color: rgb(0, 119, 170);">for</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">(</span><span>i </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">0</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span> i </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">&lt;</span><span> kIpadXorResult</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span>length</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span> </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">++</span><span>i</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span>
</span><span>    firstAppendResult</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span>i</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span> </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> kIpadXorResult</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span>i</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>  </span><span class="token" style="color: rgb(153, 153, 153);">}</span><span>
</span>
<span>  </span><span class="token" style="color: rgb(0, 119, 170);">for</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">(</span><span>i </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">0</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span> i </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">&lt;</span><span> data</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span>length</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span> </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">++</span><span>i</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span>
</span><span>    firstAppendResult</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span>i </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">+</span><span> keyArr</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span>length</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span> </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> data</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span>i</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>  </span><span class="token" style="color: rgb(153, 153, 153);">}</span><span>
</span>
<span>  </span><span class="token" style="color: rgb(0, 119, 170);">byte</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span> firstHashResult </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">md5</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span>firstAppendResult</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>  </span><span class="token" style="color: rgb(0, 119, 170);">byte</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span> kOpadXorResult </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">new</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">byte</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span>length</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span>
<span>  </span><span class="token" style="color: rgb(0, 119, 170);">for</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">(</span><span>i </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">0</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span> i </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">&lt;</span><span> length</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span> </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">++</span><span>i</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span>
</span><span>    kOpadXorResult</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span>i</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span> </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(0, 119, 170);">byte</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">(</span><span>keyArr</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span>i</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span> </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">^</span><span> opad</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span>i</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>  </span><span class="token" style="color: rgb(153, 153, 153);">}</span><span>
</span>
<span>  </span><span class="token" style="color: rgb(0, 119, 170);">byte</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span> secondAppendResult </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">new</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">byte</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span>kOpadXorResult</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span>length </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">+</span><span> firstHashResult</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span>length</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span>
<span>  </span><span class="token" style="color: rgb(0, 119, 170);">for</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">(</span><span>i </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">0</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span> i </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">&lt;</span><span> kOpadXorResult</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span>length</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span> </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">++</span><span>i</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span>
</span><span>    secondAppendResult</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span>i</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span> </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> kOpadXorResult</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span>i</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>  </span><span class="token" style="color: rgb(153, 153, 153);">}</span><span>
</span>
<span>  </span><span class="token" style="color: rgb(0, 119, 170);">for</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">(</span><span>i </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">0</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span> i </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">&lt;</span><span> firstHashResult</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span>length</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span> </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">++</span><span>i</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span>
</span><span>    secondAppendResult</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span>i </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">+</span><span> keyArr</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span>length</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span> </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> firstHashResult</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span>i</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>  </span><span class="token" style="color: rgb(153, 153, 153);">}</span><span>
</span>
<span>  </span><span class="token" style="color: rgb(0, 119, 170);">byte</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span> hmacMd5Bytes </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">md5</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span>secondAppendResult</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>  </span><span class="token" style="color: rgb(0, 119, 170);">return</span><span> hmacMd5Bytes</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span></span><span class="token" style="color: rgb(153, 153, 153);">}</span><span>
</span>
<span></span><span class="token" style="color: rgb(0, 119, 170);">private</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">static</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">byte</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">md5</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(0, 119, 170);">byte</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span> str</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">throws</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">NoSuchAlgorithmException</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span>
</span><span>  </span><span class="token" style="color: rgb(221, 74, 104);">MessageDigest</span><span> md </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">MessageDigest</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">getInstance</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(102, 153, 0);">"MD5"</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>  md</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">update</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span>str</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>  </span><span class="token" style="color: rgb(0, 119, 170);">return</span><span> md</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">digest</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span></span><span class="token" style="color: rgb(153, 153, 153);">}</span></code></div></pre>
            <h3 id="7.4 Token">7.4 Token</h3>
            <ol>
                <li>为了防止CSRF攻击，每一次业务请求都需要先请求小桔的 TOKEN 获取接口，校验账号和秘钥，校验通过后小桔返回具有一定有效期的 TOKEN ，再请求业务数据时，将 TOKEN 附加到请求的 Header 上，收到请求后小桔先对TOKEN的合法性做校验，添加方式</li>
                <li>返回的 token 有效期内可以复用， 一般是2小时过期，并且每一次创建token都会返回失效时间。在临近token失效前1分钟的queryToken才会创建新的token。</li>
            </ol>
            <pre><code>addHeader("Authorization", "Bearer " + token);
</code></pre>
            <ol>
                <li>可通过A1接口获取token。</li>
            </ol>
            <p>
                <strong>注:</strong> Bearer 和 token 用”<strong>空格</strong>“分隔
            </p>
            <h3 id="7.5 完整请求举例">7.5 完整请求举例</h3>
            <p>以 queryToken 接口为例：</p>
            <ul>
                <li>Step 1: 业务参数 json 化：{"appSecret": "HlqAnnonTUxDr51AynjlNYzdt+OYXaqceMI70CskDis="}</li>
                <li>Step 2: 对上述json string 执行加密操作 base64(AES(jsonStringData，dataSecret)) 得到：</li>
            </ul>
            <blockquote>
                <p>wcz5oKvj9t112I/oWaW0fQhiGvF6JiGHNjGWtLnvN+Jv3Q8Bg7zvbyNx+9CNFKWz35eNqGhVL1cdgaMkE0iO7w==</p>
            </blockquote>
            <ul>
                <li>Step 3: 构造请求参数
                    <ul>
                        <li>该请求因为是queryToken示例，没有写到token。其他请求都需要在header中添加token。</li>
                        <li>最终的请求为(因为时间戳的问题，该curl命令不可执行。需要合作方自行拼接参数)：</li>
                    </ul>
                </li>
            </ul>
            <pre><div style="color: black; background: rgb(245, 242, 240); text-shadow: white 0px 1px; font-family: Consolas, Monaco, &quot;Andale Mono&quot;, &quot;Ubuntu Mono&quot;, monospace; font-size: 1em; text-align: left; white-space: pre; word-spacing: normal; word-break: normal; overflow-wrap: normal; line-height: 1.5; tab-size: 4; hyphens: none; padding: 1em; margin: 0.5em 0px; overflow: auto;"><code class="language-java" style="white-space: pre; color: black; background: none; text-shadow: white 0px 1px; font-family: Consolas, Monaco, &quot;Andale Mono&quot;, &quot;Ubuntu Mono&quot;, monospace; font-size: 1em; text-align: left; word-spacing: normal; word-break: normal; overflow-wrap: normal; line-height: 1.5; tab-size: 4; hyphens: none;"><span>curl </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">--</span><span>location </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">--</span><span>request POST 'https</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">/</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">/</span><span>gw</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span>am</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span>xiaojukeji</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span>com</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">/</span><span>online</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">/</span><span class="token" style="color: rgb(0, 119, 170);">open</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">/</span><span>energy</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">/</span><span>v1</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">/</span><span>queryToken'
</span><span></span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">--</span><span>header '</span><span class="token" style="color: rgb(221, 74, 104);">Content</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">-</span><span class="token" style="color: rgb(221, 74, 104);">Type</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> application</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">/</span><span>json'
</span><span></span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">--</span><span>header '</span><span class="token" style="color: rgb(221, 74, 104);">Accept</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> application</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">/</span><span>json'
</span><span></span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">--</span><span>data</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">-</span><span>raw '</span><span class="token" style="color: rgb(153, 153, 153);">{</span><span>
</span><span>    </span><span class="token" style="color: rgb(102, 153, 0);">"timeStamp"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"20221024173703"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span>
</span><span>    </span><span class="token" style="color: rgb(102, 153, 0);">"sig"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"FFB8E1D2DBCE55653B2EF998438C7BFF"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span>
</span><span>    </span><span class="token" style="color: rgb(102, 153, 0);">"appKey"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"xiongtest"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span>
</span><span>    </span><span class="token" style="color: rgb(102, 153, 0);">"data"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"wcz5oKvj9t112I/oWaW0fQhiGvF6JiGHNjGWtLnvN+Jv3Q8Bg7zvbyNx+9CNFKWz35eNqGhVL1cdgaMkE0iO7w=="</span><span>
</span><span></span><span class="token" style="color: rgb(153, 153, 153);">}</span><span>'</span></code></div></pre>
            <ul>
                <li>Step 4: 发起请求，得到响应内容</li>
            </ul>
            <pre><div style="color: black; background: rgb(245, 242, 240); text-shadow: white 0px 1px; font-family: Consolas, Monaco, &quot;Andale Mono&quot;, &quot;Ubuntu Mono&quot;, monospace; font-size: 1em; text-align: left; white-space: pre; word-spacing: normal; word-break: normal; overflow-wrap: normal; line-height: 1.5; tab-size: 4; hyphens: none; padding: 1em; margin: 0.5em 0px; overflow: auto;"><code class="language-json" style="white-space: pre; color: black; background: none; text-shadow: white 0px 1px; font-family: Consolas, Monaco, &quot;Andale Mono&quot;, &quot;Ubuntu Mono&quot;, monospace; font-size: 1em; text-align: left; word-spacing: normal; word-break: normal; overflow-wrap: normal; line-height: 1.5; tab-size: 4; hyphens: none;"><span class="token" style="color: rgb(153, 153, 153);">{</span><span>
</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">"appKey"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span class="token" style="color: rgb(102, 153, 0);">"xiongtest"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span>
</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">"data"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span class="token" style="color: rgb(102, 153, 0);">"34bgypgmDHZPaA3nsqJD0sO13xP284Nw8rjrXVr87hYRwyDyMnsjwYhps+2IgVtEJfAio97b9Of+5Y6N63N2MwkrKvmPIQU1XxtUTx6dUDA="</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span>
</span><span>  </span><span class="token" style="color: rgb(153, 0, 85);">"code"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span class="token" style="color: rgb(102, 153, 0);">"0"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span>
</span><span>  </span><span class="token" style="color: rgb(153, 0, 85);">"msg"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span class="token" style="color: rgb(102, 153, 0);">"请求成功"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span>
</span><span>  </span><span class="token" style="color: rgb(153, 0, 85);">"sig"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span class="token" style="color: rgb(102, 153, 0);">"A663BFB99FED85FDE7D7180D1DB572FD"</span><span>
</span><span></span><span class="token" style="color: rgb(153, 153, 153);">}</span></code></div></pre>
            <ul>
                <li>Step 5(可选)：校验响应的签名字段是否符合预期(判断本地计算的签名值是否与响应内容的 sig 字段一致)</li>
            </ul>
            <pre><div style="color: black; background: rgb(245, 242, 240); text-shadow: white 0px 1px; font-family: Consolas, Monaco, &quot;Andale Mono&quot;, &quot;Ubuntu Mono&quot;, monospace; font-size: 1em; text-align: left; white-space: pre; word-spacing: normal; word-break: normal; overflow-wrap: normal; line-height: 1.5; tab-size: 4; hyphens: none; padding: 1em; margin: 0.5em 0px; overflow: auto;"><code class="language-json" style="white-space: pre; color: black; background: none; text-shadow: white 0px 1px; font-family: Consolas, Monaco, &quot;Andale Mono&quot;, &quot;Ubuntu Mono&quot;, monospace; font-size: 1em; text-align: left; word-spacing: normal; word-break: normal; overflow-wrap: normal; line-height: 1.5; tab-size: 4; hyphens: none;"><span>sig_calculated=UpperCase(HMacMD5(appKey + data + code + msg，sigSecret))=A663BFB99FED85FDE7D7180D1DB572FD</span></code></div></pre>
            <ul>
                <li>Step 6: 将响应内容的 data 解密，得到业务数据</li>
            </ul>
            <pre><div style="color: black; background: rgb(245, 242, 240); text-shadow: white 0px 1px; font-family: Consolas, Monaco, &quot;Andale Mono&quot;, &quot;Ubuntu Mono&quot;, monospace; font-size: 1em; text-align: left; white-space: pre; word-spacing: normal; word-break: normal; overflow-wrap: normal; line-height: 1.5; tab-size: 4; hyphens: none; padding: 1em; margin: 0.5em 0px; overflow: auto;"><code class="language-json" style="white-space: pre; color: black; background: none; text-shadow: white 0px 1px; font-family: Consolas, Monaco, &quot;Andale Mono&quot;, &quot;Ubuntu Mono&quot;, monospace; font-size: 1em; text-align: left; word-spacing: normal; word-break: normal; overflow-wrap: normal; line-height: 1.5; tab-size: 4; hyphens: none;"><span class="token" style="color: rgb(153, 153, 153);">{</span><span>
</span><span>  </span><span class="token" style="color: rgb(153, 0, 85);">"accessToken"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"1d79ec49f7604ed589821403ca08e21f"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span>
</span><span>  </span><span class="token" style="color: rgb(153, 0, 85);">"availableTime"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">7200</span><span>
</span><span></span><span class="token" style="color: rgb(153, 153, 153);">}</span></code></div></pre>
            <h3 id="7.6 响应状态码">7.6 响应状态码</h3>
            <table>
                <thead>
                <tr>
                    <th style="text-align: left;">状态码</th>
                    <th style="text-align: left;">说明</th>
                </tr>
                </thead>
                <tbody>
                <tr>
                    <td style="text-align: left;">1001</td>
                    <td style="text-align: left;">签名错误</td>
                </tr>
                <tr>
                    <td style="text-align: left;">1002</td>
                    <td style="text-align: left;">Token 错误</td>
                </tr>
                <tr>
                    <td style="text-align: left;">1003</td>
                    <td style="text-align: left;">POST 参数不合法</td>
                </tr>
                <tr>
                    <td style="text-align: left;">1004</td>
                    <td style="text-align: left;">业务参数不合法</td>
                </tr>
                <tr>
                    <td style="text-align: left;">1005</td>
                    <td style="text-align: left;">appKey 不合法</td>
                </tr>
                <tr>
                    <td style="text-align: left;">1006</td>
                    <td style="text-align: left;">数据加解密错误</td>
                </tr>
                <tr>
                    <td style="text-align: left;">……</td>
                    <td style="text-align: left;">……</td>
                </tr>
                <tr>
                    <td style="text-align: left;">2001</td>
                    <td style="text-align: left;">操作太快了</td>
                </tr>
                <tr>
                    <td style="text-align: left;">2002</td>
                    <td style="text-align: left;">操作失败，请联系小桔技术人员</td>
                </tr>
                <tr>
                    <td style="text-align: left;">2003</td>
                    <td style="text-align: left;">对不起，您无权操作</td>
                </tr>
                <tr>
                    <td style="text-align: left;">……</td>
                    <td style="text-align: left;">……</td>
                </tr>
                <tr>
                    <td style="text-align: left;">500</td>
                    <td style="text-align: left;">系统繁忙，请稍后重试</td>
                </tr>
                </tbody>
            </table>
            <h3 id="7.7 接口安全方案代码示例（In Java）">7.7 接口安全方案代码示例（In Java）</h3>
            <pre><div style="color: black; background: rgb(245, 242, 240); text-shadow: white 0px 1px; font-family: Consolas, Monaco, &quot;Andale Mono&quot;, &quot;Ubuntu Mono&quot;, monospace; font-size: 1em; text-align: left; white-space: pre; word-spacing: normal; word-break: normal; overflow-wrap: normal; line-height: 1.5; tab-size: 4; hyphens: none; padding: 1em; margin: 0.5em 0px; overflow: auto;"><code class="language-java" style="white-space: pre; color: black; background: none; text-shadow: white 0px 1px; font-family: Consolas, Monaco, &quot;Andale Mono&quot;, &quot;Ubuntu Mono&quot;, monospace; font-size: 1em; text-align: left; word-spacing: normal; word-break: normal; overflow-wrap: normal; line-height: 1.5; tab-size: 4; hyphens: none;"><span>   </span><span class="token" style="color: rgb(0, 119, 170);">package</span><span> </span><span class="token">com</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token">xiaojukeji</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token">security</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token">util</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token">demo</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span>
<span>   </span><span class="token" style="color: rgb(0, 119, 170);">import</span><span> </span><span class="token">com</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token">alibaba</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token">fastjson</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span>JSON</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>   </span><span class="token" style="color: rgb(0, 119, 170);">import</span><span> </span><span class="token">sun</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token">misc</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">BASE64Decoder</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>   </span><span class="token" style="color: rgb(0, 119, 170);">import</span><span> </span><span class="token">sun</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token">misc</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">BASE64Encoder</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span>
<span>   </span><span class="token" style="color: rgb(0, 119, 170);">import</span><span> </span><span class="token">javax</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token">crypto</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">Cipher</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>   </span><span class="token" style="color: rgb(0, 119, 170);">import</span><span> </span><span class="token">javax</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token">crypto</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token">spec</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">IvParameterSpec</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>   </span><span class="token" style="color: rgb(0, 119, 170);">import</span><span> </span><span class="token">javax</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token">crypto</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token">spec</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">SecretKeySpec</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>   </span><span class="token" style="color: rgb(0, 119, 170);">import</span><span> </span><span class="token">java</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token">security</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">MessageDigest</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>   </span><span class="token" style="color: rgb(0, 119, 170);">import</span><span> </span><span class="token">java</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token">security</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">NoSuchAlgorithmException</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>   </span><span class="token" style="color: rgb(0, 119, 170);">import</span><span> </span><span class="token">java</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token">util</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">HashMap</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>   </span><span class="token" style="color: rgb(0, 119, 170);">import</span><span> </span><span class="token">java</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token">util</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">Map</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span>
<span>   </span><span class="token" style="color: rgb(0, 119, 170);">public</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">class</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">SecurityDemoUtil</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span>
</span>
<span>       </span><span class="token" style="color: rgb(0, 119, 170);">private</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">static</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">final</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">String</span><span> appKey </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"xiongtest"</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>       </span><span class="token" style="color: rgb(0, 119, 170);">private</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">static</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">final</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">String</span><span> appSecret </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"HlqAnnonTUxDr51AynjlNYzdt+OYXaqceMI70CskDis="</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>       </span><span class="token" style="color: rgb(0, 119, 170);">private</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">static</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">final</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">String</span><span> dataSecret </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"HlqAnnonTUxDr51A"</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>       </span><span class="token" style="color: rgb(0, 119, 170);">private</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">static</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">final</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">String</span><span> sigSecret </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"XaqceMI70CskDis="</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span>
<span>       </span><span class="token" style="color: rgb(0, 119, 170);">public</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">static</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">void</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">main</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(221, 74, 104);">String</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span> args</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">throws</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">Exception</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span>
</span><span>           </span><span class="token" style="color: rgb(221, 74, 104);">Map</span><span class="token generics" style="color: rgb(153, 153, 153);">&lt;</span><span class="token generics" style="color: rgb(221, 74, 104);">String</span><span class="token generics" style="color: rgb(153, 153, 153);">,</span><span class="token generics"> </span><span class="token generics" style="color: rgb(221, 74, 104);">Object</span><span class="token generics" style="color: rgb(153, 153, 153);">&gt;</span><span> originRequestDataMap </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">new</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">HashMap</span><span class="token generics" style="color: rgb(153, 153, 153);">&lt;</span><span class="token generics" style="color: rgb(221, 74, 104);">String</span><span class="token generics" style="color: rgb(153, 153, 153);">,</span><span class="token generics"> </span><span class="token generics" style="color: rgb(221, 74, 104);">Object</span><span class="token generics" style="color: rgb(153, 153, 153);">&gt;</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(153, 0, 85);">4</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span>
</span><span>               </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span>
</span><span>                   </span><span class="token" style="color: rgb(221, 74, 104);">put</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(102, 153, 0);">"appSecret"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> appSecret</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>               </span><span class="token" style="color: rgb(153, 153, 153);">}</span><span>
</span><span>           </span><span class="token" style="color: rgb(153, 153, 153);">}</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span>
<span>           </span><span class="token" style="color: rgb(221, 74, 104);">String</span><span> originRequestDataJsonStr </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> JSON</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">toJSONString</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span>originRequestDataMap</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>           </span><span class="token" style="color: rgb(221, 74, 104);">System</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span>out</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">println</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(102, 153, 0);">"原始请求 jsonStr:"</span><span> </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">+</span><span> originRequestDataJsonStr</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>           </span><span class="token" style="color: slategray;">//{"appSecret":"HSoAnnonTUxDr51AynjlNYzdt+OYXaqceMI70CskDis="}</span><span>
</span>
<span>           </span><span class="token" style="color: slategray;">// 加密</span><span>
</span><span>           </span><span class="token" style="color: rgb(221, 74, 104);">String</span><span> encryptedRequestDataStr </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">encrypt</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span>originRequestDataJsonStr</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> dataSecret</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> dataSecret</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>           </span><span class="token" style="color: rgb(221, 74, 104);">System</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span>out</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">println</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(102, 153, 0);">"加密后请求 str:"</span><span> </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">+</span><span> encryptedRequestDataStr</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>           </span><span class="token" style="color: slategray;">//wcz5oKvj9t112I/oWaW0fQhiGvF6JiGHNjGWtLnvN+Jv3Q8Bg7zvbyNx+9CNFKWz35eNqGhVL1cdgaMkE0iO7w==</span><span>
</span>
<span>           </span><span class="token" style="color: slategray;">// 解密</span><span>
</span><span>           </span><span class="token" style="color: rgb(221, 74, 104);">String</span><span> decryptedStr </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">decrypt</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span>encryptedRequestDataStr</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> dataSecret</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> dataSecret</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>           </span><span class="token" style="color: rgb(221, 74, 104);">System</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span>out</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">println</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(102, 153, 0);">"解密后请求 str:"</span><span> </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">+</span><span> decryptedStr</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>           </span><span class="token" style="color: slategray;">//{"appSecret":"HlqAnnonTUxDr51AynjlNYzdt+OYXaqceMI70CskDis="}</span><span>
</span>
<span>           </span><span class="token" style="color: slategray;">// request 加签</span><span>
</span><span>           </span><span class="token" style="color: rgb(221, 74, 104);">String</span><span> timestamp </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"20200309160100"</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>           </span><span class="token" style="color: rgb(221, 74, 104);">String</span><span> requestSignStr </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">signRequest</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span>appKey</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> sigSecret</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> encryptedRequestDataStr</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> timestamp</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>           </span><span class="token" style="color: rgb(221, 74, 104);">System</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span>out</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">println</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(102, 153, 0);">"加签后请求数据:"</span><span> </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">+</span><span> requestSignStr</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>           </span><span class="token" style="color: slategray;">//4C1153DD1A1C98B02B5AD135E42BA65A</span><span>
</span>
<span>           </span><span class="token" style="color: rgb(221, 74, 104);">Map</span><span class="token generics" style="color: rgb(153, 153, 153);">&lt;</span><span class="token generics" style="color: rgb(221, 74, 104);">String</span><span class="token generics" style="color: rgb(153, 153, 153);">,</span><span class="token generics"> </span><span class="token generics" style="color: rgb(221, 74, 104);">Object</span><span class="token generics" style="color: rgb(153, 153, 153);">&gt;</span><span> requestParamDataMap </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">new</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">HashMap</span><span class="token generics" style="color: rgb(153, 153, 153);">&lt;</span><span class="token generics" style="color: rgb(221, 74, 104);">String</span><span class="token generics" style="color: rgb(153, 153, 153);">,</span><span class="token generics"> </span><span class="token generics" style="color: rgb(221, 74, 104);">Object</span><span class="token generics" style="color: rgb(153, 153, 153);">&gt;</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(153, 0, 85);">6</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span>
</span><span>               </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span>
</span><span>                   </span><span class="token" style="color: rgb(221, 74, 104);">put</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(102, 153, 0);">"appKey"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> appKey</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>                   </span><span class="token" style="color: rgb(221, 74, 104);">put</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(102, 153, 0);">"data"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> encryptedRequestDataStr</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>                   </span><span class="token" style="color: rgb(221, 74, 104);">put</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(102, 153, 0);">"timeStamp"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> timestamp</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>                   </span><span class="token" style="color: rgb(221, 74, 104);">put</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(102, 153, 0);">"sig"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> requestSignStr</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>               </span><span class="token" style="color: rgb(153, 153, 153);">}</span><span>
</span><span>           </span><span class="token" style="color: rgb(153, 153, 153);">}</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>           </span><span class="token" style="color: rgb(221, 74, 104);">System</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span>out</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">println</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(102, 153, 0);">"request str:"</span><span> </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">+</span><span> JSON</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">toJSONString</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span>requestParamDataMap</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>           </span><span class="token" style="color: slategray;">//{"timeStamp":"20200309160100","sig":"4C1153DD1A1C98B02B5AD135E42BA65A","appKey":"xiongtest","data":"wcz5oKvj9t112I/oWaW0fQhiGvF6JiGHNjGWtLnvN+Jv3Q8Bg7zvbyNx+9CNFKWz35eNqGhVL1cdgaMkE0iO7w=="}</span><span>
</span>
<span>           </span><span class="token" style="color: slategray;">// response 加签</span><span>
</span><span>           </span><span class="token" style="color: rgb(221, 74, 104);">Map</span><span class="token generics" style="color: rgb(153, 153, 153);">&lt;</span><span class="token generics" style="color: rgb(221, 74, 104);">String</span><span class="token generics" style="color: rgb(153, 153, 153);">,</span><span class="token generics"> </span><span class="token generics" style="color: rgb(221, 74, 104);">Object</span><span class="token generics" style="color: rgb(153, 153, 153);">&gt;</span><span> originResponseDataMap </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">new</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">HashMap</span><span class="token generics" style="color: rgb(153, 153, 153);">&lt;</span><span class="token generics" style="color: rgb(221, 74, 104);">String</span><span class="token generics" style="color: rgb(153, 153, 153);">,</span><span class="token generics"> </span><span class="token generics" style="color: rgb(221, 74, 104);">Object</span><span class="token generics" style="color: rgb(153, 153, 153);">&gt;</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(153, 0, 85);">4</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span>
</span><span>               </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span>
</span><span>                   </span><span class="token" style="color: rgb(221, 74, 104);">put</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(102, 153, 0);">"accessToken"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"1d79ec49f7604ed589821403ca08e21f"</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>                   </span><span class="token" style="color: rgb(221, 74, 104);">put</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(102, 153, 0);">"availableTime"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">7200</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>               </span><span class="token" style="color: rgb(153, 153, 153);">}</span><span>
</span><span>           </span><span class="token" style="color: rgb(153, 153, 153);">}</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>           </span><span class="token" style="color: rgb(221, 74, 104);">String</span><span> originResponseJsonStr </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> JSON</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">toJSONString</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span>originResponseDataMap</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>           </span><span class="token" style="color: rgb(221, 74, 104);">System</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span>out</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">println</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(102, 153, 0);">"原始响应:"</span><span> </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">+</span><span> originResponseJsonStr</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>           </span><span class="token" style="color: slategray;">//{"accessToken":"1d79ec49f7604ed589821403ca08e21f","availableTime":7200}</span><span>
</span>
<span>           </span><span class="token" style="color: rgb(221, 74, 104);">String</span><span> encryptedResponseStr </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">encrypt</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span>originResponseJsonStr</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> dataSecret</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> dataSecret</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>           </span><span class="token" style="color: rgb(221, 74, 104);">System</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span>out</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">println</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(102, 153, 0);">"加密响应:"</span><span> </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">+</span><span> encryptedResponseStr</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>           </span><span class="token" style="color: slategray;">//34bgypgmDHZPaA3nsqJD0sO13xP284Nw8rjrXVr87hYRwyDyMnsjwYhps+2IgVtEJfAio97b9Of+5Y6N63N2MwkrKvmPIQU1XxtUTx6dUDA=</span><span>
</span>
<span>           </span><span class="token" style="color: rgb(221, 74, 104);">String</span><span> code </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"0"</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>           </span><span class="token" style="color: rgb(221, 74, 104);">String</span><span> msg </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"请求成功"</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>           </span><span class="token" style="color: rgb(221, 74, 104);">String</span><span> responseSignStr </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">signResponse</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span>appKey</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> sigSecret</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> encryptedResponseStr</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> code</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> msg</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>           </span><span class="token" style="color: rgb(221, 74, 104);">System</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span>out</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">println</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(102, 153, 0);">"响应签名:"</span><span> </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">+</span><span> responseSignStr</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>           </span><span class="token" style="color: slategray;">//A663BFB99FED85FDE7D7180D1DB572FD</span><span>
</span>
<span>           </span><span class="token" style="color: rgb(221, 74, 104);">Map</span><span class="token generics" style="color: rgb(153, 153, 153);">&lt;</span><span class="token generics" style="color: rgb(221, 74, 104);">String</span><span class="token generics" style="color: rgb(153, 153, 153);">,</span><span class="token generics"> </span><span class="token generics" style="color: rgb(221, 74, 104);">Object</span><span class="token generics" style="color: rgb(153, 153, 153);">&gt;</span><span> responseParamMap </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">new</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">HashMap</span><span class="token generics" style="color: rgb(153, 153, 153);">&lt;</span><span class="token generics" style="color: rgb(221, 74, 104);">String</span><span class="token generics" style="color: rgb(153, 153, 153);">,</span><span class="token generics"> </span><span class="token generics" style="color: rgb(221, 74, 104);">Object</span><span class="token generics" style="color: rgb(153, 153, 153);">&gt;</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(153, 0, 85);">8</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span>
</span><span>               </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span>
</span><span>                   </span><span class="token" style="color: rgb(221, 74, 104);">put</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(102, 153, 0);">"appKey"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> appKey</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>                   </span><span class="token" style="color: rgb(221, 74, 104);">put</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(102, 153, 0);">"data"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> encryptedResponseStr</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>                   </span><span class="token" style="color: rgb(221, 74, 104);">put</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(102, 153, 0);">"code"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> code</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>                   </span><span class="token" style="color: rgb(221, 74, 104);">put</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(102, 153, 0);">"msg"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> msg</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>                   </span><span class="token" style="color: rgb(221, 74, 104);">put</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(102, 153, 0);">"sig"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> responseSignStr</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>               </span><span class="token" style="color: rgb(153, 153, 153);">}</span><span>
</span><span>           </span><span class="token" style="color: rgb(153, 153, 153);">}</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>           </span><span class="token" style="color: rgb(221, 74, 104);">System</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span>out</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">println</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(102, 153, 0);">"response str:"</span><span> </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">+</span><span> JSON</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">toJSONString</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span>responseParamMap</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>           </span><span class="token" style="color: slategray;">//{"msg":"请求成功","sig":"A663BFB99FED85FDE7D7180D1DB572FD","appKey":"xiongtest","code":"0","data":"34bgypgmDHZPaA3nsqJD0sO13xP284Nw8rjrXVr87hYRwyDyMnsjwYhps+2IgVtEJfAio97b9Of+5Y6N63N2MwkrKvmPIQU1XxtUTx6dUDA="}</span><span>
</span><span>       </span><span class="token" style="color: rgb(153, 153, 153);">}</span><span>
</span>
<span>       </span><span class="token" style="color: rgb(0, 119, 170);">public</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">static</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">String</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">encrypt</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(221, 74, 104);">String</span><span> originDataStr</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">String</span><span> dataSecret</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">String</span><span> dataSecretIv</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">throws</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">Exception</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span>
</span><span>           </span><span class="token" style="color: rgb(0, 119, 170);">byte</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span> raw </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> dataSecret</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">getBytes</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(102, 153, 0);">"UTF-8"</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>           </span><span class="token" style="color: rgb(221, 74, 104);">SecretKeySpec</span><span> skeySpec </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">new</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">SecretKeySpec</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span>raw</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"AES"</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span>
<span>           </span><span class="token" style="color: rgb(221, 74, 104);">Cipher</span><span> cipher </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">Cipher</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">getInstance</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(102, 153, 0);">"AES/CBC/PKCS5Padding"</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>           </span><span class="token" style="color: rgb(221, 74, 104);">IvParameterSpec</span><span> iv </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">new</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">IvParameterSpec</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span>dataSecretIv</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">getBytes</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>           cipher</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">init</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(153, 0, 85);">1</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> skeySpec</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> iv</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span>
<span>           </span><span class="token" style="color: rgb(0, 119, 170);">byte</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span> encryptedBytes </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> cipher</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">doFinal</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span>originDataStr</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">getBytes</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>           </span><span class="token" style="color: rgb(221, 74, 104);">String</span><span> str </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(0, 119, 170);">new</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">BASE64Encoder</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">encode</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span>encryptedBytes</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>           str </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> str</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">replaceAll</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(102, 153, 0);">"\r"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">""</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">replaceAll</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(102, 153, 0);">"\n"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">""</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span>
<span>           </span><span class="token" style="color: rgb(0, 119, 170);">return</span><span> str</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>       </span><span class="token" style="color: rgb(153, 153, 153);">}</span><span>
</span>
<span>       </span><span class="token" style="color: rgb(0, 119, 170);">public</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">static</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">String</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">decrypt</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(221, 74, 104);">String</span><span> encodedDataStr</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">String</span><span> dataSecret</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">String</span><span> dataSecretIv</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">throws</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">Exception</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span>
</span><span>           </span><span class="token" style="color: rgb(0, 119, 170);">byte</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span> raw </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> dataSecret</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">getBytes</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(102, 153, 0);">"UTF-8"</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>           </span><span class="token" style="color: rgb(221, 74, 104);">SecretKeySpec</span><span> skeySpec </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">new</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">SecretKeySpec</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span>raw</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"AES"</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span>
<span>           </span><span class="token" style="color: rgb(221, 74, 104);">Cipher</span><span> cipher </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">Cipher</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">getInstance</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(102, 153, 0);">"AES/CBC/PKCS5Padding"</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>           </span><span class="token" style="color: rgb(221, 74, 104);">IvParameterSpec</span><span> iv </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">new</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">IvParameterSpec</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span>dataSecretIv</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">getBytes</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>           cipher</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">init</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(153, 0, 85);">2</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> skeySpec</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> iv</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span>
<span>           </span><span class="token" style="color: rgb(0, 119, 170);">byte</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span> encryptedBytes </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(0, 119, 170);">new</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">BASE64Decoder</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">decodeBuffer</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span>encodedDataStr</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>           </span><span class="token" style="color: rgb(0, 119, 170);">byte</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span> original </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> cipher</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">doFinal</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span>encryptedBytes</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>           </span><span class="token" style="color: rgb(221, 74, 104);">String</span><span> originalString </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">new</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">String</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span>original</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"UTF-8"</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span>
<span>           </span><span class="token" style="color: rgb(0, 119, 170);">return</span><span> originalString</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>       </span><span class="token" style="color: rgb(153, 153, 153);">}</span><span>
</span>
<span>       </span><span class="token" style="color: rgb(0, 119, 170);">public</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">static</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">String</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">signRequest</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(221, 74, 104);">String</span><span> appKey</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">String</span><span> sigSecret</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">String</span><span> data</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">String</span><span> timestamp</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">throws</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">Exception</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span>
</span><span>           </span><span class="token" style="color: rgb(221, 74, 104);">String</span><span> strToBeSigned </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> appKey </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">+</span><span> data </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">+</span><span> timestamp</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>           </span><span class="token" style="color: rgb(0, 119, 170);">return</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">getHmacMd5Str</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span>sigSecret</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> strToBeSigned</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>       </span><span class="token" style="color: rgb(153, 153, 153);">}</span><span>
</span>
<span>       </span><span class="token" style="color: rgb(0, 119, 170);">public</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">static</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">String</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">signResponse</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(221, 74, 104);">String</span><span> appKey</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">String</span><span> sigSecret</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">String</span><span> data</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">String</span><span> code</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">String</span><span> msg</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">throws</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">Exception</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span>
</span><span>           </span><span class="token" style="color: rgb(221, 74, 104);">String</span><span> strToBeSigned </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> appKey </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">+</span><span> data </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">+</span><span> code </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">+</span><span> msg</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>           </span><span class="token" style="color: rgb(0, 119, 170);">return</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">getHmacMd5Str</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span>sigSecret</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> strToBeSigned</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>       </span><span class="token" style="color: rgb(153, 153, 153);">}</span><span>
</span>
<span>       </span><span class="token" style="color: rgb(0, 119, 170);">public</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">static</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">String</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">getHmacMd5Str</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(221, 74, 104);">String</span><span> key</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">String</span><span> data</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">throws</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">Exception</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span>
</span><span>           </span><span class="token" style="color: rgb(0, 119, 170);">byte</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span> keyByte </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> key</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">getBytes</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(102, 153, 0);">"UTF-8"</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>           </span><span class="token" style="color: rgb(0, 119, 170);">byte</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span> dataByte </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> data</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">getBytes</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(102, 153, 0);">"UTF-8"</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>           </span><span class="token" style="color: rgb(0, 119, 170);">byte</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span> hmacMd5Byte </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">getHmacMd5Bytes</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span>keyByte</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> dataByte</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>           </span><span class="token" style="color: rgb(221, 74, 104);">StringBuffer</span><span> md5StrBuff </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">new</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">StringBuffer</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span>
<span>           </span><span class="token" style="color: rgb(0, 119, 170);">for</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(0, 119, 170);">int</span><span> i </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">0</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span> i </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">&lt;</span><span> hmacMd5Byte</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span>length</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span> </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">++</span><span>i</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span>
</span><span>               </span><span class="token" style="color: rgb(0, 119, 170);">if</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(221, 74, 104);">Integer</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">toHexString</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(153, 0, 85);">255</span><span> </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">&amp;</span><span> hmacMd5Byte</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span>i</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">length</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span> </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">==</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">1</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span>
</span><span>                   md5StrBuff</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">append</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(102, 153, 0);">"0"</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">append</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(221, 74, 104);">Integer</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">toHexString</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(153, 0, 85);">255</span><span> </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">&amp;</span><span> hmacMd5Byte</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span>i</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>               </span><span class="token" style="color: rgb(153, 153, 153);">}</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">else</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span>
</span><span>                   md5StrBuff</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">append</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(221, 74, 104);">Integer</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">toHexString</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(153, 0, 85);">255</span><span> </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">&amp;</span><span> hmacMd5Byte</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span>i</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>               </span><span class="token" style="color: rgb(153, 153, 153);">}</span><span>
</span><span>           </span><span class="token" style="color: rgb(153, 153, 153);">}</span><span>
</span>
<span>           </span><span class="token" style="color: rgb(0, 119, 170);">return</span><span> md5StrBuff</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">toString</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">toUpperCase</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>       </span><span class="token" style="color: rgb(153, 153, 153);">}</span><span>
</span>
<span>       </span><span class="token" style="color: rgb(0, 119, 170);">public</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">static</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">byte</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">getHmacMd5Bytes</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(0, 119, 170);">byte</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span> key</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">byte</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span> data</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">throws</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">NoSuchAlgorithmException</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span>
</span><span>           </span><span class="token" style="color: rgb(0, 119, 170);">int</span><span> length </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">64</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>           </span><span class="token" style="color: rgb(0, 119, 170);">byte</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span> ipad </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">new</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">byte</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span>length</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>           </span><span class="token" style="color: rgb(0, 119, 170);">byte</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span> opad </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">new</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">byte</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span>length</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span>
<span>           </span><span class="token" style="color: rgb(0, 119, 170);">for</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(0, 119, 170);">int</span><span> i </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">0</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span> i </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">&lt;</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">64</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span> </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">++</span><span>i</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span>
</span><span>               ipad</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span>i</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span> </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">54</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>               opad</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span>i</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span> </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">92</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>           </span><span class="token" style="color: rgb(153, 153, 153);">}</span><span>
</span>
<span>           </span><span class="token" style="color: rgb(0, 119, 170);">byte</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span> actualKey </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> key</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>           </span><span class="token" style="color: rgb(0, 119, 170);">byte</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span> keyArr </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">new</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">byte</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span>length</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>           </span><span class="token" style="color: rgb(0, 119, 170);">if</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">(</span><span>key</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span>length </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">&gt;</span><span> length</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span>
</span><span>               actualKey </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">md5</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span>key</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>           </span><span class="token" style="color: rgb(153, 153, 153);">}</span><span>
</span>
<span>           </span><span class="token" style="color: rgb(0, 119, 170);">int</span><span> i</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>           </span><span class="token" style="color: rgb(0, 119, 170);">for</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">(</span><span>i </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">0</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span> i </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">&lt;</span><span> actualKey</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span>length</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span> </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">++</span><span>i</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span>
</span><span>               keyArr</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span>i</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span> </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> actualKey</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span>i</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>           </span><span class="token" style="color: rgb(153, 153, 153);">}</span><span>
</span>
<span>           </span><span class="token" style="color: rgb(0, 119, 170);">if</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">(</span><span>actualKey</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span>length </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">&lt;</span><span> length</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span>
</span><span>               </span><span class="token" style="color: rgb(0, 119, 170);">for</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">(</span><span>i </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> actualKey</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span>length</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span> i </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">&lt;</span><span> keyArr</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span>length</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span> </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">++</span><span>i</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span>
</span><span>                   keyArr</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span>i</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span> </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">0</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>               </span><span class="token" style="color: rgb(153, 153, 153);">}</span><span>
</span><span>           </span><span class="token" style="color: rgb(153, 153, 153);">}</span><span>
</span>
<span>           </span><span class="token" style="color: rgb(0, 119, 170);">byte</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span> kIpadXorResult </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">new</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">byte</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span>length</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span>
<span>           </span><span class="token" style="color: rgb(0, 119, 170);">for</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">(</span><span>i </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">0</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span> i </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">&lt;</span><span> length</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span> </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">++</span><span>i</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span>
</span><span>               kIpadXorResult</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span>i</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span> </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(0, 119, 170);">byte</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">(</span><span>keyArr</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span>i</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span> </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">^</span><span> ipad</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span>i</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>           </span><span class="token" style="color: rgb(153, 153, 153);">}</span><span>
</span>
<span>           </span><span class="token" style="color: rgb(0, 119, 170);">byte</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span> firstAppendResult </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">new</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">byte</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span>kIpadXorResult</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span>length </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">+</span><span> data</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span>length</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span>
<span>           </span><span class="token" style="color: rgb(0, 119, 170);">for</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">(</span><span>i </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">0</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span> i </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">&lt;</span><span> kIpadXorResult</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span>length</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span> </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">++</span><span>i</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span>
</span><span>               firstAppendResult</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span>i</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span> </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> kIpadXorResult</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span>i</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>           </span><span class="token" style="color: rgb(153, 153, 153);">}</span><span>
</span>
<span>           </span><span class="token" style="color: rgb(0, 119, 170);">for</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">(</span><span>i </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">0</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span> i </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">&lt;</span><span> data</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span>length</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span> </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">++</span><span>i</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span>
</span><span>               firstAppendResult</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span>i </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">+</span><span> keyArr</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span>length</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span> </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> data</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span>i</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>           </span><span class="token" style="color: rgb(153, 153, 153);">}</span><span>
</span>
<span>           </span><span class="token" style="color: rgb(0, 119, 170);">byte</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span> firstHashResult </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">md5</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span>firstAppendResult</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>           </span><span class="token" style="color: rgb(0, 119, 170);">byte</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span> kOpadXorResult </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">new</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">byte</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span>length</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span>
<span>           </span><span class="token" style="color: rgb(0, 119, 170);">for</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">(</span><span>i </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">0</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span> i </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">&lt;</span><span> length</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span> </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">++</span><span>i</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span>
</span><span>               kOpadXorResult</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span>i</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span> </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(0, 119, 170);">byte</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">(</span><span>keyArr</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span>i</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span> </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">^</span><span> opad</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span>i</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>           </span><span class="token" style="color: rgb(153, 153, 153);">}</span><span>
</span>
<span>           </span><span class="token" style="color: rgb(0, 119, 170);">byte</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span> secondAppendResult </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">new</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">byte</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span>kOpadXorResult</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span>length </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">+</span><span> firstHashResult</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span>length</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span>
<span>           </span><span class="token" style="color: rgb(0, 119, 170);">for</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">(</span><span>i </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">0</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span> i </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">&lt;</span><span> kOpadXorResult</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span>length</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span> </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">++</span><span>i</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span>
</span><span>               secondAppendResult</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span>i</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span> </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> kOpadXorResult</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span>i</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>           </span><span class="token" style="color: rgb(153, 153, 153);">}</span><span>
</span>
<span>           </span><span class="token" style="color: rgb(0, 119, 170);">for</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">(</span><span>i </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">0</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span> i </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">&lt;</span><span> firstHashResult</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span>length</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span> </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">++</span><span>i</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span>
</span><span>               secondAppendResult</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span>i </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">+</span><span> keyArr</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span>length</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span> </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> firstHashResult</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span>i</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>           </span><span class="token" style="color: rgb(153, 153, 153);">}</span><span>
</span>
<span>           </span><span class="token" style="color: rgb(0, 119, 170);">byte</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span> hmacMd5Bytes </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">md5</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span>secondAppendResult</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>           </span><span class="token" style="color: rgb(0, 119, 170);">return</span><span> hmacMd5Bytes</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>       </span><span class="token" style="color: rgb(153, 153, 153);">}</span><span>
</span>
<span>       </span><span class="token" style="color: rgb(0, 119, 170);">private</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">static</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">byte</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">md5</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(0, 119, 170);">byte</span><span class="token" style="color: rgb(153, 153, 153);">[</span><span class="token" style="color: rgb(153, 153, 153);">]</span><span> str</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span> </span><span class="token" style="color: rgb(0, 119, 170);">throws</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">NoSuchAlgorithmException</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span>
</span><span>           </span><span class="token" style="color: rgb(221, 74, 104);">MessageDigest</span><span> md </span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">=</span><span> </span><span class="token" style="color: rgb(221, 74, 104);">MessageDigest</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">getInstance</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(102, 153, 0);">"MD5"</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>           md</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">update</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span>str</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>           </span><span class="token" style="color: rgb(0, 119, 170);">return</span><span> md</span><span class="token" style="color: rgb(153, 153, 153);">.</span><span class="token" style="color: rgb(221, 74, 104);">digest</span><span class="token" style="color: rgb(153, 153, 153);">(</span><span class="token" style="color: rgb(153, 153, 153);">)</span><span class="token" style="color: rgb(153, 153, 153);">;</span><span>
</span><span>       </span><span class="token" style="color: rgb(153, 153, 153);">}</span><span>
</span><span>   </span><span class="token" style="color: rgb(153, 153, 153);">}</span></code></div></pre>
            <h1 id="8、附录">8、附录</h1>
            <h3 id="8.1 小桔油站品牌">8.1 小桔油站品牌</h3>
            <pre><div style="color: black; background: rgb(245, 242, 240); text-shadow: white 0px 1px; font-family: Consolas, Monaco, &quot;Andale Mono&quot;, &quot;Ubuntu Mono&quot;, monospace; font-size: 1em; text-align: left; white-space: pre; word-spacing: normal; word-break: normal; overflow-wrap: normal; line-height: 1.5; tab-size: 4; hyphens: none; padding: 1em; margin: 0.5em 0px; overflow: auto;"><code class="language-json" style="white-space: pre; color: black; background: none; text-shadow: white 0px 1px; font-family: Consolas, Monaco, &quot;Andale Mono&quot;, &quot;Ubuntu Mono&quot;, monospace; font-size: 1em; text-align: left; word-spacing: normal; word-break: normal; overflow-wrap: normal; line-height: 1.5; tab-size: 4; hyphens: none;"><span class="token" style="color: rgb(102, 153, 0);">"中航石油"</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">,</span><span class="token" style="color: rgb(102, 153, 0);">"昆仑石油"</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"中能石化"</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"中国航油"</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"中海石油"</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"湘中石油"</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"中海石化"</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"加德士"</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"中润连锁"</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"闽海石化"</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"中海能源"</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"美福石油"</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"龙禹石油"</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"连天红"</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"中侨能源"</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"中源石化"</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"中正石油"</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"中福石油"</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"中鲁石化"</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"海源发石油"</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"苏油石化"</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"供销石油"</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"金盾石化"</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"中国海油"</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"中惠泽"</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"安邦"</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"中港石化"</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"中海新能源"</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"众诚连锁"</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"悦孚石化"</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"强林石化"</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"壳牌"</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"长乐CNG"</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"新奥燃气"</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"粤美特"</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"万顺达"</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"东部石油"</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"中图能源"</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"中化石油"</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"冠德石油"</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"BP"</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"道达尔"</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"云南石化"</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"和顺石油"</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"中国石油"</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"中国石化"</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"宝塔石化"</span></code></div></pre>
            <h3 id="8.2 油号映射关系">8.2 油号映射关系</h3>
            <pre><div style="color: black; background: rgb(245, 242, 240); text-shadow: white 0px 1px; font-family: Consolas, Monaco, &quot;Andale Mono&quot;, &quot;Ubuntu Mono&quot;, monospace; font-size: 1em; text-align: left; white-space: pre; word-spacing: normal; word-break: normal; overflow-wrap: normal; line-height: 1.5; tab-size: 4; hyphens: none; padding: 1em; margin: 0.5em 0px; overflow: auto;"><code class="language-json" style="white-space: pre; color: black; background: none; text-shadow: white 0px 1px; font-family: Consolas, Monaco, &quot;Andale Mono&quot;, &quot;Ubuntu Mono&quot;, monospace; font-size: 1em; text-align: left; word-spacing: normal; word-break: normal; overflow-wrap: normal; line-height: 1.5; tab-size: 4; hyphens: none;"><span class="token" style="color: rgb(153, 153, 153);">[</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">"itemName"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"CNG天然气"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">"spuId"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">4884362250503325458</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">}</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">"itemName"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"LPG"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">"spuId"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">4998251560540833545</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">}</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">"itemName"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"优途95#"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">"spuId"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">4947544671981405779</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">}</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">"itemName"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"92#"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">"spuId"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">4879454965524071453</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">}</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">"itemName"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"95#"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">"spuId"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">4879454965540848671</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">}</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">"itemName"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"89#"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">"spuId"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">4879454965494711323</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">}</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">"itemName"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"98#"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">"spuId"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">4879454965557625889</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">}</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">{</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">"itemName"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(102, 153, 0);">"0#"</span><span class="token" style="color: rgb(153, 153, 153);">,</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">"spuId"</span><span class="token" style="color: rgb(154, 110, 58); background: rgba(255, 255, 255, 0.5);">:</span><span> </span><span class="token" style="color: rgb(153, 0, 85);">4879454965574403107</span><span> </span><span class="token" style="color: rgb(153, 153, 153);">}</span><span class="token" style="color: rgb(153, 153, 153);">]</span></code></div></pre>
            <p>文档变更记录</p>
            <ul>
                <li>2022.10.25：初始版本</li>
            </ul>
        </div>
    </div>
    <div class="doc-anchor-wrap">
        <div>
            <div class="">
                <div class="ant-anchor-wrapper" style="max-height: 100vh;">
                    <div class="ant-anchor">
                        <div class="ant-anchor-ink">
                            <span class="ant-anchor-ink-ball"></span>
                        </div>
                        <div class="ant-anchor-link">
                            <a class="ant-anchor-link-title" href="#1、接入准备" title="1、接入准备">1、接入准备</a>
                            <div class="ant-anchor-link">
                                <a class="ant-anchor-link-title" href="#1.1 名词解释" title="1.1 名词解释">1.1 名词解释</a>
                            </div>
                        </div>
                        <div class="ant-anchor-link">
                            <a class="ant-anchor-link-title" href="#2、服务端开发推荐步骤" title="2、服务端开发推荐步骤">2、服务端开发推荐步骤</a>
                        </div>
                        <div class="ant-anchor-link">
                            <a class="ant-anchor-link-title" href="#3、系统交互流程" title="3、系统交互流程">3、系统交互流程</a>
                            <div class="ant-anchor-link">
                                <a class="ant-anchor-link-title" href="#3.1 用户交易阶段接口交互图" title="3.1 用户交易阶段接口交互图">3.1 用户交易阶段接口交互图</a>
                            </div>
                            <div class="ant-anchor-link">
                                <a class="ant-anchor-link-title" href="#3.2 小桔详情页登录状态" title="3.2 小桔详情页登录状态">3.2 小桔详情页登录状态</a>
                            </div>
                            <div class="ant-anchor-link">
                                <a class="ant-anchor-link-title" href="#3.3 退款流程" title="3.3 退款流程">3.3 退款流程</a>
                            </div>
                        </div>
                        <div class="ant-anchor-link">
                            <a class="ant-anchor-link-title" href="#4、数据加密、验签、token" title="4、数据加密、验签、token">4、数据加密、验签、token</a>
                        </div>
                        <div class="ant-anchor-link">
                            <a class="ant-anchor-link-title" href="#5、接口列表（A.小桔实现）" title="5、接口列表（A.小桔实现）">5、接口列表（A.小桔实现）</a>
                            <div class="ant-anchor-link">
                                <a class="ant-anchor-link-title" href="#A1. 查询 Token" title="A1. 查询 Token">A1. 查询 Token</a>
                            </div>
                            <div class="ant-anchor-link">
                                <a class="ant-anchor-link-title" href="#A2. 油站列表查询" title="A2. 油站列表查询">A2. 油站列表查询</a>
                            </div>
                            <div class="ant-anchor-link">
                                <a class="ant-anchor-link-title" href="#A3. 油站实时价格批量查询" title="A3. 油站实时价格批量查询">A3. 油站实时价格批量查询</a>
                            </div>
                            <div class="ant-anchor-link">
                                <a class="ant-anchor-link-title" href="#A4.  跳转地址查询" title="A4.  跳转地址查询">A4. 跳转地址查询</a>
                            </div>
                            <div class="ant-anchor-link">
                                <a class="ant-anchor-link-title" href="#A5. 订单批量查询" title="A5. 订单批量查询">A5. 订单批量查询</a>
                            </div>
                            <div class="ant-anchor-link">
                                <a class="ant-anchor-link-title" href="#A6. 订单查询" title="A6. 订单查询">A6. 订单查询</a>
                            </div>
                        </div>
                        <div class="ant-anchor-link">
                            <a class="ant-anchor-link-title" href="#6、接口列表(B. 接入方实现)" title="6、接口列表(B. 接入方实现)">6、接口列表(B. 接入方实现)</a>
                            <div class="ant-anchor-link">
                                <a class="ant-anchor-link-title" href="#B1. 订单回调通知" title="B1. 订单回调通知">B1. 订单回调通知</a>
                            </div>
                        </div>
                        <div class="ant-anchor-link">
                            <a class="ant-anchor-link-title" href="#7、请求数据处理" title="7、请求数据处理">7、请求数据处理</a>
                            <div class="ant-anchor-link">
                                <a class="ant-anchor-link-title" href="#7.1 加密" title="7.1 加密">7.1 加密</a>
                            </div>
                            <div class="ant-anchor-link">
                                <a class="ant-anchor-link-title" href="#7.2 时间戳" title="7.2 时间戳">7.2 时间戳</a>
                            </div>
                            <div class="ant-anchor-link">
                                <a class="ant-anchor-link-title" href="#7.3 验签" title="7.3 验签">7.3 验签</a>
                            </div>
                            <div class="ant-anchor-link">
                                <a class="ant-anchor-link-title" href="#7.4 Token" title="7.4 Token">7.4 Token</a>
                            </div>
                            <div class="ant-anchor-link">
                                <a class="ant-anchor-link-title" href="#7.5 完整请求举例" title="7.5 完整请求举例">7.5 完整请求举例</a>
                            </div>
                            <div class="ant-anchor-link">
                                <a class="ant-anchor-link-title" href="#7.6 响应状态码" title="7.6 响应状态码">7.6 响应状态码</a>
                            </div>
                            <div class="ant-anchor-link">
                                <a class="ant-anchor-link-title" href="#7.7 接口安全方案代码示例（In Java）" title="7.7 接口安全方案代码示例（In Java）">7.7 接口安全方案代码示例（In Java）</a>
                            </div>
                        </div>
                        <div class="ant-anchor-link">
                            <a class="ant-anchor-link-title" href="#8、附录" title="8、附录">8、附录</a>
                            <div class="ant-anchor-link">
                                <a class="ant-anchor-link-title" href="#8.1 小桔油站品牌" title="8.1 小桔油站品牌">8.1 小桔油站品牌</a>
                            </div>
                            <div class="ant-anchor-link">
                                <a class="ant-anchor-link-title" href="#8.2 油号映射关系" title="8.2 油号映射关系">8.2 油号映射关系</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>
