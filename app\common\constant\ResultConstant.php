<?php
declare(strict_types = 1);
namespace app\common\constant;

/**
 * 全局返回常量
 * <AUTHOR> <<EMAIL>>
 * @package app\common\constant
 */
class ResultConstant
{
    // 成功状态码（和前端组件定义的保持一致都为0。后期尽量不要更改）
    public const RESULT_SUCCESS_STATUS = 1;

    // 失败状态
    public const RESULT_FAILED_STATUS = 0;

    // 成功编码（和前端组件定义的保持一致都为0。后期尽量不要更改）
    public const RESULT_SUCCESS_CODE = 0;

    // 失败编码
    public const RESULT_FAILED_CODE = 100;
}
