<?php
declare(strict_types = 1);

namespace app\system\model;

use think\db\Query;

/**
 * 应用模块模型
 * <AUTHOR> <<EMAIL>>
 *@package app\system\model
 */
class App extends BaseModel
{
    /**
     * 表名
     * @var string
     */
    protected $name = 'system_app';

    /**
     * 设置字段信息
     * @var string[]
     */
    protected $schema = [
        'id'          => 'int',
        'name'        => 'string',
        'code'        => 'string',
        'version'     => 'string',
        'description' => 'string',
        'author'      => 'string',
        'website'     => 'string',
        'status'      => 'int',
        'create_at'   => 'int',
        'update_at'   => 'int',
    ];

    /**
     * 定义索引
     * @var array|string[]
     */
    protected array $indexes = [
        'code'   => 'unique',
        'status' => 'index',
    ];

    /**
     * 自动完成
     * @var array|string[]
     */
    protected array $auto = ['update_at'];

    protected $insert = ['create_at'];

    /**
     * 修改器 create_at
     * @return int
     */
    public function setCreateAtAttr(): int
    {
        return time();
    }

    /**
     * 修改器 update_at
     * @return int
     */
    public function setUpdateAtAttr(): int
    {
        return time();
    }

    /**
     * 获取器
     * @param $value
     * @param $data
     * @return string
     */
    public function getStatusTextAttr($value, $data): string
    {
        $status = [0 => '禁用', 1 => '启用'];

        return $status[$data['status']] ?? '未知';
    }

    /**
     * 查询作用域
     * @param Query $query
     * @return \think\db\Query
     */
    public function scopeEnabled(Query $query): Query
    {
        return$query->where('status', 1);
    }
}
