<?php
declare(strict_types = 1);
namespace app\system\constant;

use app\common\constant\CommonConstant;

/**
 * 应用模块相关常量 (20000-29999)
 * <AUTHOR> <<EMAIL>>
 * @package app\system\constant
 */
class AppModuleConstant extends CommonConstant
{
    /**
     * 不进行应用检查的模块
     */
    public const NO_CHECK_APPS = ['common', 'system', 'api'];

    // 应用基础错误 (20000-20099)
    public const APP_NOT_FOUND = ['code' => 20000, 'message' => '应用不存在'];

    public const APP_INSTALL_FAILED = ['code' => 20001, 'message' => '应用安装失败'];

    public const APP_UNINSTALL_FAILED = ['code' => 20002, 'message' => '应用卸载失败'];

    public const APP_ENABLE_FAILED = ['code' => 20003, 'message' => '应用启用失败'];

    public const APP_DISABLE_FAILED = ['code' => 20004, 'message' => '应用禁用失败'];

    public const APP_UPGRADE_FAILED = ['code' => 20005, 'message' => '应用升级失败'];

    public const APP_HAS_DEPENDENCIES = ['code' => 20006, 'message' => '应用存在依赖'];

    public const APP_BATCH_UPDATE_STATUS_FAILED = ['code' => 20007, 'message' => '批量更新状态失败'];

    public const APP_INVALID_STATUS = ['code' => 20008, 'message' => '无效的应用状态值'];

    public const APP_ALREADY_INSTALLED = ['code' => 20009, 'message' => '应用已安装'];

    public const APP_INFO_INVALID = ['code' => 20010, 'message' => '应用信息无效'];

    // 应用状态错误 (20100-20199)
    public const APP_NOT_INSTALLED = ['code' => 20100, 'message' => '应用未安装，请安装后使用'];

    public const APP_DISABLED = ['code' => 20101, 'message' => '应用已禁用'];

    public const APP_VERSION_LOW = ['code' => 20102, 'message' => '应用版本过低'];

    public const APP_VERSION_INVALID = ['code' => 20103, 'message' => '无效的版本号格式'];

    // 应用配置错误 (20200-20299)
    public const CONFIG_NOT_FOUND = ['code' => 20200, 'message' => '配置文件不存在'];

    public const CONFIG_SAVE_FAILED = ['code' => 20201, 'message' => '配置保存失败'];

    public const CONFIG_INVALID = ['code' => 20202, 'message' => '配置格式无效'];

    // 菜单相关错误 (20300-20399)
    public const MENU_FILE_NOT_FOUND = ['code' => 20300, 'message' => '菜单配置文件不存在'];

    public const MENU_CONFIG_INVALID = ['code' => 20301, 'message' => '菜单配置无效'];

    public const MENU_FIELD_REQUIRED = ['code' => 20302, 'message' => '菜单字段必填'];

    public const MENU_CREATE_FAILED = ['code' => 20303, 'message' => '菜单创建失败'];

    // 依赖相关错误 (20400-20499)
    public const DEPENDENCY_CHECK_FAILED = ['code' => 20400, 'message' => '依赖检查失败'];

    public const DEPENDENCY_VERSION_MISMATCH = ['code' => 20401, 'message' => '依赖版本不匹配'];

    // 缓存相关错误 (20500-20599)
    public const CACHE_SET_FAILED = ['code' => 20500, 'message' => '缓存设置失败'];

    public const CACHE_GET_FAILED = ['code' => 20501, 'message' => '缓存获取失败'];

    /**
     * 根据code获取message
     * @param int|string $code
     * @return string
     */
    public static function getMessage(int|string $code): string
    {
        foreach (static::getConstants() as $constant) {
            if ($constant['code'] === $code) {
                return $constant['message'];
            }
        }

        return '未知错误';
    }

    /**
     * 获取常量信息
     */
    private static function getConstants(): array
    {
        $ref = new \ReflectionClass(static::class);

        return $ref->getConstants();
    }
}
