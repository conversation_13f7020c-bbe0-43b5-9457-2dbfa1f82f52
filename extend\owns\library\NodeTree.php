<?php
declare(strict_types = 1);

namespace owns\library;

/**
 * 树结构生成类
 * 用于将一维数组数据转换成树形结构或列表结构
 * <AUTHOR> <<EMAIL>>
 */
class NodeTree
{
    /**
     * @var ?self 对象实例
     */
    protected static ?self $instance = null;

    /**
     * 配置参数
     * @var array<string, mixed>
     */
    protected static array $config = [
        'id'    => 'id',    // id名称
        'pid'   => 'pid',   // pid名称
        'title' => 'title', // 标题名称
        'child' => 'children', // 子元素键名
        'html'  => '┝ ',    // 层级标记
        'step'  => 4,       // 层级步进数量
    ];

    /**
     * 缓存数组
     * @var array<string, array>
     */
    protected static array $cache = [
        'child_ids'    => [],
        'parent_ids'   => [],
        'child_nodes'  => [],
        'parent_nodes' => [],
    ];

    /**
     * 架构函数
     * @param array<string, mixed> $config 配置参数
     */
    public function __construct(array $config = [])
    {
        self::$config = array_merge(self::$config, $config);
    }

    /**
     * 配置参数
     * @param array<string, mixed> $config 配置参数
     * @return self 当前实例
     */
    public static function config(array $config = []): self
    {
        if (!empty($config)) {
            self::$config = array_merge(self::$config, $config);
        }

        if (is_null(self::$instance)) {
            self::$instance = new static($config);
        }

        return self::$instance;
    }

    /**
     * 清除缓存
     * @return void
     */
    public static function clearCache(): void
    {
        self::$cache = [
            'child_ids'    => [],
            'parent_ids'   => [],
            'child_nodes'  => [],
            'parent_nodes' => [],
        ];
    }

    /**
     * 将数据集格式化成层次结构
     * @param array $lists 要格式化的数据集
     * @param int $pid 父级id
     * @param int $max_level 最多返回多少层，0为不限制
     * @param int $curr_level 当前层数
     * @return array 树形结构数据
     */
    public static function toLayer(array $lists = [], int $pid = 0, int $max_level = 0, int $curr_level = 0): array
    {
        $trees = [];
        $lists = array_values($lists);

        // 优化：预先建立索引，避免每次循环都要遍历
        $indexed_lists = [];

        foreach ($lists as $item) {
            $parent_id = $item[self::$config['pid']] ?? null;

            if ($parent_id !== null) {
                $indexed_lists[$parent_id][] = $item;
            }
        }

        // 如果当前层级已达到最大层级限制，直接返回
        if ($max_level > 0 && $curr_level >= $max_level) {
            return $trees;
        }

        // 获取当前父级下的所有子节点
        $children = $indexed_lists[$pid] ?? [];

        foreach ($children as $child) {
            $child_id = $child[self::$config['id']];
            // 递归获取子节点的子节点
            $sub_children = self::toLayer($lists, $child_id, $max_level, $curr_level + 1);

            // 如果有子节点，则添加到当前节点
            if (!empty($sub_children)) {
                $child[self::$config['child']] = $sub_children;
            }

            $trees[] = $child;
        }

        return $trees;
    }

    /**
     * 将数据集格式化成列表结构
     * @param array $lists 要格式化的数据集
     * @param int $pid 父级id
     * @param int $level 级别
     * @return array 列表结构(一维数组)
     */
    public static function toList(array $lists = [], int $pid = 0, int $level = 0): array
    {
        $trees = [];

        // 优化：预先建立索引，避免每次递归都要遍历
        $indexed_lists = [];

        foreach ($lists as $key => $value) {
            $parent_id = $value[self::$config['pid']] ?? null;

            if ($parent_id !== null) {
                $indexed_lists[$parent_id][] = ['key' => $key, 'value' => $value];
            }
        }

        // 获取当前父级下的所有子节点
        $children = $indexed_lists[$pid] ?? [];

        foreach ($children as $item) {
            $key   = $item['key'];
            $value = $item['value'];

            $title_prefix           = str_repeat('&nbsp;', $level * self::$config['step']) . self::$config['html'];
            $value['level']         = $level + 1;
            $value['title_prefix']  = $level == 0 ? '' : $title_prefix;
            $value['title_display'] = $level == 0 ? $value[self::$config['title']] : $title_prefix . $value[self::$config['title']];

            $trees[] = $value;
            unset($lists[$key]);

            // 递归获取子节点
            $sub_trees = self::toList($lists, $value[self::$config['id']], $level + 1);
            $trees     = array_merge($trees, $sub_trees);
        }

        return $trees;
    }

    /**
     * 根据子节点返回所有父节点
     * @param array $lists 数据集
     * @param string|int $id 子节点id
     * @return array 所有父节点数组
     */
    public static function getParents(array $lists = [], $id = ''): array
    {
        // 使用缓存提高性能
        $cache_key = md5(serialize($lists) . '_' . $id);

        if (isset(self::$cache['parent_nodes'][$cache_key])) {
            return self::$cache['parent_nodes'][$cache_key];
        }

        // 优化：预先建立索引，避免每次递归都要遍历
        $indexed_lists = [];

        foreach ($lists as $item) {
            $indexed_lists[$item[self::$config['id']]] = $item;
        }

        $trees      = [];
        $current_id = $id;

        while (isset($indexed_lists[$current_id])) {
            $current_node = $indexed_lists[$current_id];
            $trees[]      = $current_node;
            $current_id   = $current_node[self::$config['pid']];

            // 防止无限循环（如果数据中存在循环引用）
            if ($current_id == $id) {
                break;
            }
        }

        // 存入缓存
        self::$cache['parent_nodes'][$cache_key] = $trees;

        return $trees;
    }

    /**
     * 获取所有父节点id
     * @param array $lists 数据集
     * @param mixed $id 子节点id
     * @param bool $sort 是否需要排序
     * @param bool $current 是否需要当前节点
     * @return array 所有父节点id数组
     */
    public static function getParentIds(array $lists, mixed $id, bool $sort = false, bool $current = true): array
    {
        // 使用缓存提高性能
        $cache_key = md5(serialize($lists) . '_' . $id . '_' . (int)$sort . '_' . (int)$current);

        if (isset(self::$cache['parent_ids'][$cache_key])) {
            return self::$cache['parent_ids'][$cache_key];
        }

        // 优化：预先建立索引，避免每次递归都要遍历
        $indexed_lists = [];

        foreach ($lists as $item) {
            $indexed_lists[$item[self::$config['id']]] = $item;
        }

        $result     = [];
        $current_id = $id;

        if ($current === true && isset($indexed_lists[$current_id])) {
            $result[] = $current_id;
        }

        while (isset($indexed_lists[$current_id])) {
            $parent_id = $indexed_lists[$current_id][self::$config['pid']];

            if ($parent_id && isset($indexed_lists[$parent_id])) {
                $result[]   = $parent_id;
                $current_id = $parent_id;

                // 防止无限循环（如果数据中存在循环引用）
                if ($current_id == $id) {
                    break;
                }
            } else {
                break;
            }
        }

        if ($sort === true) {
            sort($result);
        }

        // 存入缓存
        self::$cache['parent_ids'][$cache_key] = $result;

        return $result;
    }

    /**
     * 获取所有子节点id
     * @param array $lists 数据集
     * @param mixed $pid 父级id
     * @return array 所有子节点id数组
     */
    public static function getChildIds(array $lists = [], mixed $pid = ''): array
    {
        // 使用缓存提高性能
        $cache_key = md5(serialize($lists) . '_' . $pid);

        if (isset(self::$cache['child_ids'][$cache_key])) {
            return self::$cache['child_ids'][$cache_key];
        }

        // 优化：预先建立索引，避免每次递归都要遍历
        $indexed_lists = [];

        foreach ($lists as $item) {
            $parent_id = $item[self::$config['pid']] ?? null;

            if ($parent_id !== null) {
                $indexed_lists[$parent_id][] = $item;
            }
        }

        $result = [];
        $queue  = [$pid];

        while (!empty($queue)) {
            $current_pid = array_shift($queue);

            if (isset($indexed_lists[$current_pid])) {
                foreach ($indexed_lists[$current_pid] as $item) {
                    $child_id = $item[self::$config['id']];
                    $result[] = $child_id;
                    $queue[]  = $child_id;
                }
            }
        }

        // 存入缓存
        self::$cache['child_ids'][$cache_key] = $result;

        return $result;
    }

    /**
     * 获取所有子节点
     * @param array $lists 数据集
     * @param mixed $pid 父级id
     * @return array 所有子节点数组
     */
    public static function getChild(array $lists = [], mixed $pid = ''): array
    {
        // 使用缓存提高性能
        $cache_key = md5(serialize($lists) . '_' . $pid);

        if (isset(self::$cache['child_nodes'][$cache_key])) {
            return self::$cache['child_nodes'][$cache_key];
        }

        // 优化：预先建立索引，避免每次递归都要遍历
        $indexed_lists = [];

        foreach ($lists as $item) {
            $parent_id = $item[self::$config['pid']] ?? null;

            if ($parent_id !== null) {
                $indexed_lists[$parent_id][] = $item;
            }
        }

        $result = [];
        $queue  = [$pid];

        while (!empty($queue)) {
            $current_pid = array_shift($queue);

            if (isset($indexed_lists[$current_pid])) {
                foreach ($indexed_lists[$current_pid] as $item) {
                    $result[] = $item;
                    $queue[]  = $item[self::$config['id']];
                }
            }
        }

        // 存入缓存
        self::$cache['child_nodes'][$cache_key] = $result;

        return $result;
    }
}
