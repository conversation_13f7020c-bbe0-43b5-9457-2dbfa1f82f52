-- 表结构: system_user
CREATE TABLE `system_user` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) NOT NULL DEFAULT '' COMMENT '账号',
  `realname` varchar(20) NOT NULL DEFAULT '' COMMENT '姓名',
  `phone` varchar(16) NOT NULL DEFAULT '' COMMENT '用户手机号',
  `is_super` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否超管',
  `email` varchar(50) NOT NULL DEFAULT '' COMMENT '用户邮箱',
  `gender` tinyint(3) unsigned NOT NULL DEFAULT '2' COMMENT '性别(0:女,1:男,2:保密)',
  `birthday` date NOT NULL DEFAULT '1990-01-01' COMMENT '生日',
  `avatar` varchar(20) NOT NULL DEFAULT '' COMMENT '用户头像',
  `password` varchar(255) NOT NULL DEFAULT '' COMMENT '用户密码',
  `salt` char(16) NOT NULL DEFAULT '' COMMENT '加密的随机字符串',
  `force_change_password` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否强制修改密码（0:不强制,1强制）',
  `theme` varchar(20) NOT NULL DEFAULT '' COMMENT '主题风格',
  `signature` varchar(500) NOT NULL DEFAULT '' COMMENT '用户签名',
  `score` bigint(20) NOT NULL DEFAULT '0' COMMENT '用户积分',
  `balance` bigint(20) NOT NULL DEFAULT '0' COMMENT '用户余额',
  `reg_ip` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '注册ip',
  `last_at` int(11) NOT NULL DEFAULT '0' COMMENT '上次登录时间',
  `last_ip` bigint(20) NOT NULL DEFAULT '0' COMMENT '上次登录ip',
  `login_times` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '登录次数',
  `create_at` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_at` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  `delete_at` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '删除时间',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '用户状态 -1:软删除用户 0:已冻结 1:正常用户',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='系统用户表';
