<?php
declare(strict_types = 1);
namespace app\system\service;

use app\common\constant\ResultConstant;
use app\common\service\ClientService;
use app\common\service\FingerService;
use app\common\service\UserAgentService;
use app\system\constant\AccountConstant;
use app\system\constant\TokenConstant;
use app\system\model\Permission as PermissionModel;
use app\system\model\PermissionRole as PermissionRoleModel;
use app\system\model\User as UserModel;
use app\system\repository\RoleUserRepository;
use app\system\repository\UserRepository;
use think\facade\Cache;

/**
 * 账户服务类
 * <AUTHOR> <<EMAIL>>
 * @package app\system\service
 */
class AccountService extends BaseService
{
    /**
     * 登录方式常量
     */
    public const LOGIN_TYPES = [
        'username' => [ // 账号+密码登录
            'account' => '账号',
            'code'    => '密码',
            'field'   => 'username', // 验证字段
        ],
        'email' => [ // 邮箱+密码登录
            'account' => '邮箱',
            'code'    => '密码',
            'field'   => 'email', // 验证字段
        ],
        'phone' => [ // 手机号+密码登录
            'account' => '手机号',
            'code'    => '密码',
            'field'   => 'phone', // 验证字段
        ],
        'mobile' => [ // 手机号短信验证
            'account' => '手机号',
            'code'    => '验证码',
            'field'   => 'phone', // 验证字段
        ],
        'refresh_token' => [ // refresh_token登录（一般用户刷新token时使用）
            'account' => 'refresh_token',
            'code'    => 'refresh_token',
            'field'   => 'id', // 验证字段
        ],
    ];

    /**
     * 日志类型常量
     */
    private const LOG_TYPE = [
        'LOGIN_SUCCESS' => '登录成功',
        'LOGIN_FAILED'  => '登录失败',
        'LOGIN_ATTEMPT' => '尝试登录失败',
    ];

    /**
     * 基础验证
     * @param string $login_type 登录方式
     * @param string|null $account
     * @param string $code
     * @return array
     */
    public static function baseValidate(string $login_type, ?string $account, string $code): array
    {
        if ($login_type != 'refresh_token' && empty($account)) {
            return [
                'code'    => ResultConstant::RESULT_FAILED_CODE,
                'message' => self::LOGIN_TYPES[$login_type]['account'] . '不能为空',
            ];
        }

        if ($login_type != 'refresh_token' && empty($code)) {
            return [
                'code'    => ResultConstant::RESULT_FAILED_CODE,
                'message' => self::LOGIN_TYPES[$login_type]['code'] . '不能为空',
            ];
        }

        return ['code' => ResultConstant::RESULT_SUCCESS_CODE];
    }

    /**
     * 登录验证统一方法
     * @param string $login_type 登录方式
     * @param string|null $account 对应账户
     * @param string $code
     * @throws \Psr\SimpleCache\InvalidArgumentException
     * @throws \Random\RandomException
     * @throws \UAParser\Exception\FileNotFoundException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return array
     */
    public static function doLogin(string $login_type = '', ?string $account = '', string $code = ''): array
    {
        // 基础验证
        $validate = self::baseValidate($login_type, $account, $code);

        if ($validate['code'] !== ResultConstant::RESULT_SUCCESS_CODE) {
            return $validate;
        }

        // 根据登录类型调用对应的登录方法
        switch ($login_type) {
            case 'account': // 账号密码登录
            case 'username':
                return self::usernameLogin($account, $code);
            case 'email': // 邮箱密码登录
                return self::emailLogin($account, $code);
            case 'mobile': // 手机号验证码登录
                return self::mobileLogin($account, $code);
            case 'refresh_token': // 刷新token登录
                return self::refreshTokenLogin($code);
            default:
                return ['code' => ResultConstant::RESULT_FAILED_CODE, 'message' => '不支持的登录方式'];
        }
    }

    /**
     * 退出登录统一方法
     * @param int $id 用户ID
     * @return bool
     */
    public static function logout(int $id): bool
    {
        $mode = AuthService::getLoginMode();

        switch ($mode) {
            case AuthService::LOGIN_MODE['SINGLE']:
                // 单点登录时清除所有缓存
                AuthService::removeUserCaches($id);
                break;
            case AuthService::LOGIN_MODE['MULTIPLE_SEPARATE']:
                // 多点独立token时只清除当前指纹Id的token和登录状态
                $finger_id = FingerService::getClientFingerId();
                self::removeUserLoginInfo($id);
                // 清除当前指纹Id的token缓存
                $prefix = AuthService::getUserTokenPrefix($id) . ':' . $finger_id;
                Cache::delete($prefix . AuthService::CACHE_ADMIN_ACCESS_TOKEN_TAG);
                Cache::delete($prefix . AuthService::CACHE_ADMIN_REFRESH_TOKEN_TAG);
                break;
            case AuthService::LOGIN_MODE['MULTIPLE_SHARE']:
                // 多点共享token时只清除当前指纹Id的登录状态
                self::removeUserLoginInfo($id);
                break;
        }

        return true;
    }

    /**
     * 账号密码登录
     * @param string $username 用户名
     * @param string $password 密码
     * @throws \Psr\SimpleCache\InvalidArgumentException
     * @throws \UAParser\Exception\FileNotFoundException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return array
     */
    public static function usernameLogin(string $username, string $password): array
    {
        return self::validatePassword($username, $password, self::LOGIN_TYPES['username']['field']);
    }

    /**
     * 邮箱登录
     * @param string $email 邮箱号
     * @param string $password 密码
     * @throws \Psr\SimpleCache\InvalidArgumentException
     * @throws \UAParser\Exception\FileNotFoundException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return array
     */
    public static function emailLogin(string $email, string $password): array
    {
        if (!is_email($email)) {
            return [
                'code'    => ResultConstant::RESULT_FAILED_CODE,
                'message' => '邮箱号格式不正确',
            ];
        }

        return self::validatePassword($email, $password, self::LOGIN_TYPES['email']['field']);
    }

    /**
     *  手机号验证码登录
     * @param string $mobile 手机号
     * @param string $code 验证码
     * @throws \Random\RandomException
     * @return array
     */
    public static function mobileLogin(string $mobile, string $code): array
    {
        if (!is_mobile($mobile)) {
            return [
                'code'    => ResultConstant::RESULT_FAILED_CODE,
                'message' => '手机号格式不正确',
            ];
        }

        $sms = random_int(10000, 99999);

        if (empty($sms)) {
            return [
                'code'    => ResultConstant::RESULT_FAILED_CODE,
                'message' => '验证码已过期，请重新获取',
            ];
        }

        if ($sms != $code) {
            return [
                'code'    => ResultConstant::RESULT_FAILED_CODE,
                'message' => '验证码错误，请重试',
            ];
        }

        // ::TODO 后期需要做验证码登录
        return [];
    }

    /**
     * RefreshToken登录
     * @param string $token 携带过来的refresh_token
     * @throws \Psr\SimpleCache\InvalidArgumentException
     * @throws \UAParser\Exception\FileNotFoundException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return array
     */
    public static function refreshTokenLogin(string $token): array
    {
        if (empty($token)) {
            return [
                'code'    => TokenConstant::REFRESH_TOKEN_ERROR['code'],
                'message' => TokenConstant::REFRESH_TOKEN_ERROR['message'] . ':[code]',
                'label'   => TokenConstant::REFRESH_TOKEN_ERROR['label'] ?? '',
            ];
        }

        // 验证用户token是否合法、过期并返回携带数据
        $token_info = TokenService::verifyToken($token, 'refresh_token');

        // 返回错误时提示给客户端
        if (isset($token_info['code'])) {
            return [
                'code'    => $token_info['code'],
                'message' => $token_info['message'],
            ];
        }

        $userId = $token_info['uid'] ?? 0;

        if (empty($userId)) {
            return [
                'code'    => TokenConstant::REFRESH_TOKEN_ERROR['code'],
                'message' => TokenConstant::REFRESH_TOKEN_ERROR['message'] . ':[uid]',
                'label'   => TokenConstant::REFRESH_TOKEN_ERROR['label'] ?? '',
            ];
        }

        // 验证refresh_token合法性（如果是单点登录或多点共享，就记录该token到redis、且做比对）
        if (AuthService::getLoginMode() === AuthService::LOGIN_MODE['SINGLE'] || AuthService::getLoginMode() === AuthService::LOGIN_MODE['MULTIPLE_SHARE']) {
            // 从缓存中获取用户refresh_token
            $cache_refresh_token = AuthService::getCacheUserInfo(
                (int)$userId,
                AuthService::CACHE_ADMIN_REFRESH_TOKEN_TAG
            );

            // 说明缓存中没有该用户的缓存信息
            if (empty($cache_refresh_token)) {
                return [
                    'code'    => TokenConstant::REFRESH_TOKEN_INVALID['code'],
                    'message' => TokenConstant::REFRESH_TOKEN_INVALID['message'] . ':[empty]',
                    'label'   => TokenConstant::REFRESH_TOKEN_INVALID['label'] ?? '',
                ];
            }

            // 说明在其它地方登录了
            if ($cache_refresh_token != $token) {
                return [
                    'code'    => TokenConstant::REFRESH_TOKEN_INVALID['code'],
                    'message' => TokenConstant::REFRESH_TOKEN_INVALID['message'] . ':[invalid]',
                    'label'   => TokenConstant::REFRESH_TOKEN_INVALID['label'] ?? '',
                ];
            }
        }

        // 获取用户最新状态
        $userResult = self::getUserInfo($userId, self::LOGIN_TYPES['refresh_token']['field']);

        if ($userResult['code'] !== ResultConstant::RESULT_SUCCESS_CODE) {
            return $userResult;
        }

        return self::handleLogin($userResult['userInfo'], self::LOGIN_TYPES['refresh_token']['field']);
    }

    /**
     * 获取并验证用户基础信息
     * @param mixed $account
     * @param string $login_type
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return array ['code' => int, 'message' => string] | array ['code' => 1, 'data' => array]
     */
    protected static function getUserInfo(mixed $account, string $login_type): array
    {
        $info = UserModel::where([$login_type => $account])->find();

        if (empty($info)) {
            // 记录登录日志
            self::writeLoginLog(null, 'LOGIN_ATTEMPT', $account, $login_type, '', AccountConstant::ACCOUNT_NOT_EXIST);

            return AccountConstant::ACCOUNT_NOT_EXIST;
        }

        $userInfo = $info->toArray();

        return ['code' => ResultConstant::RESULT_SUCCESS_CODE, 'userInfo' => $userInfo];
    }

    /**
     * 检查账号状态
     * @param array $userInfo 用户信息
     * @return array
     */
    protected static function checkAccountStatus(array $userInfo): array
    {
        if ($userInfo['status'] == 0) {
            return AccountConstant::ACCOUNT_FROZEN;
        }

        if ($userInfo['status'] == -1) {
            return AccountConstant::ACCOUNT_DELETED;
        }

        if ($userInfo['status'] != 1) {
            return AccountConstant::ACCOUNT_STATUS_ABNORMAL;
        }

        // 返回验证成功码
        return ['code' => ResultConstant::RESULT_SUCCESS_CODE];
    }

    /**
     * 验证密码
     * @param string $account 对应账号
     * @param string $code 密码
     * @param string $login_type
     * @throws \Psr\SimpleCache\InvalidArgumentException
     * @throws \UAParser\Exception\FileNotFoundException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return array
     */
    protected static function validatePassword(string $account, string $code, string $login_type): array
    {
        // 获取用户信息并验证状态
        $userResult = self::getUserInfo($account, $login_type);

        if ($userResult['code'] !== ResultConstant::RESULT_SUCCESS_CODE) {
            return $userResult;
        }

        $userInfo = $userResult['userInfo'];

        // 验证密码
        if (AuthService::generatePassword($code, $userInfo['salt']) != $userInfo['password']) {
            self::writeLoginLog($userInfo, 'LOGIN_FAILED', $account, $login_type, $code, AccountConstant::ACCOUNT_PASSWORD_INVALID);

            return AccountConstant::ACCOUNT_PASSWORD_INVALID;
        }

        // 处理登录逻辑
        return self::handleLogin($userInfo, $login_type);
    }

    /**
     * 处理登录逻辑
     * @param array $userInfo 用户信息
     * @param string $login_type 登录方式
     * @throws \Psr\SimpleCache\InvalidArgumentException
     * @throws \UAParser\Exception\FileNotFoundException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return array
     */
    protected static function handleLogin(array $userInfo, string $login_type): array
    {
        // 获取当前用户指纹ID
        $fingerId = FingerService::getClientFingerId();

        // 检查用户指纹Id是否被拉黑
        if (OnlineUserService::isUserBlacklisted((int)$userInfo['id'], $fingerId)) {
            // 记录登录日志
            self::writeLoginLog($userInfo, 'LOGIN_FAILED', $userInfo[$login_type], $login_type, '', AccountConstant::ACCOUNT_BLACKLISTED);

            return AccountConstant::ACCOUNT_BLACKLISTED;
        }

        // 账号状态检查
        $statusCheck = self::checkAccountStatus($userInfo);

        if ($statusCheck['code'] !== ResultConstant::RESULT_SUCCESS_CODE) {
            // 记录登录日志
            self::writeLoginLog($userInfo, 'LOGIN_FAILED', $userInfo[$login_type], $login_type, '', $statusCheck);

            return $statusCheck;
        }

        // 获取用户对应的角色组（联合一次查询用户组信息，禁用的不查询）
        $userRoles    = (new RoleUserRepository())->getUserRoles((int)$userInfo['id']);
        $userRolesArr = $userRoles ? array_column($userRoles, 'id') : [];

        // 是否超管
        $is_super = intval($userInfo['is_super']);

        // 无任何权限角色时返回提示（超管时不验证）
        if (!$userRolesArr && $is_super !== 1) {
            // 记录登录日志
            self::writeLoginLog($userInfo, 'LOGIN_FAILED', $userInfo[$login_type], $login_type, '', AccountConstant::ACCOUNT_NO_ROLE);

            return AccountConstant::ACCOUNT_NO_ROLE;
        }

        // 获取用户组对应的所有节点权限列表
        if ($is_super === 0) {
            $userPermissionRole = PermissionRoleModel::where('role_id', 'IN', $userRolesArr)->select()->toArray();

            // 非超管，角色组无任何权限节点时返回提示
            if (empty($userPermissionRole)) {
                // 记录登录日志
                self::writeLoginLog($userInfo, 'LOGIN_FAILED', $userInfo[$login_type], $login_type, '', AccountConstant::ACCOUNT_NO_ROLE_PERMISSION);

                return AccountConstant::ACCOUNT_NO_ROLE_PERMISSION;
            }
        } else {
            $userPermissionRole = [];
        }

        // 将该用户所有的权限节点id存成数组
        $userPermissionArr = array_values(array_unique(array_column($userPermissionRole, 'permission_id')));
        // 查询所在角色组名称（用于用户的岗位身份显示）
        $userRolesName = $userRoles ? array_column($userRoles, 'name') : [];

        // 将用户信息、用户拥有的权限节点存到session
        $userInfoArr = [
            'id'                    => $userInfo['id'],
            'username'              => $userInfo['username'],
            'realname'              => $userInfo['realname'],
            'birthday'              => $userInfo['birthday'],
            'score'                 => $userInfo['score'],
            'balance'               => $userInfo['balance'],
            'avatar'                => $userInfo['avatar'],
            'gender'                => $userInfo['gender'],
            'theme'                 => $userInfo['theme'],
            'signature'             => $userInfo['signature'],
            'phone'                 => $userInfo['phone'],
            'email'                 => $userInfo['email'],
            'is_super'              => $userInfo['is_super'], // 是否超管
            'lockscreen'            => 0, // 登录后取消一切锁屏
            'force_change_password' => $userInfo['force_change_password'], // 是否强制修改密码
            'create_at'             => $userInfo['create_at'], // 注册时间
            'dept_id'               => $userInfo['dept_id'] ?? 0, // 用户所在部门id
            'roles_id'              => $is_super === 1 ? 0 : implode(',', $userRolesArr), // 用户所在角色组ids
            'roles_name'            => $is_super === 1 ? 'Administrators' : implode(',', $userRolesName), // 用户所在角色组名称
            'last_login_ip'         => $userInfo['last_ip'], // 上次登录ip
            'last_login_at'         => date('Y-m-d H:i:s', (int)$userInfo['last_at']), // 上次登录时间
            'login_times'           => $userInfo['login_times'] + 1, // 登录次数
            'login_ip'              => request()->ip(), // 本次登录ip
            'login_at'              => date('Y-m-d H:i:s'), // 本次登录时间
            'login_type'            => $login_type, // 本次登录方式
        ];

        // 根据登录模式处理token存储
        $token_result = AuthService::handleLoginToken((int)$userInfo['id'], $login_type);

        $access_token  = $token_result['access_token']  ?? null;
        $refresh_token = $token_result['refresh_token'] ?? null;

        if ($token_result === false || !$access_token || !$refresh_token) {
            return TokenConstant::ISSUE_TOKEN_FAILED;
        }

        // 用户的个人信息放到redis中
        AuthService::updateCacheUserInfo((int)$userInfo['id'], $userInfoArr, true);
        // 用户的所有权限节点放到redis中
        AuthService::updateCacheUserNodes((int)$userInfo['id'], $userPermissionArr, true);

        // 更新用户的本次登录信息
        (new UserRepository())->updateData($userInfo['id'], [
            'last_ip'     => $userInfoArr['login_ip'],
            'last_at'     => strtotime($userInfoArr['login_at']),
            'login_times' => $userInfoArr['login_times'],
        ]);

        // 卸载敏感字段数据，不传给前端
        unset(
            $userInfoArr['id'],
            $userInfoArr['is_super'],
            $userInfoArr['create_at'],
            $userInfoArr['roles_id'],
            $userInfoArr['lockscreen']
        );

        // 获取菜单数据，超管时为所有状态正常的菜单列表
        if ($is_super === 1) {
            $menu_list = PermissionModel::getRulelist();
        } else {
            $menu_list = $userPermissionArr ? PermissionModel::where([
                ['id', 'IN', $userPermissionArr],
                ['status', '=', 1],
            ])->select()->toArray() : [];
        }

        // 菜单标准输出
        $menu_data       = $menu_list ? MenuService::format($menu_list) : [];
        $permission_data = $menu_list ? MenuService::permissions($menu_list) : [];

        // token过期时间
        $token_expire = AuthService::getTokenExpireTime((int)$userInfo['id']);

        // 返回给客户端的数据
        $result_data = [
            'access_token'       => $access_token,
            'refresh_token'      => $refresh_token,
            'expires_in'         => $token_expire['access_token_expire'],
            'expires_at'         => time() + $token_expire['access_token_expire'],
            'refresh_expires_in' => $token_expire['refresh_token_expire'],
            'user_info'          => $userInfoArr,
            'menu_data'          => $menu_data,
            'permission_data'    => $permission_data,
            'login_type'         => $login_type,
        ];

        // 如果需要修改密码，登录后让其强行修改密码
        if ($userInfo['force_change_password'] == 1) {
            $result_data['force_change_password'] = 1;
        }

        // 登录成功信息
        $result = AccountConstant::ACCOUNT_LOGIN_SUCCESS;

        $result['data'] = $result_data;

        // 存储用户本次登录信息到有序集合缓存
        self::storeUserLoginInfo((int)$userInfo['id'], $userInfoArr);

        // 登录成功后记录在线用户
        OnlineUserService::addOnlineUser((int)$userInfo['id']);

        // 登录成功记录日志
        self::writeLoginLog($userInfo, 'LOGIN_SUCCESS', $userInfo[$login_type], $login_type, '', $result);

        return $result;
    }

    /**
     * 存储用户本次登录信息缓存
     * @param int $userId
     * @param array $userInfo
     * @throws \UAParser\Exception\FileNotFoundException
     * @return void
     */
    public static function storeUserLoginInfo(int $userId, array $userInfo): void
    {
        $fingerId = FingerService::getClientFingerId(); // 指纹Id

        // 实例化UA服务类
        $userAgent = UserAgentService::getInstance();

        $activeInfo = [
            'login_type'    => $userInfo['login_type'], // 本次登录方式
            'login_at'      => $userInfo['login_at'], // 本次登录时间
            'login_ip'      => $userInfo['login_ip'] ?: request()->ip(), // 本次登录ip
            'last_login_at' => $userInfo['last_login_at'], // 上次登录时间
            'last_login_ip' => $userInfo['last_login_ip'] ?: null, // 上次登录ip
            'login_times'   => $userInfo['login_times'], // 登录次数
            'login_mode'    => AuthService::getLoginMode(), // 登录模式（单点、多点独享、多点共享）
            'client_id'     => ClientService::getClientId(), // 来自哪个端
            'browser'       => $userAgent->getBrowser()['info'], // 用户浏览器信息
            'os'            => $userAgent->getOs()['info'], // 用户操作系统信息
        ];

        // 获取Redis对象 进行额外方法调用
        $redis = Cache::store('redis')->handler();

        // 用户登录信息缓存key（pms:sys_user:{user_id}:info:{finger_id}）
        $userInfoFingerKey = AuthService::getUserInfoFingerKey($userId, $fingerId);

        // 增加一个用户的登录状态信息HASH缓存
        $redis->hMset($userInfoFingerKey, $activeInfo);

        // 获取access_token的过期时间
        $access_token_expire = AuthService::getTokenExpireTime($userId)['access_token_expire'];

        // 设置过期时间和access_token的过期时间一样
        $redis->expire($userInfoFingerKey, $access_token_expire);
    }

    /**
     * 删除用户本次登录信息缓存
     * @param int $userId
     * @return void
     */
    public static function removeUserLoginInfo(int $userId): void
    {
        $fingerId = FingerService::getClientFingerId(); // 指纹Id

        // 获取Redis对象 进行额外方法调用
        $redis = Cache::store('redis')->handler();

        // 删除用户的登录状态信息HASH缓存（pms:sys_user:{user_id}:info:{finger_id}）
        //$redis->del(AuthService::getUserInfoFingerKey($userId, $fingerId));

        // 设置用户不活跃状态（删除当前用户活跃缓存记录）
        OnlineUserService::setUserInactive($userId);
    }

    /**
     * 获取用户登录信息缓存
     * @param int $userId
     * @param string|null $fingerId
     * @return mixed
     */
    public static function getUserLoginInfo(int $userId, ?string $fingerId): mixed
    {
        if (empty($fingerId)) {
            $fingerId = FingerService::getClientFingerId(); // 指纹Id
        }

        // 获取Redis对象 进行额外方法调用
        $redis = Cache::store('redis')->handler();

        // 删除用户的登录状态信息HASH缓存（pms:sys_user:{user_id}:info:{finger_id}）
        return $redis->hGetAll(AuthService::getUserInfoFingerKey($userId, $fingerId));
    }

    /**
     * 记录登录日志
     * @param array|null $userInfo
     * @param string $type
     * @param mixed $account
     * @param string $login_type
     * @param string $code
     * @param array $result
     * @return void
     */
    private static function writeLoginLog(?array $userInfo, string $type, mixed $account, string $login_type, string $code, array $result): void
    {
        $hideCode = in_array($login_type, ['account', 'username', 'email']) ? '******' : $code;

        $event = $type === 'LOGIN_SUCCESS' ? $result['message'] : (self::LOG_TYPE[$type] . (isset($result['message']) ? '：' . $result['message'] : ''));

        LogService::write(
            $userInfo,
            $event,
            '',
            [
                'login_type' => $login_type,
                'account'    => $account,
                'code'       => $hideCode,
            ],
            $result
        );
    }

    /**
     * 格式化用户信息字段
     * @param array $user
     * @return array
     */
    public static function formatUserInfo(array $user = []): array
    {
        foreach ($user as $key => $val) {
            // 是否超级管理员
            if ($key === 'is_super') {
                $user[$key] = $val ? '是' : '否';
            }

            // 是否锁屏
            if ($key === 'lockscreen') {
                $user[$key] = $val ? '是' : '否';
            }

            // 是否强制修改密码
            if ($key === 'force_change_password') {
                $user[$key] = $val ? '是' : '否';
            }

            // 登录方式
            if ($key === 'login_type') {
                $user[$key] = self::formatLoginType($val);
            }

            // 登录模式
            if ($key === 'login_mode') {
                $user[$key] = self::formatLoginMode($val);
            }

            // 获取客户端信息
            if ($key === 'client_id') {
                $user[$key] = ClientService::getClientInfo($val);
            }
        }

        return $user;
    }

    /**
     * 登录方式
     * @param $value
     * @return string
     */
    private static function formatLoginType($value): string
    {
        switch ($value) {
            case 'username':
                $value = '账号+密码登录';
                break;
            case 'email':
                $value = '邮箱+密码登录';
                break;
            case 'phone':
                $value = '手机号+密码登录';
                break;
            case 'mobile':
                $value = '手机验证码登录';
                break;
            case 'refresh_token':
                $value = '刷新token登录';
                break;
            default:
                break;
        }

        return $value;
    }

    /**
     * 登录模式
     * @param $value
     * @return string
     */
    private static function formatLoginMode($value): string
    {
        switch ($value) {
            case 'single':
                $value = '单点独立token';
                break;
            case 'multiple_share':
                $value = '多点独立token';
                break;
            case 'multiple_separate':
                $value = '多点共享token';
                break;
            default:
                break;
        }

        return $value;
    }
}
