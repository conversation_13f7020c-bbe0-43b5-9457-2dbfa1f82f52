<?php
namespace app\common\repository;

use app\common\traits\CacheTrait;
use think\Model;

/**
 * 全局仓储基类
 * 提供统一的数据访问和操作接口，实现基础的CRUD操作和通用查询功能
 * 主要职责：
 * 1. 提供统一的数据访问接口
 * 2. 实现基础的CRUD操作
 * 3. 提供分页查询功能
 * 4. 提供数据状态管理
 * 5. 提供数据验证功能
 * <AUTHOR> <<EMAIL>>
 * @package app\common\repository
 */
abstract class CommonRepository
{
    use CacheTrait;

    protected string $modelClass;

    protected Model $model;

    /**
     * 初始化仓储类
     * 注入模型类名而非实例，以避免条件累积问题
     * @param string $modelClass ThinkPHP模型类名
     */
    public function __construct(string $modelClass)
    {
        $this->modelClass = $modelClass;
        $this->model      = new $modelClass();
    }

    /**
     * 获取新的模型实例
     * 避免多次查询时条件累加的问题
     * @return \think\Model
     */
    protected function getNewModel(): Model
    {
        return new $this->modelClass();
    }

    /**
     * 设置自定义注入查询条件
     * @param array $condition 查询条件数组
     * @param string $type 条件类型: where, order, field, withoutField, limit
     * @return $this 返回当前对象以支持链式调用
     */
    public function setCondition(array $condition, string $type = 'where'): self
    {
        // 在设置条件前先清除已有条件，避免条件累积
        $this->clearConditions();

        $result = $this->model->setCondition($condition, $type);

        if ($result instanceof Model) {
            $this->model = $result;
        }

        return $this;
    }

    /**
     * 清除查询条件
     * @param string $name 要清除的条件名称，为空则清除所有
     * @return $this
     */
    public function clearConditions(string $name = ''): self
    {
        // thinkphp去除查询参数（防止条件累积）
        $this->model->removeOption($name);

        return $this;
    }

    /**
     * 获取分页数据列表（系统模块通用实现）
     * 支持关键字搜索和状态过滤，返回统一的分页数据结构
     * @param array $params 查询参数
     * @param int $pageNo 当前页码（从1开始）
     * @param int $pageSize 每页数据量
     * @return array 返回统一分页格式数据，包含：
     *               - total: 总记录数
     *               - list: 当前页数据集合
     *               - page: 当前页码
     *               - pageSize: 每页数量
     */
    public function pageList(array $params = [], int $pageNo = 1, int $pageSize = 10): array
    {
        // 在查询前清除已有条件
        $this->clearConditions();

        // 处理排序参数
        if (isset($params['order']) && isset($params['prop'])) {
            $orderType = $params['order'] === 'descending' ? 'desc' : 'asc';
            $this->setCondition([$params['prop'] => $orderType], 'order');
        }

        // 处理字段筛选参数
        if (!empty($params['filter'])) {
            $filters = $params['filter'];

            foreach ($filters as $field => $value) {
                if (empty($value)) {
                    continue;
                }

                if (!str_contains($value, ',')) {
                    $this->setCondition([
                        function($query) use ($field, $value) {
                            $query->whereOr([$field, '=', $value]);
                        },
                    ]);
                } else {
                    $values = explode(',', (string)$value);
                    $this->setCondition([
                        function($query) use ($field, $values) {
                            $query->whereOr(function($subQuery) use ($field, $values) {
                                foreach ($values as $val) {
                                    $subQuery->where([$field, '=', $val]);
                                }
                            });
                        },
                    ]);
                }
            }
        }

        return $this->model->pageList($pageNo, $pageSize);
    }

    /**
     * 启用 (支持批量)
     * @param mixed $ids
     * @return bool
     */
    public function enable(mixed $ids): bool
    {
        return $this->model->enable($ids);
    }

    /**
     * 禁用 (支持批量)
     * @param mixed $ids
     * @return bool
     */
    public function disable(mixed $ids): bool
    {
        return $this->model->disable($ids);
    }

    /**
     * 回收 (支持批量)
     * @param mixed $ids
     * @return bool
     */
    public function recycle(mixed $ids): bool
    {
        return $this->model->recycle($ids);
    }

    /**
     * 设置状态（后面要删掉这里的，改为私有）
     * @param mixed $ids
     * @param int $status
     * @return bool
     */
    public function setStatus(mixed $ids, int $status): bool
    {
        return $this->model->setStatus($ids, $status);
    }

    /**
     * 获取状态文本
     * @param int $status 状态值
     * @return string 状态描述文本
     */
    public function getStatusText(int $status): string
    {
        return $this->model->getStatusText($status);
    }

    /**
     * 更新数据排序
     * @param int $id 记录ID
     * @param int $sort 排序值
     * @param string $filed 排序字段
     * @return bool 更新成功返回true，失败返回false
     */
    public function updateSort(int $id, int $sort, string $filed = 'sort'): bool
    {
        return $this->model->updateSort($id, $sort, $filed);
    }

    /**
     * 查询数据记录
     * 调用模型find方法查询数据
     * @param mixed $value 值
     * @param string $field 要查询的字段
     * @return ?array 成功返回数据集，数据不存在返回null
     */
    public function getInfo(mixed $value, string $field = 'id'): ?array
    {
        return $this->model->getInfo($value, $field);
    }

    /**
     * 查询某个字段值
     * 调用模型value方法查询数据
     * @param mixed $where 查询条件
     * @param string $field 要查询的字段
     * @return mixed
     */
    public function getValue(mixed $where, string $field = 'id'): mixed
    {
        return $this->model->where($where)->value($field);
    }

    /**
     * 创建新数据记录
     * 调用模型create方法插入数据
     * @param array $data 包含完整字段的数据数组
     * @return \think\Model 成功返回新创建的数据模型对象
     */
    public function createData(array $data): Model
    {
        return $this->model->create($data);
    }

    /**
     * 更新数据记录
     * 支持单一ID或多条件更新
     * @param mixed $condition ID或条件数组
     * @param array $data 包含需要更新字段的数据数组
     * @return bool 成功返回true，失败返回false
     */
    public function updateData(mixed $condition, array $data): bool
    {
        try {
            if (is_array($condition)) {
                // 多条件更新
                $query = $this->model;

                foreach ($condition as $field => $value) {
                    $query = $query->where($field, $value);
                }

                $count = $query->count();

                if ($count <= 0) {
                    return false;
                }

                return $query->update($data) >= 0;
            }

            // 单一ID更新
            $model = $this->model->find($condition);

            if (!$model) {
                return false;
            }

            return $model->save($data);
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 删除指定ID的数据记录
     * @param mixed $condition ID或条件数组
     * @return bool 删除是否成功
     */
    public function deleteData(mixed $condition): bool
    {
        if (is_array($condition)) {
            return $this->model->where($condition)->delete() !== false;
        }

        return $this->model->where('id', $condition)->delete() !== false;
    }

    /**
     * 检查字段值是否已存在
     * 用于唯一性验证（如用户名、手机号等字段查重）
     * @param string $field 要检查的字段名
     * @param mixed $value 要检查的字段值
     * @param int $excludeId 需要排除的主键ID（常用于更新操作时排除自身）
     * @return \think\Model|null 存在返回数据模型对象，不存在返回null
     */
    public function checkExist(string $field, mixed $value, int $excludeId = 0): ?Model
    {
        return $this->model->checkExist($field, $value, $excludeId);
    }

    /**
     * 获取有效记录的下拉列表
     */
    public function getSelects(string $value = 'id', string $label = 'name'): array
    {
        return $this->model->getSelects($value, $label);
    }
}
