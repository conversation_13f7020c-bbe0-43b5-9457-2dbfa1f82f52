<?php
declare(strict_types = 1);

namespace app\system\service;

use app\common\service\FingerService;
use app\system\constant\AccountConstant;
use app\system\constant\AuthConstant;
use app\system\constant\InterceptConstant;
use app\system\constant\TokenConstant;
use app\system\model\PermissionRole as PermissionRoleModel;
use app\system\repository\PermissionRepository;
use app\system\repository\RoleUserRepository;
use app\system\repository\UserRepository;
use think\facade\Cache;

/**
 * 权限服务类
 * <AUTHOR> <<EMAIL>>
 * @package app\system\service
 */
class AuthService extends BaseService
{
    /**
     * 请求头中Authorization名称定义
     */
    public const HEADER_AUTHORIZATION_KEY = 'Authorization';

    public const CACHE_TAG_PREFIX = 'system'; // 管理员用户redis缓存tag前缀，用于统一管理

    public const CACHE_ADMIN_INFO_TAG = ':info'; // 管理员用户信息缓存tag标签

    public const CACHE_ADMIN_NODES_TAG = ':nodes'; // 管理员用户节点缓存tag标签

    public const CACHE_ADMIN_ACCESS_TOKEN_TAG = ':access_token'; // 管理员用户access_token缓存tag标签

    public const CACHE_ADMIN_REFRESH_TOKEN_TAG = ':refresh_token'; // 管理员用户refresh_token缓存tag标签

    // 登录模式常量定义
    public const LOGIN_MODE = [
        'SINGLE'            => 'single',            // 单点登录(新登录会踢掉旧登录)
        'MULTIPLE_SEPARATE' => 'multiple_separate', // 多点独立token(每个端独立维护一个token)
        'MULTIPLE_SHARE'    => 'multiple_share',    // 多点共享token(所有端共用一个token)
    ];

    /**
     * 获取当前节点路径
     * @return string
     */
    public static function getRoutePath(): string
    {
        return '/' . strtolower(app()->http->getName()) . '/' . strtolower(request()->controller()) . '/' . request()->action();
    }

    /**
     * 获取当前登录模式
     * @return string
     */
    public static function getLoginMode(): string
    {
        $mode = env('JWT.LOGIN_MODE', self::LOGIN_MODE['SINGLE']);

        return in_array($mode, self::LOGIN_MODE) ? $mode : self::LOGIN_MODE['SINGLE'];
    }

    /**
     * 签发用户token
     * @param int $userId
     * @return array
     */
    protected static function issueUserToken(int $userId): array
    {
        // 统一管理用户token中的payload数据
        $jwt_data = TokenService::setTokenPayloadData($userId);
        // 签发生成用户请求的access_token
        $access_token = TokenService::issueToken('access_token', $jwt_data);
        // 签发生成用户刷新token的refresh_token
        $refresh_token = TokenService::issueToken('refresh_token', $jwt_data);

        return [
            'access_token'  => $access_token,
            'refresh_token' => $refresh_token,
        ];
    }

    /**
     * 根据登录模式处理token存储
     * @return array|bool 返回array时包含token,返回bool时使用原token
     */
    public static function handleLoginToken(int $userId, string $login_type): array|bool
    {
        $mode = self::getLoginMode();

        // 生成新的用户token
        $issueUserTokenRes = self::issueUserToken($userId);

        switch ($mode) {
            case self::LOGIN_MODE['SINGLE']: // 单点模式
                // 单点登录时清除所有缓存
                self::removeUserCaches($userId);

                if (self::storeToken($userId, '', $issueUserTokenRes['access_token'], $issueUserTokenRes['refresh_token'])) {
                    return ['access_token' => $issueUserTokenRes['access_token'], 'refresh_token' => $issueUserTokenRes['refresh_token']];
                }

                return false;
            case self::LOGIN_MODE['MULTIPLE_SEPARATE']: // 多点独享模式
                $fingerId = FingerService::getClientFingerId(); // 获取客户端指纹ID

                if (self::storeToken($userId, $fingerId, $issueUserTokenRes['access_token'], $issueUserTokenRes['refresh_token'])) {
                    return ['access_token' => $issueUserTokenRes['access_token'], 'refresh_token' => $issueUserTokenRes['refresh_token']];
                }

                return false;
            case self::LOGIN_MODE['MULTIPLE_SHARE']: // 多点共享模式
                // 多点共享登录模式
                $stored_access_token  = self::getCacheUserInfo($userId, self::CACHE_ADMIN_ACCESS_TOKEN_TAG);
                $stored_refresh_token = self::getCacheUserInfo($userId, self::CACHE_ADMIN_REFRESH_TOKEN_TAG);

                // 如果存在旧的token就直接返回，不需要重新签发token
                if (!empty($stored_access_token) && !empty($stored_refresh_token)) {
                    // 如果是刷新token
                    if ($login_type === 'refresh_token') {
                        // 验证两个token是否合法有效，只要有一个不合法就重新生成
                        if (isset(TokenService::verifyToken($stored_access_token)['code']) || isset(TokenService::verifyToken($stored_refresh_token)['code'])) {
                            // 重新生成新的token
                            self::storeToken($userId, '', $issueUserTokenRes['access_token'], $issueUserTokenRes['refresh_token']);

                            return ['access_token' => $issueUserTokenRes['access_token'], 'refresh_token' => $issueUserTokenRes['refresh_token']];
                        }
                    }

                    return ['access_token' => $stored_access_token, 'refresh_token' => $stored_refresh_token];
                }

                // 将传过来的新token存起来并返回新的
                if (self::storeToken($userId, '', $issueUserTokenRes['access_token'], $issueUserTokenRes['refresh_token'])) {
                    return ['access_token' => $issueUserTokenRes['access_token'], 'refresh_token' => $issueUserTokenRes['refresh_token']];
                }

                return false;
            default:
                return false;
        }
    }

    /**
     * 校验权限
     * @param string $routePath
     * @param bool $checkNoAuth
     * @throws \Throwable
     * @return array|bool
     */
    public static function checkPermission(string $routePath, bool $checkNoAuth = true): array|bool
    {
        $userId        = 0;
        $cacheUserInfo = [];
        $userNodes     = [];

        $fingerId = FingerService::getClientFingerId();

        // 用户token，目前必须是客户端header中传递过来的Authorization参数，且必须带上Bearer
        $accessToken = TokenService::getBearerToken();

        // 如果有token，尝试获取用户信息
        if (!empty($accessToken)) {
            // 根据token获取用户基本信息
            $tokenInfo = self::getUserInfoByAccessToken($accessToken);

            if (isset($tokenInfo['userInfo'])) {
                $userId        = (int)$tokenInfo['userInfo']['id'];
                $cacheUserInfo = $tokenInfo['userInfo'];
                $userNodes     = self::getCacheUserInfo($userId, self::CACHE_ADMIN_NODES_TAG);

                // 检查用户是否被拉黑
                if (OnlineUserService::isUserBlacklisted($userId, $fingerId)) {
                    return AccountConstant::ACCOUNT_BLACKLISTED;
                }
            }
        }

        // 如果需要检测完全不需要鉴权的节点
        if ($checkNoAuth && in_array($routePath, self::getNoAuthPermissionList())) {
            // 直接返回用户缓存信息（如果有）
            return [
                'qualified'   => true,
                'userId'      => $userId,
                'userInfo'    => $cacheUserInfo,
                'userNodes'   => $userNodes,
                'accessToken' => $accessToken,
            ];
        }

        // 需要鉴权的接口，必须要有token和用户信息
        if (empty($accessToken)) {
            return TokenConstant::ACCESS_TOKEN_EMPTY;
        }

        // 如果前面的验证token有返回错误，这里就可以返回给客户端
        if (isset($tokenInfo['code'])) {
            return $tokenInfo;
        }

        // 如果用户ID为空
        if (empty($userId)) {
            return AccountConstant::ACCOUNT_USERID_EMPTY;
        }

        // 获取客户端指纹ID
        $fingerId = FingerService::getClientFingerId();

        // 检查用户指纹Id登录信息是否存在
        if (!OnlineUserService::checkUserInfoFingerKeyExist($userId, $fingerId)) {
            return TokenConstant::LOGIN_DEVICE_EMPTY;
        }

        // 如果是心跳检测接口就返回验证通过（心跳不需要在往下走了）
        if ($routePath == self::getSpecialPermission('CHECK_HEARTBEAT')) {
            return [
                'qualified'   => true,
                'userId'      => $userId,
                'userInfo'    => $cacheUserInfo,
                'userNodes'   => $userNodes,
                'accessToken' => $accessToken,
            ];
        }

        // 对需要鉴权的接口进行数据库查询验证
        $dbUserInfo = (new UserRepository())->getInfo($userId);

        // 说明用户已删除（或软删除）
        if (!$dbUserInfo) {
            return AccountConstant::ACCOUNT_NOT_EXIST;
        }

        if ($dbUserInfo['status'] == 0) {
            return AccountConstant::ACCOUNT_FROZEN;
        }

        if ($dbUserInfo['status'] == -1) {
            return AccountConstant::ACCOUNT_DELETED;
        }

        if ($dbUserInfo['status'] != 1) {
            return AccountConstant::ACCOUNT_STATUS_ABNORMAL;
        }

        // 锁屏检查
        if (isset($cacheUserInfo['lockscreen']) && $cacheUserInfo['lockscreen'] == 1 && !in_array($routePath, [self::getSpecialPermission('LOCKSCREEN'), self::getSpecialPermission('UNLOCKSCREEN')])) {
            return InterceptConstant::USER_FORCE_LOCKSCREEN;
        }

        // 强制修改密码检查
        if (isset($dbUserInfo['force_change_password']) && $dbUserInfo['force_change_password'] == 1 && $routePath != self::getSpecialPermission('CHANGE_PASSWORD')) {
            return InterceptConstant::USER_FORCE_CHANGE_PASSWORD;
        }

        // 非超管且无权限节点
        if ($dbUserInfo['is_super'] != 1 && empty($userNodes)) {
            return TokenConstant::LOGIN_NODE_EMPTY;
        }

        // 检查权限：超级管理员或公共节点直接通过，否则需要验证具体权限
        if (!$dbUserInfo['is_super'] && !self::isPublicPermission($routePath)) {
            $permissionRepository = new PermissionRepository();

            // 获取用户权限API列表
            $user_auth_apiList = $permissionRepository->getUserAuthApiList($dbUserInfo, $userNodes);
            $user_rules_path   = array_column($user_auth_apiList, 'url');

            // 验证是否有权限访问
            if (!in_array($routePath, $user_rules_path)) {
                return AuthConstant::AUTH_NO_PERMISSION;
            }
        }

        // 鉴权通过，返回必要数据
        return [
            'qualified'   => true,
            'userId'      => $userId,
            'userInfo'    => $dbUserInfo,
            'userNodes'   => $userNodes,
            'accessToken' => $accessToken,
        ];
    }

    /**
     * 根据token获取用户信息
     * @param string|null $accessToken
     * @return array
     */
    public static function getUserInfoByAccessToken(?string $accessToken): array
    {
        if (!empty($accessToken)) {
            // 验证用户token是否合法有效
            $tokenInfo = TokenService::verifyToken($accessToken);

            // token验证失败时返回错误
            if (isset($tokenInfo['code'])) {
                return $tokenInfo;
            }

            // 从token中获取用户id
            $userId = $tokenInfo['uid'] ?? 0;

            // 如果没有携带用户UID
            if (empty($userId)) {
                return TokenConstant::ACCESS_TOKEN_ERROR;
            }

            // 验证token有效性（比对和服务端存储的是否一致）
            if (!self::checkToken($userId, $accessToken)) {
                return TokenConstant::ACCESS_TOKEN_INVALID;
            }

            // 从缓存获取用户信息
            $cacheUserInfo = self::getCacheUserInfo($userId, self::CACHE_ADMIN_INFO_TAG);

            if (empty($cacheUserInfo) || !isset($cacheUserInfo['id'])) {
                return TokenConstant::LOGIN_CACHE_EMPTY;
            }

            return [
                'userId'   => $userId,
                'userInfo' => $cacheUserInfo,
            ];
        }

        return TokenConstant::ACCESS_TOKEN_EMPTY;
    }

    /**
     * 根据token获取用户信息简单版（不验证token）
     * @param string|null $accessToken
     * @return array
     */
    public static function getUserInfoByAccessTokenSimple(?string $accessToken): array
    {
        if (!empty($accessToken)) {
            // 解析token，拿到用户ID
            $tokenInfo = TokenService::parseToken($accessToken);

            // 从token中获取用户id
            $userId = $tokenInfo['uid'] ?? 0;

            // 从缓存获取用户信息
            $cacheUserInfo = self::getCacheUserInfo($userId, self::CACHE_ADMIN_INFO_TAG);

            return [
                'userId'   => $userId,
                'userInfo' => $cacheUserInfo ?: [],
            ];
        }

        return [];
    }

    /**
     * 根据用户ID获取用户信息
     * @param int $userId
     * @return array|null
     */
    public static function getUserInfoById(int $userId): ?array
    {
        if (!empty($userId)) {
            // 从缓存获取用户信息
            return self::getCacheUserInfo($userId, self::CACHE_ADMIN_INFO_TAG) ?: null;
        }

        return [];
    }

    /**
     * 获取权限配置
     * @return array
     */
    protected static function getPermissionConfig(): array
    {
        return config('permissions');
    }

    /**
     * 获取不需要鉴权的节点列表
     * @return array
     */
    protected static function getNoAuthPermissionList(): array
    {
        $config = self::getPermissionConfig();

        return $config['NO_AUTH_PERMISSION_LIST'] ?? [];
    }

    /**
     * 获取公共可访问节点列表
     * @return array
     */
    protected static function getPublicPermissionList(): array
    {
        $permissionConfig = self::getPermissionConfig();
        $publicNodes      = [];

        // 公共可访问节点附加特殊权限节点（特殊节点也需要开放）
        $permissionConfig['PUBLIC_PERMISSION_LIST']['special'] = array_values(self::getSpecialPermission());

        // 展开配置中的公共节点
        foreach ($permissionConfig['PUBLIC_PERMISSION_LIST'] ?? [] as $group) {
            foreach ($group as $node) {
                // 如果包含通配符
                if (str_contains($node, '*')) {
                    // 处理通配符
                    $pattern       = str_replace('*', '.*', $node);
                    $publicNodes[] = ['pattern' => $pattern, 'is_wildcard' => true];
                } else {
                    $publicNodes[] = ['pattern' => $node, 'is_wildcard' => false];
                }
            }
        }

        return $publicNodes;
    }

    /**
     * 获取特殊权限节点列表或某个特殊权限节点
     * @param string|null $key
     * @return mixed
     */
    protected static function getSpecialPermission(?string $key = null): mixed
    {
        $config = self::getPermissionConfig();

        if (empty($key)) {
            return $config['SPECIAL_PERMISSION_LIST'] ?? [];
        }

        return $config['SPECIAL_PERMISSION_LIST'][$key] ?? null;
    }

    /**
     * 判断路由是否在公共节点中
     * @param string $routePath
     * @return bool
     */
    protected static function isPublicPermission(string $routePath): bool
    {
        $publicNodes = self::getPublicPermissionList();

        foreach ($publicNodes as $node) {
            // 判断是否通配符
            if ($node['is_wildcard']) {
                if (preg_match('#^' . $node['pattern'] . '$#', $routePath)) {
                    return true;
                }
            } else {
                if ($node['pattern'] === $routePath) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 存储token到缓存
     * @param int $userId
     * @param string $fingerId
     * @param string $access_token
     * @param string $refresh_token
     * @return bool
     */
    private static function storeToken(int $userId, string $fingerId, string $access_token, string $refresh_token): bool
    {
        $prefix = self::getUserTokenPrefix($userId);

        if ($fingerId) {
            $prefix .= ':' . $fingerId;
        }

        return Cache::tag(self::userCacheTag($userId, true))->set($prefix . self::CACHE_ADMIN_ACCESS_TOKEN_TAG, $access_token, TokenService::getTokenExpire('access_token'))
            && Cache::tag(self::userCacheTag($userId, true))->set($prefix . self::CACHE_ADMIN_REFRESH_TOKEN_TAG, $refresh_token, TokenService::getTokenExpire('refresh_token'));
    }

    /**
     * 获取用户token剩余生命周期时长
     * @param int $userId
     * @param string $type
     * @return int
     */
    public static function getUserTokenTTL(int $userId, string $type = 'access_token'): int
    {
        // 获取缓存前缀
        $cache_prefix = config('cache.stores.redis.prefix');
        // 获取用户token前缀
        $user_token_prefix = self::getUserTokenPrefix($userId);

        $token_type = [
            'access_token'  => self::CACHE_ADMIN_ACCESS_TOKEN_TAG,
            'refresh_token' => self::CACHE_ADMIN_REFRESH_TOKEN_TAG,
        ];

        return Cache::store('redis')->handler()->ttl($cache_prefix . $user_token_prefix . $token_type[$type] ?? 'access_token');
    }

    /**
     * 获取用户token生命周期（或剩余）时长
     * @param int $userId
     * @return array
     */
    public static function getTokenExpireTime(int $userId): array
    {
        $access_token_expire  = TokenService::getTokenExpire('access_token');
        $refresh_token_expire = TokenService::getTokenExpire('refresh_token');

        if (self::getLoginMode() === self::LOGIN_MODE['MULTIPLE_SHARE']) {
            // 获取当前缓存的剩余生命周期
            $access_token_expire  = self::getUserTokenTTL($userId, 'access_token') ?: $access_token_expire;
            $refresh_token_expire = self::getUserTokenTTL($userId, 'refresh_token') ?: $refresh_token_expire;
        }

        return [
            'access_token_expire'  => $access_token_expire,
            'refresh_token_expire' => $refresh_token_expire,
        ];
    }

    /**
     * 校验token是否有效
     * @param int $userId
     * @param string $token
     * @return bool
     */
    public static function checkToken(int $userId, string $token): bool
    {
        $mode = self::getLoginMode();

        switch ($mode) {
            case self::LOGIN_MODE['SINGLE']:
                // 单点登录验证
                $stored_token = self::getCacheUserInfo($userId, self::CACHE_ADMIN_ACCESS_TOKEN_TAG);
                break;
            case self::LOGIN_MODE['MULTIPLE_SEPARATE']:
                // 多点独立token:使用更可靠的指纹Id标识
                $fingerId     = FingerService::getClientFingerId(); // 获取客户端指纹ID
                $stored_token = self::getCacheUserInfo($userId, self::CACHE_ADMIN_ACCESS_TOKEN_TAG, $fingerId);
                break;
            case self::LOGIN_MODE['MULTIPLE_SHARE']:
                // 多点共享验证不需要验证指纹Id
                $stored_token = self::getCacheUserInfo($userId, self::CACHE_ADMIN_ACCESS_TOKEN_TAG);
                break;
            default:
                $stored_token = null;
        }

        return !empty($stored_token) && $stored_token === $token;
    }

    /**
     * 定义用户缓存tag
     * @param int $userId
     * @param bool $return_user_tag
     * @return string|string[]
     */
    public static function userCacheTag(int $userId = 0, bool $return_user_tag = true): array|string
    {
        // 用户自己的tag
        $user_tag = self::CACHE_TAG_PREFIX . '@' . $userId;

        if ($return_user_tag) {
            return $user_tag;
        }

        return [self::CACHE_TAG_PREFIX, $user_tag];
    }

    /**
     * 获取用户token前缀
     * @param int $userId 用户id
     * @param bool $userIdSuffix 是否用用户ID后缀
     * @return string
     * @example （sys_user:{user_id} 或 pms:sys_user:）
     */
    public static function getUserTokenPrefix(int $userId, bool $userIdSuffix = true): string
    {
        $prefix = self::APP_PREFIX . 'user:';

        if ($userIdSuffix) {
            return $prefix . $userId;
        }

        return config('cache.stores.redis.prefix') . $prefix;
    }

    /**
     * 获取用户token前缀
     * @param int $userId
     * @param bool $useRedisPrefix 是否用项目前缀（只有在单独用redis原生操作时）
     * @return string
     * @example （sys_user:{user_id}:info 或 pms:sys_user:{user_id}:info）
     */
    public static function getUserInfoTokenPrefix(int $userId, bool $useRedisPrefix = false): string
    {
        $prefix = self::getUserTokenPrefix($userId) . self::CACHE_ADMIN_INFO_TAG;

        if ($useRedisPrefix) {
            return config('cache.stores.redis.prefix') . $prefix;
        }

        return $prefix;
    }

    /**
     * 获取用户token前缀
     * @param int $userId 用户ID
     * @param string $fingerId 指纹Id
     * @return string
     * @example （pms:sys_user:{user_id}:info:{finger_id}）
     */
    public static function getUserInfoFingerKey(int $userId, string $fingerId): string
    {
        return self::getUserInfoTokenPrefix($userId, true) . ':' . $fingerId;
    }

    /**
     * 生成密码
     * @param string $password
     * @param string $salt
     * @return string
     */
    public static function generatePassword(string $password = '', string $salt = ''): string
    {
        return md5($password . $salt);
    }

    /**
     * 生成盐值
     * @return string
     */
    public static function generateSalt(): string
    {
        return (string)mt_rand(10000, 99999);
    }

    /**
     * 根据用户id返回缓存中的对应信息
     * @param int $userId
     * @param string $key
     * @param string $fingerId 指纹Id
     * @return mixed
     */
    public static function getCacheUserInfo(int $userId = 0, string $key = '', string $fingerId = ''): mixed
    {
        if (empty($userId) || empty($key)) {
            return null;
        }

        // 获取用户token前缀
        $prefix = self::getUserTokenPrefix($userId);

        // 添加指纹Id
        if (!empty($fingerId)) {
            $prefix .= ':' . $fingerId;
        }

        return Cache::get($prefix . $key);
    }

    /**
     * 根据用户id更新缓存中对应的用户信息（公共方法：标准字段增删需评估）
     * @param int $userId 用户id
     * @param array $data 对应的缓存数据
     * @param bool $is_insert 是否插入数据（false时为更新缓存）
     * @return bool
     */
    public static function updateCacheUserInfo(int $userId, array $data, bool $is_insert = false): bool
    {
        if (empty($userId) || empty($data)) {
            return false;
        }

        // 从缓存中获取用户信息
        $cache_info = self::getCacheUserInfo($userId, self::CACHE_ADMIN_INFO_TAG);

        // 定义允许更新的字段列表
        $allowedFields = [
            'id', 'username', 'realname', 'phone', 'email', 'is_super', 'dept_id', 'roles_id', 'roles_name', 'create_at', 'status',
            // 是否强制修改密码
            'force_change_password',
            // 是否锁屏
            'lockscreen',
            // 是否需要重新登录
            'relogin',
        ];

        if (empty($cache_info)) {
            // 登录时写入缓存
            if ($is_insert === true) {
                // 过滤输入数据，只保留允许更新的字段
                $userInfoArr = array_intersect_key($data, array_flip($allowedFields));
            } else {
                // 说明用户未登录，不需要更新缓存
                return false;
            }
        } else {
            $userInfoArr = $cache_info;

            // 只更新传入的字段
            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    $userInfoArr[$field] = $data[$field];
                }
            }
        }

        if (empty($userInfoArr)) {
            return false;
        }

        // 用户缓存前缀
        $cache_prefix = self::getUserTokenPrefix($userId);

        // token过期时间
        $token_expire = self::getTokenExpireTime($userId);

        return Cache::tag(self::userCacheTag($userId, true))->set($cache_prefix . self::CACHE_ADMIN_INFO_TAG, $userInfoArr, $token_expire['refresh_token_expire']);
    }

    /**
     * 根据用户id更新缓存中对应的用户权限节点数据
     * @param int $userId 用户id
     * @param array $data 对应的缓存数据
     * @param bool $is_insert 是否插入数据（false时为更新缓存）
     * @return bool
     */
    public static function updateCacheUserNodes(int $userId, array $data, bool $is_insert = false): bool
    {
        if (empty($userId)) {
            return false;
        }

        // 从缓存中获取用户权限节点数据
        $cache_nodes = self::getCacheUserInfo($userId, self::CACHE_ADMIN_NODES_TAG);

        // 缓存里无对应信息，且为更新状态时
        if (empty($cache_nodes) && $is_insert === false) {
            // 说明用户未登录，不需要更新缓存
            return false;
        }

        // 用户缓存前缀
        $cache_prefix = self::getUserTokenPrefix($userId);

        // token过期时间
        $token_expire = self::getTokenExpireTime($userId);

        $data = array_values($data);

        return Cache::tag(self::userCacheTag($userId, true))->set($cache_prefix . self::CACHE_ADMIN_NODES_TAG, $data, $token_expire['refresh_token_expire']);
    }

    /**
     * 根据用户ids更新已登录用户的节点权限缓存
     * @param mixed $userId
     * @return bool
     */
    public static function updateCacheUserNodesByUserIds(mixed $userId): bool
    {
        // 转成数组
        $userIds = is_array($userId) ? $userId : [$userId];

        foreach ($userIds as $uid) {
            // 根据用户id用户所在的角色ids
            $userRoleIds = (new RoleUserRepository())->getRoleIdsByUserId((int)$uid, 1);

            // 获取用户所在角色ids对应的权限节点ids
            $userPermissionIds = PermissionRoleModel::getPermissions($userRoleIds);

            // 根据用户id更新缓存中对应的用户权限节点数据
            self::updateCacheUserNodes((int)$uid, $userPermissionIds);
        }

        return true;
    }

    /**
     * 根据用户id更新缓存中对应的用户token（包括access_token和refresh_token，根据第一个参数tag来区分）
     * @param string $cache_tag
     * @param int $userId 用户id
     * @param string $token 对应的缓存数据
     * @param bool $is_insert 是否插入数据（false时为更新缓存）
     * @param int|null $ttl 缓存时长（为null时永久有效）
     * @return bool
     * @deprecated 好像没用到，后期看情况删除掉
     */
    public static function updateCacheUserToken(string $cache_tag, int $userId, string $token, bool $is_insert = false, ?int $ttl = null): bool
    {
        if (empty($userId) || empty($token)) {
            return false;
        }

        // 从缓存中获取用户token数据
        $cache_token = self::getCacheUserInfo($userId, $cache_tag);

        // 缓存里无对应信息，且为更新状态时
        if (empty($cache_token) && $is_insert === false) {
            // 说明用户未登录，不需要更新缓存
            return false;
        }

        // 用户缓存前缀
        $cache_prefix = self::getUserTokenPrefix($userId);

        return Cache::tag(self::userCacheTag($userId, true))->set($cache_prefix . $cache_tag, trim($token), $ttl);
    }

    /**
     * 批量删除管理员用户token对应的缓存
     * @param int $userId
     * @return bool
     */
    public static function removeUserCaches(int $userId = 0): bool
    {
        // 批量删除当前用户的key
        return Cache::tag(self::userCacheTag($userId, true))->clear();
    }
}
