<?php
declare(strict_types = 1);

namespace app\tenant\service;

use app\tenant\exception\AccountException;
use Firebase\JWT\BeforeValidException;
use Firebase\JWT\ExpiredException;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Firebase\JWT\SignatureInvalidException;
use InvalidArgumentException;
use think\facade\Cache;
use TypeError;

/**
 * 租户令牌服务类
 * 负责租户系统中的令牌(Token)的创建、验证和管理
 * 包含以下主要功能：
 * - 创建访问令牌
 * - 验证令牌有效性
 * - 管理令牌的过期时间
 * <AUTHOR> <<EMAIL>>
 * @package app\tenant\service
 */
class TokenService extends BaseService
{
    private static ?TokenService $instance = null;

    protected string $tokenPrefix = self::APP_PREFIX . 'user:';

    // 登录模式常量定义
    protected const LOGIN_MODE = [
        'SINGLE'            => 'single',            // 单点登录(新登录会踢掉旧登录)
        'MULTIPLE_SEPARATE' => 'multiple_separate', // 多点独立token(每个端独立维护一个token)
        'MULTIPLE_SHARE'    => 'multiple_share',    // 多点共享token(所有端共用一个token)
    ];

    /**
     * access_token过期时间（默认2天）
     * @var int
     */
    protected int $accessTokenExpire;

    /**
     * refresh_token过期时间（默认7天）
     * @var int
     */
    protected int $refreshTokenExpire;

    public function __construct()
    {
        parent::__construct();

        // 从配置文件中读取token过期时间
        $this->accessTokenExpire  = config('token.expire_time.access_token', 2 * 24 * 60 * 60);
        $this->refreshTokenExpire = config('token.expire_time.refresh_token', 7 * 24 * 60 * 60);
    }

    /**
     * 获取当前登录模式
     * @return string
     */
    public function getLoginMode(): string
    {
        $mode = config('account.login_mode', 'single');

        return in_array($mode, self::LOGIN_MODE) ? $mode : self::LOGIN_MODE['SINGLE'];
    }

    /**
     * 获取登录模式常量
     * @return array
     */
    public function getLoginModes(): array
    {
        return self::LOGIN_MODE;
    }

    public function __clone()
    {
    }

    public static function getInstance(): self
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }

        return self::$instance;
    }

    /**
     * 签发token（生成token+缓存token）
     * @param array $userData 必须要带租户id+用户id
     * @param string $type 签发的token类型
     * @param bool $cache 是否缓存token
     * @throws \Random\RandomException
     * @throws \Exception
     * @return array 签发成功返回token数据
     */
    public function issueToken(array $userData, string $type = 'access_token', bool $cache = false): array
    {
        if (empty($userData['tenant_id']) || empty($userData['id'])) {
            throw new \Exception('issueToken参数无效');
        }

        // 生成token数据
        $tokenData = $this->generateToken($userData, $type);

        if ($cache) {
            $cacheService  = CacheService::getInstance();
            $tokenCacheKey = $this->getCacheKey($userData['tenant_id'], $userData['id'], $type);

            // 将对应类型的token放入缓存
            $cacheService->set($tokenCacheKey, $tokenData['token'], $tokenData['expires_in']);
        }

        return $tokenData;
    }

    /**
     * 根据配置的token风格生成token
     * @param array $userData 用户数据
     * @param string $type token类型
     * @throws \Random\RandomException
     * @return array 生成的token信息
     */
    protected function generateToken(array $userData, string $type = 'access_token'): array
    {
        $tokenStyle = config('token.token_style', 'jwt');
        $result     = [];

        switch ($tokenStyle) {
            case 'uuid':
                $result['token'] = $this->generateUuidToken();
                break;
            case 'md5':
                // refresh_token时还可以为其加上salt
                $salt = $type === 'refresh_token' && isset($userData['access_token']) ? $userData['access_token'] : '';

                $result['token'] = $this->generateMd5Token($salt);
                break;
            case 'jwt':
            default:
                $result['token'] = $this->generateJwtToken($userData, $type);
                break;
        }

        $result['expires_in'] = $type === 'access_token' ? $this->accessTokenExpire : $this->refreshTokenExpire;

        return $result;
    }

    /**
     * 检查token是否即将过期
     * @param int $tenantId 租户ID
     * @param int $userId 用户ID
     * @return bool
     */
    private function isTokenNearExpiry(int $tenantId, int $userId): bool
    {
        $tokenExpireTime = $this->getTokenExpireTime($tenantId, $userId);

        if (!$tokenExpireTime || $tokenExpireTime <= 0) {
            return true;
        }

        // 当剩余时间小于过期时间的20%时，认为即将过期
        $remainingTime = $tokenExpireTime - time();

        return $remainingTime <= ($this->accessTokenExpire * 0.2);
    }

    /**
     * 获取token过期时间
     * @param int $tenantId
     * @param int $userId
     * @return int
     */
    public function getTokenExpireTime(int $tenantId, int $userId): int
    {
        $accessTokenCacheKey = $this->getCacheKey($tenantId, $userId, 'access_token');

        return CacheService::getInstance()->ttl($accessTokenCacheKey);
    }

    /**
     * 统一管理缓存key
     * @param int $tenantId
     * @param int $userId
     * @param string $suffix 后缀
     * @return string
     */
    private function getCacheKey(int $tenantId, int $userId, string $suffix): string
    {
        return CacheService::getInstance()->getUserCacheKey($tenantId, $userId, $suffix);
    }

    /**
     * 统一获取请求带过来的token
     * @param string $type 获取的token类型，默认为access_token
     * @return string
     */
    public function getRequestToken(string $type = 'access_token'): string
    {
        if ($type === 'access_token') {
            // 获取accessToken
            $accessToken = request()->header('Authorization');

            if (empty($accessToken)) {
                return '';
            }

            // 统一处理Authorization头部格式，移除可能存在的Bearer前缀
            return trim(str_replace('Bearer', '', $accessToken));
        }

        // 获取refreshToken，一般从请求参数中获取
        $refreshToken = request()->param('refresh_token');

        return $refreshToken ?: '';
    }

    /**
     * 验证token
     * @param string $token 要验证的token
     * @param string $type token类型，access_token或refresh_token
     * @throws AccountException
     * @return array
     */
    public function verifyToken(string $token, string $type = 'access_token'): array
    {
        // 获取token风格配置
        $tokenStyle = config('token.token_style', 'jwt');

        // 根据配置的风格验证token
        switch ($tokenStyle) {
            case 'jwt':
                $userData = $this->verifyJwtToken($token, $type);
                break;
            case 'uuid':
            case 'md5':
            default:
                $userData = $this->verifySimpleToken($token, $type);
                break;
        }

        // 如果是access_token，检查是否即将过期
        /*if ($type === 'access_token' && $this->isTokenNearExpiry($userData['tenant_id'], $userData['id'])) {
            // 获取refresh_token
            $refreshTokenCacheKey = $this->getCacheKey($userData['tenant_id'], $userData['id'], 'refresh_token');
            $refreshTokenData     = CacheService::getInstance()->get($refreshTokenCacheKey);

            if ($refreshTokenData && isset($refreshTokenData['token'])) {
                // 使用refresh_token刷新access_token
                $newTokenData = $this->refreshToken($refreshTokenData['token']);

                $userData['new_access_token'] = $newTokenData['access_token'];
                $userData['new_expires_in']   = $newTokenData['expires_in'];
            }
        }*/

        return $userData;
    }

    /**
     * 验证JWT格式的token
     * @param string $token JWT token字符串
     * @param string $type token类型
     * @throws AccountException
     * @return array
     */
    protected function verifyJwtToken(string $token, string $type = 'access_token'): array
    {
        $tokenName = $type === 'access_token' ? 'Access_Token' : 'Refresh_Token';
        $jwtKey    = config('token.jwt_key');

        try {
            // 验证JWT token
            $decoded = JWT::decode($token, new Key($jwtKey, config('token.styles.jwt.algorithm', 'HS256')));

            // 验证签发者和接收者
            $issuer   = config('token.styles.jwt.issuer', 'tenant');
            $audience = config('token.styles.jwt.audience', 'user');

            if ($decoded->iss !== $issuer || $decoded->sub !== $audience) {
                throw new AccountException($tokenName . '无效', AccountException::INVALID_TOKEN);
            }

            // 验证是否在生效时间之前
            if (isset($decoded->nbf) && $decoded->nbf > time()) {
                throw new AccountException($tokenName . '尚未生效', AccountException::INVALID_TOKEN);
            }

            // 验证是否过期
            if (isset($decoded->exp) && $decoded->exp < time()) {
                throw new AccountException($tokenName . '已过期', AccountException::INVALID_TOKEN);
            }

            // 验证IP地址（如果配置了包含IP）
            if (config('token.styles.jwt.include_ip', false) && isset($decoded->cip)) {
                if ($decoded->cip !== request()->ip()) {
                    throw new AccountException($tokenName . 'IP地址不匹配', AccountException::INVALID_TOKEN);
                }
            }

            // 验证设备信息（如果配置了包含设备信息）
            if (config('token.styles.jwt.include_device', false) && isset($decoded->device)) {
                if ($decoded->device !== request()->header('user-agent')) {
                    throw new AccountException($tokenName . '设备信息不匹配', AccountException::INVALID_TOKEN);
                }
            }

            // 获取用户数据
            $userDataCacheKey = $this->getCacheKey($decoded->tenant_id, $decoded->uid, 'info');
            $userData         = CacheService::getInstance()->get($userDataCacheKey);

            if (!$userData) {
                throw new AccountException('用户数据已失效', AccountException::INVALID_TOKEN);
            }

            return $userData;
        } catch (ExpiredException $e) {
            throw new AccountException($tokenName . '已过期', AccountException::INVALID_TOKEN);
        } catch (BeforeValidException $e) {
            throw new AccountException($tokenName . '尚未生效', AccountException::INVALID_TOKEN);
        } catch (SignatureInvalidException $e) {
            throw new AccountException($tokenName . '签名无效', AccountException::INVALID_TOKEN);
        } catch (InvalidArgumentException $e) {
            throw new AccountException($tokenName . '格式错误', AccountException::INVALID_TOKEN);
        } catch (TypeError $e) {
            throw new AccountException($tokenName . '类型错误[' . $e->getMessage() . ']', AccountException::INVALID_TOKEN);
        }
    }

    /**
     * 验证简单格式的token（UUID或MD5）
     * @param string $token token字符串
     * @param string $type token类型
     * @throws AccountException
     * @return array
     */
    protected function verifySimpleToken(string $token, string $type = 'access_token'): array
    {
        // 查找所有可能匹配的缓存键
        $keys      = self::getRedisKeys($this->tokenPrefix . '*:*:' . $type);
        $tokenData = null;
        $tenantId  = null;
        $userId    = null;

        // 遍历所有匹配的键，查找包含当前token的缓存数据
        foreach ($keys as $key) {
            $cacheData = CacheService::getInstance()->get($key);

            if ($cacheData && isset($cacheData['token']) && $cacheData['token'] === $token) {
                $tokenData = $cacheData;

                // 从缓存键中提取租户ID和用户ID
                $keyParts = explode(':', $key);

                if (count($keyParts) >= 3) {
                    $tenantId = (int)$keyParts[1];
                    $userId   = (int)$keyParts[2];
                }
                break;
            }
        }

        $tokenName = $type === 'access_token' ? 'Access_Token' : 'Refresh_Token';

        if (!$tokenData || $tenantId === null || $userId === null) {
            throw new AccountException($tokenName . '已失效', AccountException::INVALID_TOKEN);
        }

        if ($tokenData['expire_time'] < time()) {
            $tokenCacheKey = $this->getCacheKey($tenantId, $userId, $type);
            CacheService::getInstance()->delete($tokenCacheKey);

            throw new AccountException($tokenName . '已过期', AccountException::INVALID_TOKEN);
        }

        // 获取用户数据
        $userDataCacheKey = $this->getCacheKey($tenantId, $userId, 'user_data');
        $userData         = Cache::get($userDataCacheKey);

        if (!$userData) {
            throw new AccountException('用户数据已失效', AccountException::INVALID_TOKEN);
        }

        return $userData;
    }

    /**
     * 生成MD5风格的token
     * @param string $salt 可选的加盐字符串
     * @return string
     */
    protected function generateMd5Token(string $salt = ''): string
    {
        $useRandomPrefix = config('token.styles.md5.use_random_prefix', true);
        $prefix          = $useRandomPrefix ? (string)mt_rand() : '';

        return md5(uniqid($prefix, true) . $salt);
    }

    /**
     * 生成UUID风格的token
     * @throws \Random\RandomException
     * @return string
     */
    protected function generateUuidToken(): string
    {
        $version = config('token.styles.uuid.version', 4);

        // 基于时间的UUID (v1)
        if ($version === 1) {
            $node      = random_bytes(6);
            $timestamp = microtime(false);
            $time      = (int)substr($timestamp, 11) * 10000 + (int)substr($timestamp, 2, 9);

            $timeHex  = str_pad(dechex($time), 16, '0', STR_PAD_LEFT);
            $timeHigh = substr($timeHex, 0, 8);
            $timeMid  = substr($timeHex, 8, 4);

            $timeHighAndVersion = hexdec($timeHigh) & 0x0FFFFFFF;
            $timeHighAndVersion |= 0x10000000;

            $clockSeq   = random_int(0, 0x3FFF);
            $clockSeqHi = ($clockSeq & 0x3F00) >> 8;
            $clockSeqHi |= 0x80;
            $clockSeqLow = $clockSeq & 0xFF;

            $nodeHex = bin2hex($node);

            return sprintf(
                '%08x-%04x-%04x-%02x%02x-%012s',
                $timeHighAndVersion,
                hexdec($timeMid),
                hexdec(substr($timeHex, 12, 4)),
                $clockSeqHi,
                $clockSeqLow,
                $nodeHex
            );
        }

        // 随机UUID (v4)
        if ($version === 4) {
            $data    = random_bytes(16);
            $data[6] = chr(ord($data[6]) & 0x0F | 0x40);
            $data[8] = chr(ord($data[8]) & 0x3F | 0x80);

            return vsprintf('%s%s-%s-%s-%s-%s%s%s', str_split(bin2hex($data), 4));
        }

        // 默认返回v4 UUID
        $data    = random_bytes(16);
        $data[6] = chr(ord($data[6]) & 0x0F | 0x40);
        $data[8] = chr(ord($data[8]) & 0x3F | 0x80);

        return vsprintf('%s%s-%s-%s-%s-%s%s%s', str_split(bin2hex($data), 4));
    }

    /**
     * 生成JWT风格的token
     * @param array $userData 用户数据
     * @param string $type token类型
     * @return string
     */
    protected function generateJwtToken(array $userData, string $type = 'access_token'): string
    {
        // 获取JWT配置
        $algorithm     = config('token.styles.jwt.algorithm', 'HS256');
        $issuer        = config('token.styles.jwt.issuer', 'tenant');
        $audience      = config('token.styles.jwt.audience', 'user');
        $includeIp     = config('token.styles.jwt.include_ip', false);
        $includeDevice = config('token.styles.jwt.include_device', false);
        $jwtKey        = config('token.jwt_key');

        // 获取过期时间
        $expireTime = $type === 'access_token' ? $this->accessTokenExpire : $this->refreshTokenExpire;

        // 当前时间
        $time = time();

        // 构建payload
        $payload = [
            'iss'       => $issuer, // 签发者
            'sub'       => $audience, // 面向用户
            'iat'       => $time, // 签发时间
            'exp'       => $time + $expireTime, // 过期时间
            'nbf'       => $time, // 定义在什么时间之前，该jwt都是不可用的
            'jti'       => md5(uniqid($type) . $time), // token唯一标识
            'uid'       => $userData['id'], // 用户id
            'tenant_id' => $userData['tenant_id'], // 租户id
        ];

        // 可选添加IP地址
        if ($includeIp) {
            $payload['cip'] = request()->ip();
        }

        // 可选添加设备信息
        if ($includeDevice) {
            $payload['device'] = request()->header('user-agent');
        }

        // 使用Firebase JWT库生成token
        return JWT::encode($payload, $jwtKey, $algorithm);
    }

    /**
     * 移除token
     * @param string $token 要移除的token
     * @param int|null $tenantId 租户ID
     * @param int|null $userId 用户ID
     * @param string $type token类型，默认为access_token
     * @param bool $removeUserData 是否同时移除用户数据缓存
     * @return bool
     */
    public function removeToken(string $token = '', ?int $tenantId = null, ?int $userId = null, string $type = 'access_token', bool $removeUserData = false): bool
    {
        $result = true;

        // 获取当前登录模式
        $loginMode  = $this->getLoginMode();
        $loginModes = $this->getLoginModes();

        // 如果提供了租户ID和用户ID
        if ($tenantId !== null && $userId !== null) {
            // 根据登录模式处理token清除
            switch ($loginMode) {
                case $loginModes['SINGLE']:
                    // 单点登录模式：清除所有token
                    $accessTokenCacheKey  = $this->getCacheKey($tenantId, $userId, 'access_token');
                    $refreshTokenCacheKey = $this->getCacheKey($tenantId, $userId, 'refresh_token');
                    Cache::delete($accessTokenCacheKey);
                    Cache::delete($refreshTokenCacheKey);
                    break;
                case $loginModes['MULTIPLE_SEPARATE']:
                    // 多点独立token模式：只清除当前设备的token
                    $tokenCacheKey = $this->getCacheKey($tenantId, $userId, $type);
                    $result        = Cache::delete($tokenCacheKey);

                    // 如果是清除access_token，同时清除对应的refresh_token
                    if ($type === 'access_token') {
                        $refreshTokenCacheKey = $this->getCacheKey($tenantId, $userId, 'refresh_token');
                        Cache::delete($refreshTokenCacheKey);
                    }
                    break;
                case $loginModes['MULTIPLE_SHARE']:
                    // 多点共享token模式：清除共享的token
                    $accessTokenCacheKey  = $this->getCacheKey($tenantId, $userId, 'access_token');
                    $refreshTokenCacheKey = $this->getCacheKey($tenantId, $userId, 'refresh_token');
                    Cache::delete($accessTokenCacheKey);
                    Cache::delete($refreshTokenCacheKey);
                    break;
                default:
                    // 默认行为：清除指定类型的token
                    $tokenCacheKey = $this->getCacheKey($tenantId, $userId, $type);
                    $result        = Cache::delete($tokenCacheKey);
            }

            // 如果需要同时移除用户数据
            if ($removeUserData) {
                $userDataCacheKey = $this->getCacheKey($tenantId, $userId, 'user_data');
                Cache::delete($userDataCacheKey);
            }

            return $result;
        }

        // 如果只提供了token，保持原有的删除逻辑
        if (!empty($token)) {
            // 查找所有可能匹配的缓存键
            $keys = self::getRedisKeys($this->tokenPrefix . '*:*:' . $type);

            // 遍历所有匹配的键，查找并删除包含当前token的缓存数据
            foreach ($keys as $key) {
                $cacheData = CacheService::getInstance()->get($key);

                if ($cacheData && isset($cacheData['token']) && $cacheData['token'] === $token) {
                    $result = Cache::delete($key);

                    // 从缓存键中提取租户ID和用户ID
                    $keyParts = explode(':', $key);

                    if (count($keyParts) >= 3) {
                        $extractedTenantId = (int)$keyParts[1];
                        $extractedUserId   = (int)$keyParts[2];

                        // 根据登录模式处理关联token
                        switch ($loginMode) {
                            case $loginModes['SINGLE']:
                            case $loginModes['MULTIPLE_SHARE']:
                                // 单点登录和多点共享模式：清除所有token
                                if ($type === 'access_token') {
                                    $refreshTokenCacheKey = $this->getCacheKey($extractedTenantId, $extractedUserId, 'refresh_token');
                                    Cache::delete($refreshTokenCacheKey);
                                } elseif ($type === 'refresh_token') {
                                    $accessTokenCacheKey = $this->getCacheKey($extractedTenantId, $extractedUserId, 'access_token');
                                    Cache::delete($accessTokenCacheKey);
                                }
                                break;
                            case $loginModes['MULTIPLE_SEPARATE']:
                                // 多点独立token模式：只清除当前设备的token
                                if ($type === 'access_token') {
                                    $refreshTokenCacheKey = $this->getCacheKey($extractedTenantId, $extractedUserId, 'refresh_token');
                                    Cache::delete($refreshTokenCacheKey);
                                } elseif ($type === 'refresh_token') {
                                    $accessTokenCacheKey = $this->getCacheKey($extractedTenantId, $extractedUserId, 'access_token');
                                    Cache::delete($accessTokenCacheKey);
                                }
                                break;
                        }

                        // 如果需要同时移除用户数据
                        if ($removeUserData) {
                            $userDataCacheKey = $this->getCacheKey($extractedTenantId, $extractedUserId, 'user_data');
                            Cache::delete($userDataCacheKey);
                        }
                    }

                    return $result;
                }
            }
        }

        return true;
    }
}
