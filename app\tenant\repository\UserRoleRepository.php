<?php
declare(strict_types = 1);

namespace app\tenant\repository;

use app\tenant\model\UserRole as UserRoleModel;
use think\facade\Db;

/**
 * 租户用户角色数据仓库类
 * 负责租户角色数据的访问和操作
 * <AUTHOR> <<EMAIL>>
 * @package app\tenant\controller
 */
class UserRoleRepository extends BaseRepository
{
    public function __construct()
    {
        // 指定当前模型类名而非实例，避免条件累积问题
        parent::__construct(UserRoleModel::class);
    }

    /**
     * 分配角色给用户
     * @param int $userId
     * @param array $roleIds
     * @param array $specialPermissions 特别权限 [roleId => ['permission1', 'permission2']]
     * @throws \Exception
     * @return bool
     */
    public function assignRoles(int $userId, array $roleIds, array $specialPermissions = []): bool
    {
        Db::startTrans();

        try {
            // 先删除用户现有角色
            $this->model->where('user_id', $userId)->delete();

            // 重新分配角色
            $data = [];

            foreach ($roleIds as $roleId) {
                $data[] = [
                    'tenant_id'           => $this->getTenantId(),
                    'user_id'             => $userId,
                    'role_id'             => $roleId,
                    'special_permissions' => $specialPermissions[$roleId] ?? [],
                ];
            }

            if (!empty($data)) {
                $this->model->insertAll($data);
            }

            Db::commit();

            return true;
        } catch (\Exception $e) {
            Db::rollback();

            throw $e;
        }
    }

    /**
     * 获取用户的角色列表(只返回有效的角色)
     * @param int $userId
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return array
     */
    public function getUserRoles(int $userId): array
    {
        $roles = $this->model->with(['role' => function($query) {
            // 只查询当前租户下的角色组
            $query->where('tenant_id', $this->getTenantId());
            // 只查询状态正常的角色
            $query->where('status', '=', 1);
        }])->where([
            'tenant_id' => $this->getTenantId(),
            'user_id'   => $userId,
        ])->select()->toArray();

        // 过滤掉role为null的数据
        return array_values(array_filter($roles, function($item) {
            return !empty($item['role']);
        }));
    }

    /**
     * 获取用户的角色ID列表(只返回有效的角色)
     * @param int $userId
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return array
     */
    public function getUserRoleIds(int $userId): array
    {
        $roles = $this->getUserRoles($userId);

        return array_column(array_column($roles, 'role'), 'id');
    }
}
