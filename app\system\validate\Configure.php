<?php
namespace app\system\validate;

/**
 * 配置验证器类
 * <AUTHOR> <<EMAIL>>
 * @package app\system\validate
 */
class Configure extends BaseValidate
{
    protected $rule = [
        'id'          => 'require',
        'name'        => 'require|max:25|unique:system_configure,name',
        'label'       => 'require|max:25|unique:system_configure,label',
        'data'        => 'require',
        'group'       => 'require|max:25',
        'type'        => 'require|checkWidgetType',
        'value'       => 'max:1000',
        'options'     => 'max:1000',
        'placeholder' => 'max:100',
        'description' => 'max:200',
        'sort'        => 'between:-1000,1000',
        'hidden'      => 'in:0,1',
        'status'      => 'in:0,1',
    ];

    /**
     * 自定义验证规则：检查控件类型是否合法
     * @param mixed $value
     * @param mixed $rule
     * @param array $data
     * @return bool|string
     */
    protected function checkWidgetType($value, $rule, $data = [])
    {
        $repository  = app()->make('app\\system\\repository\\ConfigureRepository');
        $widgetTypes = $repository->getWidgetTypes();

        return isset($widgetTypes[$value]) ? true : '控件类型不在允许的范围内';
    }

    protected $message = [
        'id.require'    => 'ID不能为空',
        'label.require' => '配置名称不能为空',
        'label.max'     => '配置名称最多不能超过25个字符',
        'label.unique'  => '配置名称已存在',
        'data.require'  => '配置数据不能为空',
        'group.require' => '所属分组不能为空',
        'group.max'     => '所属分组最多不能超过25个字符',
        'type.require'  => '控件类型为必选',
        'sort.between'  => '排序号只能是-1000到1000之间的整数值',
        'hidden.in'     => '隐藏状态错误',
        'status.in'     => '状态值错误01',
        'name.require'  => '配置标识不能为空',
        'name.max'      => '配置标识最多不能超过25个字符',
        'name.unique'   => '配置标识已存在',
    ];

    protected $scene = [
        'setting'  => ['group'],
        'saveData' => ['data', 'group'],
        'add'      => ['label','group','type','sort'],
        'edit'     => ['id','label','group','type','sort'],
        'hidden'   => ['id', 'hidden'],
        'status'   => ['id', 'status'],
        'delete'   => ['id'],
    ];
}
