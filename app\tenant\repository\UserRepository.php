<?php
namespace app\tenant\repository;

use app\tenant\model\User as UserModel;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

/**
 * 租户用户数据仓库类
 * 负责租户用户相关的数据访问和操作
 * <AUTHOR> <<EMAIL>>
 * @package app\tenant\repository
 */
class UserRepository extends BaseRepository
{
    /**
     * 构造方法
     */
    public function __construct()
    {
        // 指定当前模型类名而非实例，避免条件累积问题
        parent::__construct(UserModel::class);
    }

    /**
     * 获取用户列表（支持高级筛选）
     * @param array $params 查询参数
     * @param int $pageNo 页码
     * @param int $pageSize 每页数量
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return array
     */
    public function getList(array $params = [], int $pageNo = 1, int $pageSize = 20): array
    {
        $tenantId = $params['tenant_id'] ?? $this->getTenantId();

        // 设置需要排除的敏感字段
        $this->setCondition(['password'], 'withoutField');

        // 设置基础查询条件 - 非删除数据
        $this->setCondition([
            // 指定当前租户数据
            'tenant_id' => $tenantId,
            ['status', '>=', 0],
        ]);

        // 设置搜索条件
        if (!empty($params['keyword'])) {
            $this->setCondition([
                function($query) use ($params) {
                    $query->whereOr([
                        ['username', 'LIKE', "%{$params['keyword']}%"],
                        ['realname', 'LIKE', "%{$params['keyword']}%"],
                        ['mobile', 'LIKE', "%{$params['keyword']}%"],
                    ]);
                },
            ]);
        }

        // 复用通用分页方法
        $result = $this->pageList($params, $pageNo, $pageSize);

        // 为每个用户设置角色ID数组
        foreach ($result['list'] as &$user) {
            $user['role_ids'] = (new UserRoleRepository())->getUserRoleIds($user['id']);
        }

        return $result;
        /*// 如果没有数据，直接返回
        if (empty($result['list'])) {
            return $result;
        }

        // 提取所有用户ID
        $userIds = [];
        foreach ($result['list'] as $user) {
            $userIds[] = $user['id'];
        }

        // 批量查询用户角色关系
        $userRoleMap = [];
        if (!empty($userIds)) {
            // 查询用户角色数据
            $userRoles = UserRoleModel::alias('ur')
                ->join('tenant_role r', 'r.id = ur.role_id')
                ->where([
                    'ur.tenant_id' => $tenantId,
                    ['ur.user_id', 'in', $userIds],
                    ['r.status', '=', 1]  // 只查询有效角色
                ])
                ->field('ur.user_id, ur.role_id')
                ->select()
                ->toArray();

            // 构建用户ID到角色ID数组的映射
            foreach ($userRoles as $ur) {
                if (!isset($userRoleMap[$ur['user_id']])) {
                    $userRoleMap[$ur['user_id']] = [];
                }
                $userRoleMap[$ur['user_id']][] = $ur['role_id'];
            }
        }

        // 为每个用户设置角色ID数组
        foreach ($result['list'] as &$user) {
            $user['role_ids'] = $userRoleMap[$user['id']] ?? [];
        }

        return $result;*/
    }

    /**
     * 根据用户ID查找用户
     * @param int $userId
     * @param int $tenantId 租户ID
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return \app\tenant\model\User|null
     */
    public function findByUserId(int $userId, int $tenantId): ?UserModel
    {
        return $this->model->where([
            'id'        => $userId,
            'tenant_id' => $tenantId,
        ])->find();
    }

    /**
     * 根据用户名查找用户
     * @param string $username
     * @param int $tenantId 租户ID
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return \app\tenant\model\User|null
     */
    public function findByUsername(string $username, int $tenantId): ?UserModel
    {
        return $this->model->where([
            'tenant_id' => $tenantId,
            'username'  => $username,
        ])->find();
    }

    /**
     * 根据手机号查找用户
     * @param string $mobile
     * @param int $tenantId 租户ID
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return \app\tenant\model\User|null
     */
    public function findByMobile(string $mobile, int $tenantId): ?UserModel
    {
        return $this->model->where([
            'tenant_id' => $tenantId,
            'mobile'    => $mobile,
        ])->find();
    }

    /**
     * 根据用户名或手机号查找用户
     * @param string $username 用户名
     * @param string $mobile 手机号
     * @param int|null $excludeId 排除的用户ID
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return \app\tenant\model\User|null
     */
    public function findByUsernameOrMobile(string $username, string $mobile, ?int $excludeId = null): ?UserModel
    {
        $query = $this->model->where('tenant_id', $this->getTenantId())
            ->where(function($query) use ($username, $mobile) {
                $query->whereOr([
                    ['username', '=', $username],
                    ['mobile', '=', $mobile],
                ]);
            });

        if ($excludeId !== null) {
            $query->where('id', '<>', $excludeId);
        }

        return $query->find();
    }

    /**
     * 获取有效租户用户信息
     * @param int $userId
     * @return array|mixed
     */
    public function getTenantUserById(int $userId): mixed
    {
        try {
            $userInfo = $this->model->where(['id' => $userId, 'tenant_id' => $this->getTenantId()])->where('status', '>=', 0)->find();

            if (!empty($userInfo)) {
                return $userInfo->toArray();
            }
        } catch (DataNotFoundException|DbException|ModelNotFoundException $e) {
        }

        return null;
    }

    /**
     * 创建租户新用户
     * @param array $data
     * @return bool
     */
    public function createTenantUser(array $data): bool
    {
        $data['tenant_id'] ??= $this->getTenantId();

        return $this->model->save($data);
    }

    /**
     * 创建租户管理员
     * @param int $tenantId 租户ID
     * @param string $password 密码
     * @param string $username 用户名
     * @return bool
     */
    public function createTenantAdmin(int $tenantId = 0, string $password = '', string $username = 'root'): bool
    {
        $user['tenant_id'] = $tenantId ?: $this->getTenantId();
        $user['username']  = $username;
        $user['realname']  = '系统管理员';
        $user['password']  = $password ?: md5(generateRandomPassword(8)); // 初始密码，建议使用随机密码
        $user['founder']   = 1;
        $user['status']    = 1;

        return $this->createTenantUser($user);
    }

    /**
     * 更新用户信息
     * @param int $userId
     * @param array $data
     * @return bool
     */
    public function updateUser(int $userId, array $data): bool
    {
        if (empty($data['password'])) {
            unset($data['password']);
        }

        return (bool)$this->model->update($data, ['id' => $userId]);
    }
}
