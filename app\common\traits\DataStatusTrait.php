<?php
namespace app\common\traits;

/**
 * 数据状态复用类
 * <AUTHOR> <<EMAIL>>
 * @package app\common\traits
 */
trait DataStatusTrait
{
    /**
     * 状态常量定义
     */
    public const STATUS_DELETED = -1;  // 已删除

    public const STATUS_DISABLED = 0;  // 已禁用

    public const STATUS_ENABLE = 1;    // 正常

    /**
     * 获取状态文本
     * @param int $status 状态值
     * @return string 状态描述文本
     */
    public static function getStatusText(int $status): string
    {
        $statusMap = [
            self::STATUS_DELETED  => '已删除',
            self::STATUS_DISABLED => '已禁用',
            self::STATUS_ENABLE   => '正常',
        ];

        return $statusMap[$status] ?? '未知';
    }

    /**
     * 启用指定数据记录（支持批量）
     * 通过setStatus方法将状态设置为启用(1)
     * @param mixed $ids 要启用的数据记录主键ID
     * @throws \Exception
     * @return bool 成功返回更新后的模型对象，失败返回false
     */
    public static function enable(mixed $ids): bool
    {
        // 调用 setStatus 方法，将状态设置为 1（启用）
        return self::setStatus($ids, self::STATUS_ENABLE);
    }

    /**
     * 禁用指定数据记录（支持批量）
     * 通过setStatus方法将状态设置为禁用(0)
     * @param mixed $ids 要禁用的数据记录主键ID
     * @throws \Exception
     * @return bool 成功返回更新后的模型对象，失败返回false
     */
    public static function disable(mixed $ids): bool
    {
        // 调用 setStatus 方法，将状态设置为 0（禁用）
        return self::setStatus($ids, self::STATUS_DISABLED);
    }

    /**
     * 回收（软删除）指定数据记录（支持批量）
     * 通过setStatus方法将状态设置为已删除(-1)
     * @param mixed $ids 要删除的数据记录主键ID
     * @throws \Exception
     * @return bool 成功返回更新后的模型对象，失败返回false
     * @return bool
     */
    public static function recycle(mixed $ids): bool
    {
        // 调用 setStatus 方法，将状态设置为 -1（已删除）
        return self::setStatus($ids, self::STATUS_DELETED);
    }

    /**
     * 设置状态
     * @param mixed $ids 单个ID或ID数组
     * @param int $status 状态值
     * @throws \Exception
     * @return bool
     */
    public static function setStatus(mixed $ids, int $status = 0): bool
    {
        if (!in_array($status, [self::STATUS_DELETED, self::STATUS_DISABLED, self::STATUS_ENABLE])) {
            return false;
        }

        $data = [
            'status'    => $status,
            'update_at' => time(),
        ];

        // 如果有删除时间字段的话就更新删除时间
        /*if ($status === self::STATUS_DELETED) {
            $data['delete_at'] = time();
        }*/

        // 转换成数组
        $ids = is_array($ids) ? $ids : explode(',', (string)$ids);

        // 开启事务
        self::startTrans();

        try {
            $result = (bool)self::whereIn('id', $ids)->update($data);
            // 提交事务
            self::commit();

            return $result;
        } catch (\Exception $e) {
            // 回滚事务
            self::rollback();

            throw $e;
        }
    }

    /**
     * 更新数据排序
     * @param int $id 记录ID
     * @param int $sort 排序值
     * @param string $filed 排序字段
     * @throws \Exception
     * @return bool 更新成功返回true，失败返回false
     */
    public static function updateSort(int $id, int $sort, string $filed = 'sort'): bool
    {
        // 开启事务
        self::startTrans();

        try {
            $result = (bool)self::where('id', $id)->update([$filed => $sort]);
            // 提交事务
            self::commit();

            return $result;
        } catch (\Exception $e) {
            // 回滚事务
            self::rollback();

            throw $e;
        }
    }
}
