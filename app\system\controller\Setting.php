<?php
declare(strict_types = 1);
namespace app\system\controller;

use app\system\repository\ConfigureRepository;
use app\system\validate\Configure as ConfigureValidate;
use think\App;
use think\exception\ValidateException;
use think\Response;

/**
 * 系统设置控制器
 * <AUTHOR> <<EMAIL>>
 * @package app\system\controller
 */
class Setting extends BaseController
{
    protected ConfigureRepository $repository;

    public function __construct(App $app, ConfigureRepository $repository)
    {
        parent::__construct($app);

        // 指定对应仓储类
        $this->repository = $repository;
    }

    /**
     * 获取全部分组和对应数据
     * @return \think\Response
     */
    public function groupListData(): Response
    {
        $data = $this->repository->getGroupListData();

        return $this->success($data);
    }

    /**
     * 获取某个分组和对应数据
     * @return \think\Response
     */
    public function getData(): Response
    {
        $params = $this->request->param();

        try {
            validate(ConfigureValidate::class)->scene('setting')->check($params);
        } catch (ValidateException $e) {
            return $this->error($e->getError());
        }

        $data = $this->repository->getGroupData($params['group']);

        return $this->success($data);
    }

    /**
     * 编辑配置项方法
     * @return \think\Response
     */
    public function saveData(): Response
    {
        $params = $this->request->param();

        try {
            validate(ConfigureValidate::class)->scene('saveData')->check($params);
        } catch (ValidateException $e) {
            return $this->error($e->getError());
        }

        if ($this->repository->saveGroupData($params['group'], $params['data'])) {
            return $this->success('更新成功');
        }

        return $this->error('更新失败');
    }

    /**
     * 设置状态（禁用启用）
     * @return \think\Response
     */
    public function status(): Response
    {
        $params = $this->request->param();

        try {
            validate(ConfigureValidate::class)->scene('status')->check($params);
        } catch (ValidateException $e) {
            return $this->error($e->getError());
        }

        if ($this->repository->setStatus($params['id'], $params['status'])) {
            // 写入事件日志
            $this->event_log('设置状态', $params, []);

            return $this->success($params['status'] == 0 ? '禁用成功' : '启用成功');
        }

        return $this->error('操作失败');
    }

    /**
     * 删除方法（软删除）
     * @return \think\Response
     */
    public function delete(): Response
    {
        $params = $this->request->param();

        try {
            validate(ConfigureValidate::class)->scene('delete')->check($params);
        } catch (ValidateException $e) {
            return $this->error($e->getError());
        }

        if ($this->repository->recycle($params['id'])) {
            // 写入事件日志
            $this->event_log('删除配置项', $params, []);

            return $this->success('删除成功');
        }

        return $this->error('删除失败');
    }
}
