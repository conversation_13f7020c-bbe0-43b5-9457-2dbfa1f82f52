<?php
declare(strict_types = 1);

namespace app\tenant\controller;

use app\system\controller\BaseController;
use app\tenant\model\Tenant as TenantModel;
use app\tenant\repository\MqttRepository;
use app\tenant\repository\TenantRepository;
use app\tenant\validate\Tenant as TenantValidate;
use think\App;
use think\Response;

/**
 * 租户管理模块（用于总后台）
 * <AUTHOR> <<EMAIL>>
 * @package app\tenant\controller
 */
class Manage extends BaseController
{
    public function __construct(App $app)
    {
        parent::__construct($app);
    }

    /**
     * 租户列表
     * @return \think\Response
     */
    public function list(): Response
    {
        $params   = $this->request->param();
        $pageNo   = $this->request->param('page', 1, 'intval');
        $pageSize = $this->request->param('pageSize', 20, 'intval');

        $result = (new TenantRepository())->getList($params, $pageNo, $pageSize);

        return $this->success($result);
    }

    /**
     * 添加租户
     * @return Response
     */
    public function add(): Response
    {
        $params = $this->request->post();

        // 验证数据
        $validate = new TenantValidate();

        if (!$validate->scene('add')->check($params)) {
            return $this->error($validate->getError());
        }

        $result = (new TenantRepository())->addTenant($params);

        if ($result['status']) {
            // 写入事件日志
            $this->event_log('添加租户', $params, $result);

            return $this->success("添加成功，默认管理员信息：租户编码{$result['tenantCode']}，用户名{$result['username']}，密码{$result['password']}");
        }

        return $this->error('添加失败');
    }

    /**
     * 编辑租户
     * @return \think\Response
     */
    public function edit(): Response
    {
        $params = $this->request->post();

        if (empty($params['id'])) {
            return $this->error('参数错误');
        }

        // 验证数据
        $validate = new TenantValidate();

        if (!$validate->scene('edit')->check($params)) {
            return $this->error($validate->getError());
        }

        $result = (new TenantRepository())->editTenant($params['id'], $params);

        if ($result) {
            // 写入事件日志
            $this->event_log('编辑租户', $params, []);

            return $this->success('更新成功');
        }

        return $this->error('更新失败');
    }

    /**
     * 删除租户
     * @return \think\Response
     */
    public function delete(): Response
    {
        $id = $this->request->param('id');

        if (empty($id)) {
            return $this->error('参数错误');
        }

        if ((new TenantRepository())->recycle($id)) {
            $this->event_log('删除租户', ['id' => $id], []);

            return $this->success('删除成功');
        }

        return $this->error('删除失败');
    }

    /**
     * 批量删除租户
     * @return \think\Response
     */
    public function batchDelete(): Response
    {
        $ids = $this->request->param('ids');

        if (!is_array($ids)) {
            return $this->error('参数错误');
        }

        if ((new TenantRepository())->recycle($ids)) {
            $this->event_log('批量删除租户', ['ids' => $ids], []);

            return $this->success('批量删除成功');
        }

        return $this->error('批量删除失败');
    }

    /**
     * 设置租户排序
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return \think\Response
     */
    public function sort(): Response
    {
        $params = $this->request->post();

        if (empty($params['id']) || !isset($params['sort'])) {
            return $this->error('参数错误');
        }

        $tenant = TenantModel::find($params['id']);

        if (!$tenant) {
            return $this->error('租户不存在');
        }

        $tenant->sort = intval($params['sort']);
        $tenant->save();

        // 记录操作日志
        $this->event_log('设置租户排序', $params, []);

        return $this->success('排序设置成功');
    }

    /**
     * 获取租户MQTT客户端认证列表
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return \think\Response
     */
    public function mqttClientAuthList(): Response
    {
        $params   = $this->request->param();
        $pageNo   = $this->request->param('page', 1, 'intval');
        $pageSize = $this->request->param('pageSize', 20, 'intval');
        $tenantId = $this->request->param('tenant_id', 0, 'intval');

        $repository = new MqttRepository();
        $result     = $repository->getClientAuthList($params, $pageNo, $pageSize, $tenantId);

        return $this->success($result);
    }

    /**
     * 获取租户MQTT主题授权列表
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return \think\Response
     */
    public function mqttTopicAclList(): Response
    {
        $params   = $this->request->param();
        $pageNo   = $this->request->param('page', 1, 'intval');
        $pageSize = $this->request->param('pageSize', 20, 'intval');
        $tenantId = $this->request->param('tenant_id', 0, 'intval');

        $repository = new MqttRepository();
        $result     = $repository->getTopicAclList($params, $pageNo, $pageSize, $tenantId);

        return $this->success($result);
    }

    /**
     * 批量设置租户的MQTT主题授权规则
     * @return Response
     */
    public function setTenantMqttAcl(): Response
    {
        $params = $this->request->post();

        if (empty($params['tenant_id']) || empty($params['rules'])) {
            return $this->error('参数错误');
        }

        $tenantId = (int)$params['tenant_id'];
        $rules    = $params['rules'];

        if (!is_array($rules)) {
            return $this->error('规则格式错误');
        }

        $repository = new MqttRepository();
        $result     = $repository->batchSetTenantAcl($tenantId, $rules);

        if ($result) {
            // 写入事件日志
            $this->event_log('设置租户MQTT主题授权规则', ['tenant_id' => $tenantId], []);

            return $this->success('设置成功');
        }

        return $this->error('设置失败');
    }
}
