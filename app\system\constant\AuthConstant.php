<?php
declare(strict_types = 1);
namespace app\system\constant;

use app\common\constant\CommonConstant;

/**
 * 权限验证相关常量 (10300-10350)
 * <AUTHOR> <<EMAIL>>
 * @package app\system\constant
 */
class AuthConstant extends CommonConstant
{
    // 该code在客户端需单独判断
    public const AUTH_NO_PERMISSION = ['code' => 10300, 'message' => '无接口请求权限', 'label' => 'no_auth'];

    /**
     * 根据code获取message
     * @param int|string $code
     * @return string
     */
    public static function getMessage(int|string $code): string
    {
        foreach (static::getConstants() as $constant) {
            if ($constant['code'] === $code) {
                return $constant['message'];
            }
        }

        return '未知错误';
    }

    public static function getLabel(int|string $code): string
    {
        foreach (static::getConstants() as $constant) {
            if ($constant['code'] === $code) {
                return $constant['label'] ?? '';
            }
        }

        return '';
    }

    private static function getConstants(): array
    {
        $ref = new \ReflectionClass(static::class);

        return $ref->getConstants();
    }
}
