<?php
declare(strict_types = 1);

namespace app\system\model;

use think\model\relation\BelongsTo;

/**
 * 部门用户关联模型
 * <AUTHOR> <<EMAIL>>
 * @package app\system\model
 */
class DepartmentUser extends BaseModel
{
    protected $name = 'system_department_user';

    protected $field = [];

    /**
     * 获取关联的用户信息
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    /**
     * 获取关联的部门信息
     * @return BelongsTo
     */
    public function department(): BelongsTo
    {
        return $this->belongsTo(Department::class, 'dept_id', 'id');
    }
}
