<?php
declare(strict_types = 1);

namespace app\tenant\constant;

use app\tenant\service\login\AccountLoginStrategy;
use app\tenant\service\login\MobileLoginStrategy;
use app\tenant\service\login\RefreshTokenLoginStrategy;

/**
 * 登录类型配置类
 * <AUTHOR> <<EMAIL>>
 * @package app\tenant\constant
 */
class LoginType
{
    /**
     * 登录策略类型配置（增加其它登录类型，直接从这里加就可以了）
     * @var array
     */
    public static array $config = [
        // 账号密码登录类型
        'account' => [
            'scene'          => 'login_account', // 验证场景
            'strategy'       => AccountLoginStrategy::class, // 对应的登录策略
            'error_suffix'   => '账号或密码错误', // 错误信息后缀
            'identifier_key' => 'username', // 账号标识字段
        ],
        // 手机验证码登录类型
        'mobile' => [
            'scene'          => 'login_mobile',
            'strategy'       => MobileLoginStrategy::class,
            'error_suffix'   => '手机号或验证码错误',
            'identifier_key' => 'mobile', // 账号标识字段
        ],
        // refresh_token登录类型
        'refresh_token' => [
            'scene'          => 'login_refresh_token',
            'strategy'       => RefreshTokenLoginStrategy::class,
            'error_suffix'   => 'refresh_token无效或已过期',
            'identifier_key' => 'refresh_token',
        ],
    ];

    /**
     * 获取默认登录类型
     * @return string
     */
    public static function getDefault(): string
    {
        return array_key_first(self::$config);
    }

    /**
     * 获取登录类型配置
     * @param string $type
     * @return array|null
     */
    public static function getConfig(string $type): ?array
    {
        return self::$config[$type] ?? null;
    }

    /**
     * 获取验证场景
     * @param ?string $type
     * @return string
     */
    public static function getScene(?string $type): string
    {
        if (empty($type)) {
            return '';
        }

        return self::getConfig($type)['scene'] ?? '';
    }

    /**
     * 获取错误信息后缀
     * @param string $type
     * @return string
     */
    public static function getErrorSuffix(string $type): string
    {
        return self::getConfig($type)['error_suffix'] ?? '';
    }

    /**
     * 获取策略类名
     * @param string $type
     * @return string|null
     */
    public static function getStrategy(string $type): ?string
    {
        return self::getConfig($type)['strategy'] ?? null;
    }

    /**
     * 获取登录标识字段
     * @param string $type
     * @return string|null
     */
    public static function getIdentifierKey(string $type): ?string
    {
        return self::getConfig($type)['identifier_key'] ?? null;
    }

    /**
     * 获取登录失败次数的键值
     * @param string $loginType 登录类型
     * @param string $tenantCode 租户编码
     * @param string $account 登录账户
     * @return string
     */
    public static function getFailKey(string $loginType, string $tenantCode, string $account): string
    {
        return 'login_fail_' . md5($loginType . '_' . $tenantCode . '_' . $account . '_' . ip2long(request()->ip()));
    }

    /**
     * 验证登录类型是否合法
     * @param ?string $type
     * @return bool
     */
    public static function isValid(?string $type): bool
    {
        return isset(self::$config[$type]);
    }
}
