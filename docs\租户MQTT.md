# 租户MQTT连接指南

## 服务配置

### 客户端认证配置

配置方法：
1. 安装完emqx服务后，打开浏览器输入：http://127.0.0.1:18083，访问emqx服务管理界面，修改密码后记住。
2. 打开左侧菜单栏->访问控制->客户端认证，启用HTTP服务（最好关闭其它的认证方式）。
3. 然后点后面的设置：
    - 请求方式改为POST
    - URL改为http://url/tenant/mqtt/authenticate
    - 添加请求头：content-type，值：application/json
    - 添加请求头：X-Request-Source：值：EMQX
    - 添加请求头：X-Client-Id，值：hl:mqtt（具体根据当前应用去定义）
4. 启用TLS，暂时不启用，如果 URL 为 https，必须同时启用 ssl。
5. 请求体输入：
    ```json
    {
      "clientid": "${clientid}",
      "username": "${username}",
      "password": "${password}",
      "client_attrs": "${client_attrs}"
    }
    ```
6. 点击保存，完成配置。

### 客户端授权配置

配置方法：
1. 打开左侧菜单栏->访问控制->客户端授权，启用HTTP服务（最好关闭其它的授权方式）。
2. 然后点后面的设置：
    - 请求方式改为POST
    - URL改为http://url/tenant/mqtt/authorize
    - 添加请求头：content-type，值：application/json
    - 添加请求头：X-Request-Source：值：EMQX
    - 添加请求头：X-Client-Id，值：hl:mqtt（具体根据当前应用去定义）
3. 启用TLS，暂时不启用，如果 URL 为 https，必须同时启用 ssl。
4. 请求体输入：
    ```json
    {
      "action": "${action}",
      "client_attrs": "${client_attrs}",
      "clientid": "${clientid}",
      "mountpoint": "${mountpoint}",
      "peerhost": "${peerhost}",
      "topic": "${topic}",
      "username": "${username}"
    }
    ```
6. 点击保存，完成配置。

## 消息主题

- **主题格式**: {消息前缀}/{租户编码}/{租户用户ID}/{消息类型}
  - 示例：`helio/tenant001/10001/command`

## 后端接口

#### 1. 认证功能 (authenticate方法)：

- 支持服务器连接认证（超级用户）
- 支持租户客户端认证（通过token验证）
- 返回高效的ACL规则，避免每个操作都需要额外的授权查询
- 返回客户端属性，支持规则引擎和高级授权控制
- 设置合理的过期时间，确保客户端在token过期后需要重新连接

#### 2. 授权功能 (authorize方法)：

- 严格检查操作类型和对应的主题权限
- 实现了基于租户的主题隔离
- 支持对QoS级别和retain标志的限制
- 针对不同操作类型（publish/subscribe）实施不同的权限检查策略
- 详细的日志记录，便于问题排查

##### Mqtt控制器 (app/tenant/controller/Mqtt.php)

- 作为HTTP接口的入口点
- 负责接收EMQX发送的认证和授权请求
- 调用相应的服务处理请求
- 返回处理结果给EMQX

##### MqttAuthService (app/tenant/service/MqttAuthService.php)

- 处理认证逻辑
- 验证用户凭据
- 构建ACL规则和客户端属性
- 单例模式实现

##### MqttAclService (app/tenant/service/MqttAclService.php)

- 处理授权逻辑
- 检查发布和订阅权限
- 实现多租户隔离
- 限制QoS和retain标志的使用
- 单例模式实现

##### 这种结构有以下优点：

- 职责分离：每个类都有明确的单一职责
- 可维护性：逻辑分散到不同的文件，更易于理解和修改
- 可扩展性：可以独立扩展认证和授权逻辑，而不互相影响
- 可测试性：服务类可以单独进行单元测试
- 代码复用：服务类可在系统的其他部分重用


## 服务端连接认证

服务端连接MQTT服务器时需要进行认证，认证信息包括：
- **clientId前缀**: 必须与后端配置一致（服务端连接要求为`helio_`，云端要求为`cloud_`，客户端要求为`client_`）
- **用户名**: 由后端提供
- **密码**: 由后端提供

## 客户端连接认证

客户端连接MQTT服务器时需要进行认证，认证信息包括用户名和密码：

- **用户名格式**: {租户用户ID@租户编码}
  - 示例：`10001@tenant001`

- **密码**: 使用租户登录成功后返回的access_token

认证流程：
1. 租户通过系统登录接口获取access_token
2. 使用租户用户名和租户编码按照指定格式构造用户名
3. 使用access_token作为密码进行MQTT连接
4. 服务器验证用户名格式和密码有效性后建立连接

注意事项：
- 用户名中的租户编码必须与access_token对应的租户一致
- access_token具有时效性，过期后需要重新获取
- 连接失败时请检查用户名格式和access_token是否有效

## Keepalive配置

Keepalive参数用于控制MQTT客户端与服务器之间的心跳间隔，单位为秒。默认值为0，表示禁用keepalive机制。

- **建议值**: 60-120秒
- **作用**: 检测客户端与服务器之间的连接状态，及时处理断线情况
- **配置方法**: 后端通过环境变量MQTT.MQTT_KEEPALIVE进行配置，前端通过配置文件进行配置
- **注意事项**:
  - 设置过小会增加网络负担
  - 设置过大可能导致连接状态检测不及时
  - 生产环境建议设置为60-120秒之间
