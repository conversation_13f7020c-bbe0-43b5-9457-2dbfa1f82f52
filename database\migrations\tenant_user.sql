-- 表结构: tenant_user
CREATE TABLE `tenant_user` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户ID',
  `username` varchar(30) NOT NULL COMMENT '用户账号',
  `realname` varchar(50) NOT NULL DEFAULT '' COMMENT '真实姓名',
  `password` varchar(200) NOT NULL COMMENT '密码',
  `founder` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否创始人',
  `avatar` varchar(255) NOT NULL DEFAULT '' COMMENT '头像',
  `email` varchar(50) NOT NULL DEFAULT '' COMMENT '邮箱',
  `mobile` varchar(11) NOT NULL DEFAULT '' COMMENT '手机号',
  `birthday` date DEFAULT NULL COMMENT '生日',
  `gender` tinyint(1) NOT NULL DEFAULT '0' COMMENT '性别:0=未知,1=男,2=女',
  `department` varchar(100) NOT NULL DEFAULT '' COMMENT '部门',
  `position` varchar(100) NOT NULL DEFAULT '' COMMENT '职位',
  `last_login_ip` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '最后登录IP',
  `last_login_time` int(10) DEFAULT '0' COMMENT '最后登录时间',
  `login_times` int(10) unsigned DEFAULT '0' COMMENT '登录次数',
  `remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注',
  `create_at` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_at` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  `delete_at` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '删除时间',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态:0=禁用,1=启用',
  PRIMARY KEY (`id`),
  KEY `idx_tenant_id` (`tenant_id`) USING BTREE,
  KEY `uk_tenant_username` (`tenant_id`,`username`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='租户用户表';
