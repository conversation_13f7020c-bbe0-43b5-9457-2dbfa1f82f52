<?php
declare(strict_types = 1);

namespace app\system\controller;

use app\system\repository\OperationLogRepository;
use think\App;
use think\Response;

/**
 * 操作日志控制器
 * 负责系统操作日志的管理功能
 * <AUTHOR> <<EMAIL>>
 * @package app\system\controller
 */
class OperationLog extends BaseController
{
    /**
     * @var OperationLogRepository
     */
    protected OperationLogRepository $repository;

    public function __construct(App $app, OperationLogRepository $repository)
    {
        parent::__construct($app);

        $this->repository = $repository;
    }

    /**
     * 获取日志列表
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return \think\Response
     */
    public function index(): Response
    {
        $params = $this->request->param();

        $result = $this->repository->getList($this->getUserInfo(), $params);

        return $this->success($result);
    }

    /**
     * 获取日志详情
     * @param int $id
     * @return \think\Response
     */
    public function read(int $id): Response
    {
        $info = $this->repository->find($id);

        if (empty($info)) {
            return $this->error('日志不存在');
        }

        return $this->success($info);
    }

    /**
     * 删除日志
     * @param int $id
     * @return \think\Response
     */
    public function delete(int $id): Response
    {
        if ($this->repository->deleteData($id)) {
            return $this->success('删除成功');
        }

        return $this->error('删除失败');
    }

    /**
     * 清空日志
     * @return \think\Response
     */
    public function clear(): Response
    {
        if ($this->repository->deleteAll()) {
            return $this->success('清空成功');
        }

        return $this->error('清空失败');
    }
}
