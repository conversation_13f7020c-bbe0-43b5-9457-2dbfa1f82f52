#!/bin/bash

# 代理设置函数库
# 使用方法: source proxy.sh

# 默认代理设置
DEFAULT_PROXY_HOST="**************"
DEFAULT_PROXY_PORT="3067"

# 配置文件路径
CONFIG_FILE="$HOME/.proxy_config"

# 保存代理配置
_proxy_save_config() {
    echo "PROXY_HOST=$1" > "$CONFIG_FILE"
    echo "PROXY_PORT=$2" >> "$CONFIG_FILE"
}

# 加载代理配置
_proxy_load_config() {
    if [ -f "$CONFIG_FILE" ]; then
        source "$CONFIG_FILE"
    else
        PROXY_HOST=$DEFAULT_PROXY_HOST
        PROXY_PORT=$DEFAULT_PROXY_PORT
    fi
}

# 显示代理状态
proxy_status() {
    if [ -n "$http_proxy" ] || [ -n "$HTTP_PROXY" ]; then
        echo "当前代理状态: 已启用"
        echo "HTTP 代理: $http_proxy"
        echo "HTTPS 代理: $https_proxy"
    else
        echo "当前代理状态: 未启用"
    fi
}

# 设置代理
proxy_set() {
    local host="$1"
    local port="$2"
    
    # 如果没有提供参数，加载配置文件
    if [ -z "$host" ] || [ -z "$port" ]; then
        _proxy_load_config
        host=${host:-$PROXY_HOST}
        port=${port:-$PROXY_PORT}
    fi
    
    # 设置环境变量
    export http_proxy="http://${host}:${port}"
    export https_proxy="http://${host}:${port}"
    export HTTP_PROXY="http://${host}:${port}"
    export HTTPS_PROXY="http://${host}:${port}"
    
    # 保存配置
    _proxy_save_config "$host" "$port"
    
    echo "代理已设置为 http://${host}:${port}"
}

# 取消代理
proxy_unset() {
    unset http_proxy
    unset https_proxy
    unset HTTP_PROXY
    unset HTTPS_PROXY
    
    if [ -f "$CONFIG_FILE" ]; then
        rm -f "$CONFIG_FILE"
    fi
    
    echo "代理已取消"
}

# 显示帮助信息
proxy_help() {
    echo "代理设置函数库"
    echo "用法: source proxy.sh"
    echo "注意: 必须使用source命令加载此脚本，否则代理设置将不会生效"
    echo ""
    echo "可用命令:"
    echo "  proxy_set [host] [port]    设置代理 (默认: $DEFAULT_PROXY_HOST:$DEFAULT_PROXY_PORT)"
    echo "  proxy_unset                取消代理"
    echo "  proxy_status               显示当前代理状态"
    echo "  proxy_help                 显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  proxy_set                  # 使用默认或已保存的设置启用代理"
    echo "  proxy_set ******** 8080    # 使用指定的主机和端口启用代理"
    echo "  proxy_unset                # 禁用代理"
}

# 如果直接执行脚本而不是source，显示警告
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    echo "错误: 此脚本必须使用 'source' 命令加载"
    echo "正确用法: source ${BASH_SOURCE[0]}，然后下次直接使用可用上面命令就可以了"
    exit 1
fi

# 加载时显示帮助信息
proxy_help
