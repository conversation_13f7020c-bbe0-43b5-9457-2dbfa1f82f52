<?php
namespace app\tenant\validate;

use app\tenant\model\Permission as PermissionModel;

/**
 * 权限验证器类
 * <AUTHOR> <<EMAIL>>
 * @package app\tenant\validate
 */
class Permission extends BaseValidate
{
    protected $rule = [
        'id'        => 'require|integer',
        'pid'       => 'integer',
        'path'      => 'max:255',
        'name'      => 'require|max:50|checkNameExist',
        'title'     => 'require|max:50',
        'icon'      => 'max:50',
        'type'      => 'require|in:menu,button,api',
        'component' => 'max:255',
        'redirect'  => 'max:255',
        'active'    => 'max:255',
        'tag'       => 'max:50',
        'is_show'   => 'in:0,1',
        'fullpage'  => 'in:0,1',
        'sort'      => 'between:-1000,1000',
        'status'    => 'in:0,1,-1',
        'apiList'   => 'array|checkApiListUnique',
    ];

    protected $message = [
        'id.require'                 => 'ID不能为空',
        'id.integer'                 => 'ID必须为整数',
        'pid.integer'                => '父节点ID只能为数字类型',
        'path.max'                   => '路由路径长度不能超过255个字符',
        'name.require'               => '节点标识不能为空',
        'name.max'                   => '节点标识长度不能超过50个字符',
        'title.require'              => '节点名称不能为空',
        'title.max'                  => '节点名称长度不能超过50个字符',
        'icon.max'                   => '图标标识长度不能超过50个字符',
        'type.require'               => '节点类型不能为空',
        'type.in'                    => '节点类型只能是菜单、按钮或API',
        'component.max'              => '模板视图路径长度不能超过255个字符',
        'redirect.max'               => '重定向路径长度不能超过255个字符',
        'active.max'                 => '菜单高亮长度不能超过255个字符',
        'tag.max'                    => '标签长度不能超过50个字符',
        'is_show.in'                 => '是否显示参数错误',
        'fullpage.in'                => '是否全屏参数错误',
        'sort.between'               => '排序号只能是-1000到1000之间的整数值',
        'status.in'                  => '状态值错误',
        'apiList.array'              => 'API列表必须是数组格式',
        'apiList.checkApiListUnique' => '接口权限列表中有重复项，请检查',
    ];

    protected $scene = [
        'add'    => ['pid','path','name','title','icon','type','component','redirect','active','tag','is_show','fullpage','sort','apiList'],
        'edit'   => ['id','pid','path','name','title','icon','type','component','redirect','active','tag','is_show','fullpage','sort','status','apiList'],
        'sort'   => ['id', 'sort'],
        'status' => ['id', 'status'],
        'delete' => ['id'],
    ];

    /**
     * 检查名称是否已存在
     * @param mixed $value 字段值
     * @param mixed $rule 验证规则
     * @param array $data 全部数据
     * @return bool|string
     */
    protected function checkNameExist(mixed $value, mixed $rule, array $data): bool|string
    {
        $id = isset($data['id']) ? intval($data['id']) : 0;

        if (PermissionModel::checkExist('name', $value, $id)) {
            return '当前节点标识已存在，请检查';
        }

        return true;
    }

    /**
     * 检查API列表中是否有重复项
     * @param mixed $value 字段值
     * @param mixed $rule 验证规则
     * @param array $data 所有数据
     * @return bool|string
     */
    protected function checkApiListUnique(mixed $value, mixed $rule, array $data): bool|string
    {
        if (empty($value) || !is_array($value)) {
            return true;
        }

        $name_validate = array_column($value, 'name');
        $url_validate  = array_column($value, 'url');

        if (count($name_validate) != count(array_unique($name_validate))) {
            return '接口权限列表中有重复接口名称，请检查';
        }

        if (count($url_validate) != count(array_unique($url_validate))) {
            return '接口权限列表中有重复接口地址，请检查';
        }

        return true;
    }
}
