<?php
declare(strict_types = 1);

namespace app\tenant\repository;

use app\tenant\model\Role as RoleModel;
use app\tenant\model\UserRole as UserRoleModel;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\Model;

/**
 * 租户角色数据仓库类
 * 负责租户角色相关的数据访问和操作
 * <AUTHOR> <<EMAIL>>
 * @package app\tenant\repository
 */
class RoleRepository extends BaseRepository
{
    /**
     * 构造方法
     */
    public function __construct()
    {
        // 指定当前模型类名而非实例，避免条件累积问题
        parent::__construct(RoleModel::class);
    }

    /**
     * 获取角色列表（支持高级筛选）
     * @param array $params 查询参数
     * @param int $pageNo 页码
     * @param int $pageSize 每页数量
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return array
     */
    public function getList(array $params = [], int $pageNo = 1, int $pageSize = 20): array
    {
        $tenantId = $params['tenant_id'] ?? $this->getTenantId();
        $keyword  = trim($params['keyword'] ?? '');
        $userId   = intval($params['user_id'] ?? 0);

        // 设置基础查询条件 - 非删除数据
        $this->setCondition([
            // 指定当前租户数据
            ['tenant_id', '=', $tenantId],
            ['status', '>=', 0],
        ]);

        // 设置搜索条件
        if (!empty($keyword)) {
            $this->setCondition([
                function($query) use ($keyword) {
                    $query->whereOr([
                        ['name', 'LIKE', "%{$keyword}%"],
                        ['code', 'LIKE', "%{$keyword}%"],
                        ['description', 'LIKE', "%{$keyword}%"],
                    ]);
                },
            ]);
        }

        // 获取分页数据
        $result = $this->pageList($params, $pageNo, $pageSize);

        // 如果传入了user_id，查询用户的角色分配情况
        if (!empty($userId)) {
            // 查询用户的角色分配情况
            $userRoleIds = $this->getUserRoleIds($userId);

            // 为每个角色添加是否已分配的标记
            foreach ($result['list'] as &$role) {
                $role['is_assigned'] = in_array($role['id'], $userRoleIds) ? 1 : 0;
            }
        } else {
            /*// 分离出当前列表中的角色ids
            $roleIds = array_column($result['list']->toArray(), 'id');
            // 查询角色设备关联数据
            $roleDevices = RoleDeviceModel::where([
                'tenant_id' => $tenantId,
                ['role_id', 'IN', $roleIds]
            ])->field('role_id, device_id')->select()->toArray();

            // 构建角色ID到设备ID的映射
            $deviceIdsMap = [];
            foreach ($roleDevices as $item) {
                if (!isset($deviceIdsMap[$item['role_id']])) {
                    $deviceIdsMap[$item['role_id']] = [];
                }
                $deviceIdsMap[$item['role_id']][] = $item['device_id'];
            }

            foreach ($result['list'] as &$role) {
                $role['device_ids'] = $deviceIdsMap[$role['id']] ?? [];
            }*/

            // 为每个角色添加device_ids属性
            foreach ($result['list'] as &$role) {
                $role['device_ids'] = (new RoleDeviceRepository())->getAssignedDeviceIds($role['id']);
            }
        }

        return $result;
    }

    /**
     * 获取用户已分配的角色ID列表
     * @param int $userId
     * @return array
     */
    private function getUserRoleIds(int $userId): array
    {
        $result = [];

        try {
            // 从用户角色关联表中查询
            $result = UserRoleModel::where(['user_id' => $userId, 'tenant_id' => $this->getTenantId()])->column('role_id');
        } catch (\Exception $e) {
            // 查询异常时返回空数组
        }

        return $result;
    }

    /**
     * 创建租户角色
     * @param array $params
     * @throws \Exception
     * @return \think\Model
     */
    public function addInfo(array $params): Model
    {
        $data = [
            'tenant_id'   => intval($params['tenant_id'] ?? $this->getTenantId()),
            'name'        => trim($params['name'] ?? ''),
            'code'        => trim($params['code'] ?? ''),
            'description' => trim($params['description'] ?? ''),
            'sort'        => intval($params['sort'] ?? 0),
            'status'      => 1,
        ];

        if (empty($data['tenant_id'])) {
            throw new \Exception('租户ID不能为空');
        }

        return $this->createData($data);
    }

    /**
     * 更新角色信息
     * @param int $roleId
     * @param array $data
     * @return bool
     */
    public function updateInfo(int $roleId, array $data): bool
    {
        unset(
            $data['id'],
            $data['create_at'],
        );

        return (bool)$this->model->update($data, ['id' => $roleId]);
    }

    /**
     * 获取有效租户角色信息
     * @param int $roleId
     * @return array|mixed
     */
    public function getTenantRoleById(int $roleId): mixed
    {
        try {
            return $this->model->where(['id' => $roleId, 'tenant_id' => $this->getTenantId()])->where('status', '>=', 0)->find();
        } catch (DataNotFoundException|DbException|ModelNotFoundException $e) {
            return null;
        }
    }
}
