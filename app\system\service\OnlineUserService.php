<?php
declare(strict_types = 1);

namespace app\system\service;

use app\common\service\FingerService;
use app\system\model\OperationLog as OperationLogModel;
use app\system\repository\UserRepository;
use think\facade\Log;

/**
 * 在线用户服务类
 * <AUTHOR> <<EMAIL>>
 * @package app\system\service
 */
class OnlineUserService extends BaseService
{
    // 用户活跃状态缓存tag标签
    public const CACHE_ACTIVE_TAG = 'active_list';

    // 所有缓存key前缀
    private const CACHE_PREFIX = 'black_list:';

    // 缓存key定义
    private const CACHE_KEY = [
        'LIST'        => 'list',         // 黑名单列表(ZSET)
        'INFO'        => 'info',         // 黑名单详情(HASH)
        'AUTO_REMOVE' => 'auto_remove',  // 自动移除队列(ZSET)
    ];

    // 添加黑名单缓存key（有序集合结构）
    private const CACHE_BLACKLIST_KEY = 'black_list';

    // 黑名单信息缓存key (hash结构)
    private const CACHE_BLACKLIST_INFO_KEY = 'black_list:info';

    /**
     * 获取缓存key
     * @param string $type
     * @return string
     */
    private static function getCacheKey(string $type): string
    {
        $prefix = AuthService::getUserTokenPrefix(0, false);

        return $prefix . self::CACHE_PREFIX . self::CACHE_KEY[$type];
    }

    /**
     * 获取黑名单列表key
     */
    private static function getBlackListKey(): string
    {
        return self::getCacheKey('LIST');
    }

    /**
     * 获取黑名单详情key
     */
    private static function getBlackListInfoKey(): string
    {
        return self::getCacheKey('INFO');
    }

    /**
     * 获取自动移除队列key
     */
    private static function getAutoRemoveKey(): string
    {
        return self::getCacheKey('AUTO_REMOVE');
    }

    /**
     * 更新用户心跳
     * @param int $userId
     * @throws \Psr\SimpleCache\InvalidArgumentException
     * @return bool
     */
    public static function updateHeartbeat(int $userId): bool
    {
        $fingerId = FingerService::getClientFingerId(); // 指纹Id

        // 检查用户是否被拉黑
        /*if (self::isUserBlacklisted($userId, $fingerId)) {
            self::kickOutUser($userId, $fingerId);

            return false;
        }*/

        $redis = self::getRedisHandler();

        // 增加一个用户活跃状态有序集合缓存（pms:sys_user:active_list）
        $redis->zAdd(self::getActiveListKey(), time(), $userId . ':' . $fingerId);

        // 获取活跃列表缓存生命周期时长
        $expire_time = self::getActiveListTTL();

        // 如果（pms:sys_user:active_list）的ttl为不超时或剩余60秒，那就重新设置一个过期时间（过期时间同refresh_token过期时间）
        if ($expire_time <= 60) {
            // 设置活跃状态列表过期时间（每次有用户登录后更新）
            self::setActiveListExpire();
        }

        return true;
    }

    /**
     * 统一管理活跃列表缓存key
     * @return string
     */
    public static function getActiveListKey(): string
    {
        // 用户缓存前缀（pms:sys_user:）
        $userActivePrefix = AuthService::getUserTokenPrefix(0, false);

        return $userActivePrefix . self::CACHE_ACTIVE_TAG;
    }

    /**
     * 获取活跃列表缓存生命周期时长
     * @return int
     */
    public static function getActiveListTTL(): int
    {
        $redis = self::getRedisHandler();

        return $redis->ttl(self::getActiveListKey());
    }

    /**
     * 设置用户活跃状态
     * @throws \Psr\SimpleCache\InvalidArgumentException
     */
    public static function setUserActive(int $userId): void
    {
        $loginMode = config('auth.login_mode');

        // 如果是单点登录模式，就清除掉之前的登录
        if ($loginMode === 'single') {
            // 设置用户不活跃状态（删除当前用户活跃缓存记录）
            self::setUserInactive($userId);
        }

        self::updateHeartbeat($userId);
    }

    /**
     * 设置用户不活跃状态（删除当前用户活跃缓存记录）
     * @param int $userId
     * @param string|null $fingerId
     * @return mixed
     */
    public static function setUserInactive(int $userId, ?string $fingerId = null): mixed
    {
        if (empty($fingerId)) {
            $fingerId = FingerService::getClientFingerId(); // 指纹Id
        }

        $redis = self::getRedisHandler();

        // 删除用户活跃状态有序集合缓存（pms:sys_user:active_list）
        return $redis->zrem(self::getActiveListKey(), $userId . ':' . $fingerId);
    }

    /**
     * 设置活跃状态列表过期时间（每次有用户登录后更新）
     */
    public static function setActiveListExpire(): bool
    {
        //$expire = config('auth.heartbeat_expire');

        $redis = self::getRedisHandler();

        // 获取refresh_token的过期时间
        $refresh_token_expire = TokenService::getTokenExpire('refresh_token');

        // 设置过期时间和refresh_token的过期时间一样
        $redis->expire(self::getActiveListKey(), $refresh_token_expire);

        return true;
    }

    /**
     * 获取在线用户列表(带分页)
     * @param array $params
     * @return array
     */
    public static function getOnlineUsers(array $params = []): array
    {
        $pageNo   = isset($params['page']) ? (int)$params['page'] : 1;
        $pageSize = isset($params['pageSize']) ? (int)$params['pageSize'] : 10;
        $keyword  = $params['keyword'] ?? '';

        $redis = self::getRedisHandler();

        // 计算分页参数
        $start = ($pageNo - 1) * $pageSize;
        $end   = $start + $pageSize - 1;

        // 获取在线用户ID列表
        $userIdsWithFingerIds = $redis->zRange(self::getActiveListKey(), $start, $end, true);

        // 获取用户总数
        $total = $redis->zCard(self::getActiveListKey());

        $users = [];

        foreach ($userIdsWithFingerIds as $userIdWithFingerId => $lastActiveTime) {
            list($userId, $fingerId) = explode(':', $userIdWithFingerId);

            // 检查用户是否真实在线
            if (!self::isUserActuallyOnline((int)$userId, $fingerId)) {
                // 不在线时总数-1
                $total--;
                continue;
            }

            // 获取用户信息
            $userInfo = AuthService::getUserInfoById((int)$userId);

            // 获取用户登录信息
            $loginInfo = AccountService::getUserLoginInfo((int)$userId, $fingerId);

            if ($loginInfo) {
                $loginInfo['finger_id']        = $fingerId; // 指纹Id
                $loginInfo['last_active_time'] = date('Y-m-d H:i:s', (int)$lastActiveTime); // 活跃时间

                $userLoginInfo = array_merge($userInfo, $loginInfo);

                // 关键词搜索
                if (!empty($keyword)) {
                    $match = false;

                    foreach ($userLoginInfo as $value) {
                        if (str_contains(strtolower((string)$value), strtolower($keyword))) {
                            // 无登录信息时总数-1
                            $total--;
                            $match = true;
                            break;
                        }
                    }

                    if (!$match) {
                        continue;
                    }
                }

                $users[] = AccountService::formatUserInfo($userLoginInfo);
            }
        }

        return [
            'total'    => $total,
            'page'     => $pageNo,
            'pageSize' => $pageSize,
            'list'     => array_values($users),
        ];
    }

    /**
     * 检查用户指纹Id登录信息是否存在
     * @param int $userId
     * @param string $fingerId
     * @return bool
     */
    public static function checkUserInfoFingerKeyExist(int $userId, string $fingerId): bool
    {
        $redis = self::getRedisHandler();

        return (bool)$redis->exists(AuthService::getUserInfoFingerKey($userId, $fingerId));
    }

    /**
     * 检查用户是否真实在线
     * @param int $userId
     * @param string $fingerId
     * @return bool
     */
    private static function isUserActuallyOnline(int $userId, string $fingerId): bool
    {
        $redis = self::getRedisHandler();

        // TODO::这里暂时不清理了，清理后会造成用户在线列表信息缺失（用户心跳中断后）
        // 1. 检查用户指纹Id登录信息是否存在
        $loginInfoExists = self::checkUserInfoFingerKeyExist($userId, $fingerId);

        if (!$loginInfoExists) {
            // 如果登录信息不存在，清理活跃列表中的记录
            self::setUserInactive($userId, $fingerId);

            return false;
        }

        // 2. 检查最后活跃时间
        $score = $redis->zScore(self::getActiveListKey(), $userId . ':' . $fingerId);

        if (!$score) {
            return false;
        }

        // 3. 检查是否超过心跳时间
        $heartbeatExpire = config('auth.heartbeat_expire', 300); // 默认5分钟

        if (time() - $score > $heartbeatExpire) {
            // 超过心跳时间，清理用户登录信息和活跃状态
            //self::kickOutUser($userId, $fingerId);

            // 超过心跳时间，清理活跃列表中的记录
            self::setUserInactive($userId, $fingerId);

            return false;
        }

        return true;
    }

    /**
     * 添加在线用户记录
     * @param int $userId
     * @throws \Psr\SimpleCache\InvalidArgumentException
     * @return bool
     */
    public static function addOnlineUser(int $userId): bool
    {
        // 添加用户活跃记录
        self::setUserActive($userId);

        return true;
    }

    /**
     * 踢出指定用户
     * @param int $userId
     * @param string|null $fingerId 为空时踢出所有指纹Id
     * @return bool
     */
    public static function kickOutUser(int $userId, ?string $fingerId = null): bool
    {
        $redis = self::getRedisHandler();

        try {
            if ($fingerId) {
                // 踢出指定指纹Id
                self::setUserInactive($userId, $fingerId);
                $redis->del(AuthService::getUserInfoFingerKey($userId, $fingerId));
            } else {
                // 踢出所有指纹Id
                $userKeys = $redis->keys(AuthService::getUserInfoFingerKey($userId, '*'));

                foreach ($userKeys as $key) {
                    list(, , , $currentFingerId) = explode(':', $key);
                    self::setUserInactive($userId, $currentFingerId);
                    $redis->del($key);
                }
            }

            return true;
        } catch (\Exception $e) {
            Log::error('Kick out user failed: ' . $e->getMessage());

            return false;
        }
    }

    /**
     * 定期清理离线用户
     * @param int $threshold
     * @return int
     */
    public static function cleanOfflineUsers(int $threshold = 60): int
    {
        $redis = self::getRedisHandler();

        $currentTime = time();
        $expiredTime = $currentTime - $threshold;
        $count       = $redis->zRemRangeByScore(self::getActiveListKey(), '0', (string)$expiredTime);

        return (int)$count;
    }

    /**
     * 拉黑用户
     * @param int $userId
     * @param string $fingerId
     * @param int $blockPeriod 封禁时长(秒),0表示永久
     * @return array
     */
    public static function blacklistUser(int $userId, string $fingerId, int $blockPeriod = 0): array
    {
        try {
            $redis = self::getRedisHandler();
            $now   = time();

            // 用户标识
            $userKey = $userId . ':' . $fingerId;

            // 1. 添加到黑名单有序集合,score为拉黑时间
            $redis->zAdd(self::getBlackListKey(), $now, $userKey);

            // 2. 存储黑名单详细信息到hash
            $blacklistInfo = [
                'block_time'   => $now,
                'block_period' => $blockPeriod,
                'expire_time'  => $blockPeriod > 0 ? ($now + $blockPeriod) : 0,
            ];

            $redis->hSet(
                self::getBlackListInfoKey(),
                $userKey,
                json_encode($blacklistInfo)
            );

            // 3. 如果设置了封禁时长,添加到过期队列
            if ($blockPeriod > 0) {
                // 加入过期时间有序集合
                $expireTime = $now + $blockPeriod;
                self::addToExpireQueue($userKey, $expireTime);
            }

            // 4. 踢出该用户的指定指纹Id
            self::kickOutUser($userId, $fingerId);

            return [
                'status'  => true,
                'message' => '拉黑成功',
            ];
        } catch (\Exception $e) {
            Log::error('Blacklist user failed: ' . $e->getMessage());

            return [
                'status'  => false,
                'message' => '拉黑失败: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * 解除拉黑用户
     * @param int $userId
     * @param string $fingerId
     * @return array
     */
    public static function unblacklistUser(int $userId, string $fingerId): array
    {
        try {
            $redis   = self::getRedisHandler();
            $userKey = $userId . ':' . $fingerId;

            // 从有序集合中移除
            $result = $redis->zRem(self::getBlackListKey(), $userKey);

            // 从hash中移除详情
            $redis->hDel(self::getBlackListInfoKey(), $userKey);

            if ($result) {
                return [
                    'status'  => true,
                    'message' => '移出黑名单成功',
                ];
            }

            return [
                'status'  => false,
                'message' => '移出黑名单失败',
            ];
        } catch (\Exception $e) {
            Log::error('Unblacklist user failed: ' . $e->getMessage());

            return [
                'status'  => false,
                'message' => '移出黑名单失败: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * 获取黑名单列表(带分页)
     * @param array $params
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return array
     */
    public static function getBlacklistUsers(array $params = []): array
    {
        $pageNo   = isset($params['page']) ? (int)$params['page'] : 1;
        $pageSize = isset($params['pageSize']) ? (int)$params['pageSize'] : 10;
        $keyword  = $params['keyword'] ?? '';

        $redis = self::getRedisHandler();

        // 计算分页参数
        $start = ($pageNo - 1) * $pageSize;
        $end   = $start + $pageSize - 1;

        // 获取指定范围的黑名单成员(按拉黑时间倒序)
        $blacklistItems = $redis->zRevRange(self::getBlackListKey(), $start, $end, true);

        // 获取总数
        $total = $redis->zCard(self::getBlackListKey());

        $users = [];

        foreach ($blacklistItems as $userKey => $blockTime) {
            list($userId, $fingerId) = explode(':', $userKey);

            // 获取用户基本信息
            $userInfo = AuthService::getUserInfoById((int)$userId);

            // 缓存中找不到，就从model中查询
            if (empty($userInfo)) {
                $userInfo = (new UserRepository())->getInfo($userId);
            }

            if ($userInfo) {
                // 获取黑名单详细信息
                $blacklistDetail = $redis->hGet(self::getBlackListInfoKey(), $userKey);
                $blacklistDetail = $blacklistDetail ? json_decode($blacklistDetail, true) : [];

                // 获取用户登录IP信息
                $loginIp = AccountService::getUserLoginInfo((int)$userId, $fingerId)['login_ip'] ?? '';

                if (empty($loginIp)) {
                    $loginIp = OperationLogModel::getClientIpByUserFinger((int)$userId, $fingerId);
                }

                $info = [
                    'username'     => $userInfo['username'] ?? '',
                    'realname'     => $userInfo['realname'] ?? '',
                    'login_ip'     => $loginIp,
                    'finger_id'    => $fingerId,
                    'block_time'   => date('Y-m-d H:i:s', $blacklistDetail['block_time'] ?? $blockTime),
                    'block_period' => self::formatBlockPeriod($blacklistDetail['block_period'] ?? 0),
                    'expire_time'  => $blacklistDetail['expire_time'] > 0
                        ? date('Y-m-d H:i:s', $blacklistDetail['expire_time'])
                        : '永久',
                    'user_id' => $userId,
                ];

                // 关键词搜索
                if (!empty($keyword)) {
                    $match = false;

                    foreach ($info as $value) {
                        if (str_contains(strtolower((string)$value), strtolower($keyword))) {
                            $match = true;
                            break;
                        }
                    }

                    if (!$match) {
                        continue;
                    }
                }

                $users[] = $info;
            }
        }

        return [
            'total'    => $total,
            'page'     => $pageNo,
            'pageSize' => $pageSize,
            'list'     => array_values($users),
        ];
    }

    /**
     * 加入过期队列
     */
    private static function addToExpireQueue(string $userKey, int $expireTime): void
    {
        $redis = self::getRedisHandler();
        $redis->zAdd(self::getAutoRemoveKey(), $expireTime, $userKey);
    }

    /**
     * 检查并处理过期的黑名单
     * @return int 返回处理的记录数量
     */
    public static function processExpiredBlacklist(): int
    {
        $redis = self::getRedisHandler();
        $now   = time();

        // 获取已过期的记录
        $expired = $redis->zRangeByScore(self::getAutoRemoveKey(), 0, $now);
        $count   = count($expired);

        foreach ($expired as $userKey) {
            list($userId, $fingerId) = explode(':', $userKey);
            // 解除黑名单
            self::unblacklistUser((int)$userId, $fingerId);
            // 从过期队列移除
            $redis->zRem(self::getAutoRemoveKey(), $userKey);
        }

        return $count;
    }

    /**
     * 格式化封禁时长
     */
    private static function formatBlockPeriod(int $seconds): string
    {
        if ($seconds <= 0) {
            return '永久';
        }

        if ($seconds < 3600) {
            return floor($seconds / 60) . '分钟';
        }

        if ($seconds < 86400) {
            return floor($seconds / 3600) . '小时';
        }

        return floor($seconds / 86400) . '天';
    }

    /**
     * 检查用户是否被拉黑
     * @param int $userId
     * @param string $fingerId
     * @return bool
     */
    public static function isUserBlacklisted(int $userId, string $fingerId): bool
    {
        try {
            $redis   = self::getRedisHandler();
            $userKey = $userId . ':' . $fingerId;

            // 使用zScore来检查成员是否存在于有序集合中
            $score = $redis->zScore(self::getBlackListKey(), $userKey);

            // 如果score存在,说明在黑名单中
            return $score !== false;
        } catch (\Exception $e) {
            Log::error('Check blacklist status failed: ' . $e->getMessage());

            return false;
        }
    }
}
