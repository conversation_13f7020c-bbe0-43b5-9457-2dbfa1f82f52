# SSE客户端使用指南

## 概述

本文档介绍如何在客户端使用SSE（Server-Sent Events）接收服务器推送的实时消息通知。SSE是一种基于HTTP的单向通信技术，允许服务器向客户端推送数据。

## 客户端实现

### 基本用法

以下是使用JavaScript实现SSE客户端的基本示例：

```javascript
// 创建SSE连接
const connectSSE = () => {
  // 获取认证令牌
  const token = localStorage.getItem('token');

  if (!token) {
    console.error('未找到认证令牌，无法建立SSE连接');
    return null;
  }

  // 创建EventSource对象
  const eventSource = new EventSource('/api/tenant/sse/connect', {
    withCredentials: true // 确保发送Cookie和认证信息
  });

  // 连接建立时的处理
  eventSource.onopen = (event) => {
    console.log('SSE连接已建立');
  };

  // 连接错误处理
  eventSource.onerror = (event) => {
    console.error('SSE连接错误:', event);
    eventSource.close();

    // 可以在这里实现重连逻辑
    setTimeout(() => {
      console.log('尝试重新连接SSE...');
      connectSSE();
    }, 5000); // 5秒后重连
  };

  // 注册事件处理器

  // 系统通知
  eventSource.addEventListener('system_notification', (event) => {
    const data = JSON.parse(event.data);
    console.log('收到系统通知:', data);

    // 显示通知
    showNotification(data.title, data.content, data.level);
  });

  // 设备状态变更
  eventSource.addEventListener('device_status', (event) => {
    const data = JSON.parse(event.data);
    console.log('设备状态变更:', data);

    // 更新UI或触发其他操作
    updateDeviceStatus(data.deviceId, data.status);
  });

  // 告警信息
  eventSource.addEventListener('alarm', (event) => {
    const data = JSON.parse(event.data);
    console.log('收到告警:', data);

    // 显示告警
    showAlarm(data);
  });

  return eventSource;
};

// 显示通知的函数
const showNotification = (title, content, level = 'info') => {
  // 根据UI框架实现通知显示
  // 例如使用Element UI的通知组件
  this.$notify({
    title: title,
    message: content,
    type: level,
    duration: 4500
  });
};

// 更新设备状态的函数
const updateDeviceStatus = (deviceId, status) => {
  // 实现设备状态更新逻辑
  // 例如更新状态列表或刷新设备详情
};

// 显示告警的函数
const showAlarm = (alarmData) => {
  // 实现告警显示逻辑
  // 可能需要声音提醒、弹窗等
};

// 在应用初始化时建立SSE连接
const eventSource = connectSSE();

// 在组件销毁时关闭连接
onBeforeUnmount(() => {
  if (eventSource) {
    console.log('关闭SSE连接');
    eventSource.close();
  }
});
```

### Vue组件示例

以下是一个Vue 3组件中使用SSE的完整示例：

```javascript
<template>
  <div class="notification-center">
    <div v-if="connected" class="status connected">实时通知已连接</div>
    <div v-else class="status disconnected">实时通知未连接</div>

    <div class="notification-list">
      <div v-for="(notification, index) in notifications" :key="index"
           :class="['notification-item', notification.level]">
        <h3>{{ notification.title }}</h3>
        <p>{{ notification.content }}</p>
        <span class="time">{{ formatTime(notification.timestamp) }}</span>
      </div>

      <div v-if="notifications.length === 0" class="empty-tip">
        暂无通知
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue';
import { useStore } from 'vuex';
import dayjs from 'dayjs';

const store = useStore();
const connected = ref(false);
const notifications = ref([]);
let eventSource = null;

// 格式化时间
const formatTime = (timestamp) => {
  return dayjs(timestamp * 1000).format('YYYY-MM-DD HH:mm:ss');
};

// 连接SSE
const connectSSE = () => {
  // 创建EventSource对象
  eventSource = new EventSource('/api/tenant/sse/connect', {
    withCredentials: true
  });

  // 连接建立
  eventSource.onopen = () => {
    connected.value = true;
    console.log('SSE连接已建立');
  };

  // 连接错误
  eventSource.onerror = (event) => {
    connected.value = false;
    console.error('SSE连接错误:', event);

    // 关闭连接
    if (eventSource) {
      eventSource.close();
      eventSource = null;
    }

    // 5秒后重连
    setTimeout(() => {
      console.log('尝试重新连接SSE...');
      connectSSE();
    }, 5000);
  };

  // 注册事件处理器
  eventSource.addEventListener('system_notification', handleSystemNotification);
  eventSource.addEventListener('device_status', handleDeviceStatus);
  eventSource.addEventListener('alarm', handleAlarm);
};

// 处理系统通知
const handleSystemNotification = (event) => {
  const data = JSON.parse(event.data);

  // 添加到通知列表
  notifications.value.unshift({
    title: data.title,
    content: data.content,
    level: data.level,
    timestamp: data.timestamp || Math.floor(Date.now() / 1000)
  });

  // 限制通知数量
  if (notifications.value.length > 50) {
    notifications.value = notifications.value.slice(0, 50);
  }

  // 显示通知
  store.dispatch('notification/show', {
    title: data.title,
    message: data.content,
    type: data.level
  });
};

// 处理设备状态变更
const handleDeviceStatus = (event) => {
  const data = JSON.parse(event.data);

  // 更新设备状态
  store.dispatch('device/updateStatus', {
    deviceId: data.deviceId,
    status: data.status
  });
};

// 处理告警信息
const handleAlarm = (event) => {
  const data = JSON.parse(event.data);

  // 添加到通知列表
  notifications.value.unshift({
    title: `设备告警: ${data.type}`,
    content: data.content,
    level: data.level || 'warning',
    timestamp: data.timestamp || Math.floor(Date.now() / 1000)
  });

  // 限制通知数量
  if (notifications.value.length > 50) {
    notifications.value = notifications.value.slice(0, 50);
  }

  // 显示告警通知
  store.dispatch('notification/show', {
    title: `设备告警: ${data.type}`,
    message: data.content,
    type: data.level || 'warning'
  });

  // 播放告警声音
  playAlarmSound();
};

// 播放告警声音
const playAlarmSound = () => {
  const audio = new Audio('/static/sounds/alarm.mp3');
  audio.play().catch(err => console.error('播放告警声音失败:', err));
};

// 组件挂载时连接SSE
onMounted(() => {
  connectSSE();
});

// 组件卸载前关闭SSE连接
onBeforeUnmount(() => {
  if (eventSource) {
    console.log('关闭SSE连接');
    eventSource.close();
    eventSource = null;
  }
});
</script>

<style scoped>
.notification-center {
  padding: 15px;
}

.status {
  padding: 8px;
  margin-bottom: 15px;
  border-radius: 4px;
  text-align: center;
}

.connected {
  background-color: #f0f9eb;
  color: #67c23a;
}

.disconnected {
  background-color: #fef0f0;
  color: #f56c6c;
}

.notification-list {
  max-height: 500px;
  overflow-y: auto;
}

.notification-item {
  padding: 12px;
  margin-bottom: 10px;
  border-radius: 4px;
  border-left: 4px solid #909399;
}

.notification-item.info {
  background-color: #f4f4f5;
  border-left-color: #909399;
}

.notification-item.success {
  background-color: #f0f9eb;
  border-left-color: #67c23a;
}

.notification-item.warning {
  background-color: #fdf6ec;
  border-left-color: #e6a23c;
}

.notification-item.error {
  background-color: #fef0f0;
  border-left-color: #f56c6c;
}

.notification-item h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
}

.notification-item p {
  margin: 0 0 8px 0;
  font-size: 14px;
}

.notification-item .time {
  font-size: 12px;
  color: #909399;
}

.empty-tip {
  text-align: center;
  color: #909399;
  padding: 20px;
}
</style>
```

## 路由配置

确保在前端路由中配置了SSE连接的API路径：

```javascript
// 在API请求配置中
const API = {
  // 其他API...
  SSE: {
    CONNECT: '/api/tenant/sse/connect'
  }
};
```

## 注意事项

1. **浏览器兼容性**：大多数现代浏览器都支持SSE，但IE不支持。对于不支持的浏览器，可以使用polyfill或回退到轮询方式。

2. **认证**：SSE连接需要携带认证信息，确保设置了`withCredentials: true`。

3. **连接限制**：浏览器对同一域名的连接数有限制，通常为6个，SSE会占用其中一个。

4. **重连机制**：实现合理的重连机制，避免在连接断开时频繁重连导致服务器压力过大。

5. **资源释放**：在组件卸载或页面关闭时，务必关闭SSE连接以释放资源。

## 服务端API

### 建立SSE连接

- **URL**: `/api/tenant/sse/connect`
- **方法**: GET
- **认证**: 需要用户登录认证
- **响应**: 建立SSE连接，返回事件流

### 事件类型

服务器可能发送的事件类型包括：

1. **system_notification**: 系统通知
   ```json
   {
     "title": "系统通知",
     "content": "这是一条系统通知内容",
     "level": "info",
     "timestamp": 1623456789
   }
   ```

2. **device_status**: 设备状态变更
   ```json
   {
     "deviceId": 10001,
     "status": "online",
     "timestamp": 1623456789
   }
   ```

3. **alarm**: 告警信息
   ```json
   {
     "type": "temperature_high",
     "content": "设备温度过高",
     "deviceId": 10001,
     "level": "warning",
     "timestamp": 1623456789
   }
   ```

## 调试技巧

1. 使用浏览器开发者工具的Network标签页监控SSE连接。
2. 在连接建立后，可以看到一个持续保持的请求，其Response类型为`event-stream`。
3. 可以在Response标签页中实时查看服务器发送的事件数据。

## 示例代码

完整的示例代码可以在项目的`/examples/sse-client`目录中找到。
