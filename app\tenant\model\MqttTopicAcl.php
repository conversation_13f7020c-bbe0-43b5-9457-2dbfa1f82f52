<?php
declare(strict_types = 1);

namespace app\tenant\model;

use think\facade\Log;

/**
 * MQTT主题授权模型
 * <AUTHOR> <<EMAIL>>
 * @package app\common\model
 */
class MqttTopicAcl extends BaseModel
{
    /**
     * 表名
     * @var string
     */
    protected $name = 'mqtt_topic_acl';

    /**
     * 允许写入的字段
     * @var array
     */
    protected $field = [
        'tenant_id',
        'client_id',
        'topic',
        'action',
        'permission',
        'qos',
        'retain',
        'priority',
        'status',
        'description',
    ];

    /**
     * 获取指定租户的主题授权规则
     * @param int $tenantId 租户ID
     * @param string|null $clientId 客户端ID
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return array 授权规则列表
     */
    public static function getAclRules(int $tenantId, ?string $clientId = null): array
    {
        $query = self::where([
            ['status', '=', 1],
        ])->whereOr([
            // 全局规则
            ['tenant_id', '=', 0],
            // 租户特定规则
            ['tenant_id', '=', $tenantId],
        ]);

        // 如果指定了客户端ID，则查询适用于该客户端的规则
        if ($clientId !== null) {
            $query->whereOr([
                ['client_id', '=', $clientId],
                ['client_id', '=', null],
            ]);
        }

        // 按优先级降序排序
        return $query->order('priority', 'desc')->select()->toArray();
    }

    /**
     * 检查主题权限
     * @param string $action 操作类型
     * @param string $topic 主题
     * @param int $qos QoS级别
     * @param bool $retain 是否保留消息
     * @param array $params 用于替换主题模式中的变量
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return bool 是否允许
     */
    public static function checkPermission(
        string $action,
        string $topic,
        int $qos = 0,
        bool $retain = false,
        array $params = []
    ): bool {
        $rules = self::getAclRules($params['tenantId'], $params['clientId']);

        // 记录调试信息
        Log::debug('MQTT ACL check: {action}, {topic}', [
            'action' => $action,
            'topic'  => $topic,
        ]);

        foreach ($rules as $rule) {
            // 处理规则模式中的变量
            $rulePattern = self::processRulePattern($rule['topic'], $topic, $params);

            // 如果处理失败（有未解析的变量），跳过此规则
            if ($rulePattern === null) {
                continue;
            }

            // 检查主题匹配
            if (!self::topicMatches($rulePattern, $topic)) {
                continue;
            }

            // 检查操作类型匹配
            if ($rule['action'] !== 'all' && $rule['action'] !== $action) {
                continue;
            }

            // 检查QoS限制
            if ($rule['qos'] !== -1 && $qos > $rule['qos']) {
                continue;
            }

            // 检查retain标志
            if ($retain && !$rule['retain']) {
                continue;
            }

            // 记录匹配成功的规则
            Log::debug('MQTT ACL rule matched: id={id}, permission={permission}', [
                'id'         => $rule['id'],
                'permission' => $rule['permission'],
            ]);

            // 返回权限结果
            return $rule['permission'] === 'allow';
        }

        // 默认拒绝
        return false;
    }

    /**
     * 处理规则模式中的变量
     * @param string $pattern 原始规则模式
     * @param string $topic 实际主题
     * @param array $variables 已知变量
     * @return string|null 处理后的规则模式，如果有未解析的变量则返回null
     */
    private static function processRulePattern(string $pattern, string $topic, array $variables): ?string
    {
        $rulePattern = $pattern;

        // 替换已知变量
        foreach ($variables as $key => $value) {
            $strValue    = is_scalar($value) ? (string)$value : '';
            $rulePattern = str_replace('{$' . $key . '}', $strValue, $rulePattern);
        }

        // 尝试从主题中提取其他变量
        $extractedVars = self::extractVariablesFromTopic($pattern, $topic);

        if (!empty($extractedVars)) {
            foreach ($extractedVars as $key => $value) {
                $rulePattern = str_replace('{$' . $key . '}', (string)$value, $rulePattern);
            }
        }

        // 检查是否还有未替换的变量
        if (preg_match('/{\$[a-zA-Z0-9_]+}/', $rulePattern)) {
            Log::debug('Unresolved variables in pattern: {pattern}', ['pattern' => $rulePattern]);

            return null;
        }

        return $rulePattern;
    }

    /**
     * 从主题中提取变量
     * 例如，如果模式是 "helio/test/{$phone}/haha"，主题是 "helio/test/10086/haha"
     * 则提取出 phone => 10086
     *
     * @param string $pattern 主题模式，包含变量占位符
     * @param string $topic 实际主题
     * @return array 提取的变量，格式为 [变量名 => 变量值]
     */
    private static function extractVariablesFromTopic(string $pattern, string $topic): array
    {
        $variables = [];

        // 将模式和主题按/分割
        $patternParts = explode('/', trim($pattern, '/'));
        $topicParts   = explode('/', trim($topic, '/'));

        // 如果层级数不同，则无法提取变量
        if (count($patternParts) !== count($topicParts)) {
            return $variables;
        }

        // 逐层比较，提取变量
        for ($i = 0; $i < count($patternParts); $i++) {
            // 检查是否是变量占位符
            if (preg_match('/{\$([a-zA-Z0-9_]+)}/', $patternParts[$i], $matches)) {
                $variableName  = $matches[1];
                $variableValue = $topicParts[$i];

                // 将变量添加到结果中
                $variables[$variableName] = $variableValue;
            }
        }

        return $variables;
    }

    /**
     * 检查主题是否匹配
     * 支持MQTT通配符: # 和 +
     * # 表示匹配任意多个层级
     * + 表示匹配单个层级中的任意内容
     *
     * @param string $pattern 主题模式
     * @param string $topic 实际主题
     * @return bool 是否匹配
     */
    private static function topicMatches(string $pattern, string $topic): bool
    {
        // 完全相同的情况
        if ($pattern === $topic) {
            return true;
        }

        // 特殊情况：# 匹配任何主题
        if ($pattern === '#') {
            return true;
        }

        // 分割主题和模式
        $topicParts   = explode('/', trim($topic, '/'));
        $patternParts = explode('/', trim($pattern, '/'));

        // 处理以 # 结尾的模式
        if (end($patternParts) === '#') {
            array_pop($patternParts);

            // # 前面的部分必须少于或等于主题层级
            if (count($patternParts) > count($topicParts)) {
                return false;
            }

            // 检查前面部分是否匹配
            for ($i = 0; $i < count($patternParts); $i++) {
                if ($patternParts[$i] !== '+' && $patternParts[$i] !== $topicParts[$i]) {
                    return false;
                }
            }

            return true;
        }

        // 没有 # 时，层级数必须相同
        if (count($topicParts) !== count($patternParts)) {
            return false;
        }

        // 逐层比较
        for ($i = 0; $i < count($topicParts); $i++) {
            if ($patternParts[$i] !== '+' && $patternParts[$i] !== $topicParts[$i]) {
                return false;
            }
        }

        return true;
    }
}
