{"name": "topthink/think", "description": "the new thinkphp framework", "type": "project", "keywords": ["framework", "thinkphp", "ORM"], "homepage": "https://www.thinkphp.cn/", "license": "Apache-2.0", "authors": [{"name": "liu21st", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": ">=8.2", "simps/mqtt": "^2.0", "topthink/framework": "^8.0", "topthink/think-orm": "^3.0|^4.0", "topthink/think-filesystem": "^2.0", "topthink/think-multi-app": "^1.1", "topthink/think-queue": "^3.0.11", "firebase/php-jwt": "^6.8", "ua-parser/uap-php": "^3.9", "php-mqtt/client": "^2.2", "ext-openssl": "*", "ext-redis": "*", "ext-pcntl": "*", "ext-posix": "*"}, "require-dev": {"topthink/think-trace": "^1.0", "friendsofphp/php-cs-fixer": "^3.58.1"}, "autoload": {"psr-4": {"app\\": "app"}, "psr-0": {"": "extend/"}}, "config": {"preferred-install": "dist"}, "scripts": {"php-cs-fixer": "php ./vendor/bin/php-cs-fixer fix ./ --config=./.php-cs-fixer.dist.php", "post-autoload-dump": ["@php think service:discover", "@php think vendor:publish"]}}