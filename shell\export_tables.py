import pymysql
import os
import re

def parse_env_file(env_path):
    config = {}
    with open(env_path, 'r', encoding='utf-8') as f:
        current_section = ''
        for line in f:
            line = line.strip()
            if not line or line.startswith('#'):
                continue
            
            # 处理节段
            if line.startswith('[') and line.endswith(']'):
                current_section = line[1:-1].lower()
                continue
            
            # 处理键值对
            if '=' in line:
                key, value = line.split('=', 1)
                key = key.strip()
                value = value.strip().strip('"')
                
                if current_section:
                    if current_section not in config:
                        config[current_section] = {}
                    config[current_section][key.lower()] = value
                else:
                    config[key.lower()] = value
    return config

# 获取项目根目录
root_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

# 读取.env文件
env_path = os.path.join(root_dir, '.env')
env_config = parse_env_file(env_path)

# 数据库配置
db_config = env_config.get('database', {})
config = {
    'host': db_config.get('host', 'localhost'),
    'user': db_config.get('user', 'root'),
    'password': db_config.get('pass', ''),
    'database': db_config.get('name', ''),
    'port': int(db_config.get('port', 3306)),
    'charset': db_config.get('charset', 'utf8mb4')
}

# 表前缀移除开关
remove_prefix = True  # 设置为True时移除表前缀
prefix = db_config.get('prefix', '')  # 从配置中获取表前缀

# 输出目录
output_dir = os.path.join(root_dir, 'database', 'migrations')
if not os.path.exists(output_dir):
    os.makedirs(output_dir)

# 连接数据库并导出表结构
try:
    connection = pymysql.connect(**config)
    cursor = connection.cursor()

    # 获取所有表名
    cursor.execute("SHOW TABLES")
    tables = cursor.fetchall()
    print("所有表名:", [table[0] for table in tables])

    # 遍历每个表并导出结构
    for table in tables:
        table_name = table[0]  # 从元组中获取表名
        cursor.execute(f"SHOW CREATE TABLE `{table_name}`")
        create_table_result = cursor.fetchone()
        create_table_sql = create_table_result[1]  # 获取建表SQL语句
        
        # 移除AUTO_INCREMENT=数字的信息
        create_table_sql = re.sub(r'AUTO_INCREMENT=\d+\s*', '', create_table_sql)
        
        # 处理表名和建表语句中的前缀
        output_table_name = table_name
        if remove_prefix and prefix and table_name.startswith(prefix):
            # 移除表名中的前缀
            output_table_name = table_name[len(prefix):]
            # 在建表语句中替换表名
            create_table_sql = create_table_sql.replace(f'CREATE TABLE `{table_name}`', f'CREATE TABLE `{output_table_name}`')
        
        # 写入文件（每个表一个.sql文件）
        file_path = os.path.join(output_dir, f"{output_table_name}.sql")
        with open(file_path, "w", encoding="utf-8") as f:
            f.write(f"-- 表结构: {output_table_name}\n")
            f.write(create_table_sql + ";\n")
        print(f"导出成功: {file_path}")

except Exception as e:
    print(f"错误: {e}")
finally:
    if 'cursor' in locals():
        cursor.close()
    if 'connection' in locals():
        connection.close()