-- 表结构: notification_queue
CREATE TABLE `notification_queue` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `tenant_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '租户ID',
  `notification_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '消息ID',
  `user_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '接收用户ID',
  `is_read` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否已读',
  `interact_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '互动状态: 0=未操作 1=点赞 -1=踩',
  `is_deleted` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否已删除',
  `read_at` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '已读时间',
  `interact_at` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '互动时间',
  `delete_at` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '删除时间',
  `create_at` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_at` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_read` (`user_id`,`is_read`),
  KEY `idx_user_deleted` (`user_id`,`is_deleted`),
  KEY `idx_tenant_id` (`tenant_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='消息队列表(采用读扩散策略)';
