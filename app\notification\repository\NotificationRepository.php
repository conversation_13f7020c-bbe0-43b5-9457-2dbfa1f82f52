<?php
namespace app\notification\repository;

use app\notification\model\Notification as NotificationModel;
use app\notification\model\NotificationQueue as NotificationQueueModel;
use app\notification\service\NotificationCounterService;
use app\system\model\Department as DepartmentModel;
use app\system\model\DepartmentUser as DepartmentUserModel;
use app\system\model\User as UserModel;
use think\facade\Log;
use think\facade\Queue;

/**
 * 消息通知数据仓库类
 * 负责系统通知相关的数据访问和操作
 * <AUTHOR> <<EMAIL>>
 * @package app\notification\repository
 */
class NotificationRepository extends BaseRepository
{
    /**
     * 缓存前缀
     */
    public const CACHE_PREFIX = 'notification:';

    /**
     * 缓存时间（秒）
     */
    public const CACHE_TTL = 3600;

    /**
     * 是否启用缓存
     */
    protected bool $enableCache = true;

    /**
     * 队列名称
     * @var string
     */
    protected string $queueName = 'notification';

    /**
     * 队列分发器
     * @var string
     */
    protected string $queueDispatcher = 'app\\notification\\job\\NotificationDispatch';

    /**
     * 设置缓存开关
     * @param bool $enable
     * @return self
     */
    public function setEnableCache(bool $enable = true): self
    {
        $this->enableCache = $enable;

        return $this;
    }

    /**
     * 构造方法
     */
    public function __construct()
    {
        // 指定当前模型类名而非实例，避免条件累积问题
        parent::__construct(NotificationModel::class);
    }

    /**
     * 获取未读消息数
     * @param int $userId
     * @return int
     */
    public function getUnreadCount(int $userId): int
    {
        return NotificationQueueModel::where([
            'user_id'    => $userId,
            'is_read'    => 0,
            'is_deleted' => 0,
        ])->count();
    }

    /**
     * 获取通知详情（带缓存）
     * @param int $id
     * @throws \Exception
     * @return array
     */
    public function getNotificationInfo(int $id): array
    {
        $cacheKey = self::CACHE_PREFIX . $id;

        if (!$this->enableCache) {
            return $this->getDetailWithCounter($id);
        }

        return $this->remember($cacheKey, function() use ($id) {
            return $this->getDetailWithCounter($id);
        }, self::CACHE_TTL);
    }

    /**
     * 获取消息详情（带计数）
     * @param int $id
     * @throws \Exception
     * @return array
     */
    public function getDetailWithCounter(int $id): array
    {
        try {
            $detail = $this->model->alias('notification')
                ->field('notification.*, u.realname as sender_name')
                ->join('system_user u', 'notification.sender_id = u.id', 'LEFT')
                ->where('notification.id', $id)
                ->find();

            if (!$detail) {
                throw new \Exception('消息不存在');
            }

            $data = $detail->toArray();
            // 添加接收范围描述
            $data['receive_range_text'] = $this->getReceiveRangeText($data['receive_range'], $data['target_ids']);

            // 获取实时计数
            $counterService = new NotificationCounterService();

            $counts = $counterService->getRealTimeCount($id);

            return array_merge($data, $counts);
        } catch (\Exception $e) {
            Log::error('获取消息详情失败: ' . $e->getMessage());

            throw $e;
        }
    }

    /**
     * 发送消息（采用读扩散策略）
     * @param mixed $data
     * @param int $userId
     * @return array
     */
    public function send(array $data, int $userId): array
    {
        $this->model->startTrans();

        try {
            // 数据验证和预处理
            if (empty($data['title'])) {
                throw new \Exception('标题不能为空');
            }

            if (empty($data['content'])) {
                throw new \Exception('内容不能为空');
            }

            if (!isset($data['type'])) {
                throw new \Exception('消息类型不能为空');
            }

            if (!isset($data['receive_range'])) {
                throw new \Exception('接收范围不能为空');
            }
            // 确保 target_ids 是数组并正确处理
            $targetIds = isset($data['target_ids']) ? (is_array($data['target_ids'])
                ? $data['target_ids']
                : json_decode($data['target_ids'], true)) : [];
            $targetIds ??= [];
            // 创建消息
            $notification = [
                'title'         => trim($data['title']),
                'content'       => trim($data['content']),
                'type'          => intval($data['type']),
                'receive_range' => intval($data['receive_range']),
                'target_ids'    => json_encode($targetIds, JSON_UNESCAPED_UNICODE),
                'sender_id'     => $userId,
                'tenant_id'     => isset($data['tenant_id']) ? intval($data['tenant_id']) : 0,
                'create_at'     => time(),
                'update_at'     => time(),
            ];
            $result = $this->model->save($notification);

            if (!$result) {
                throw new \Exception('消息保存失败');
            }

            // 确保 insertId 为整型
            $insertId = is_numeric($this->model->id) ? intval($this->model->id) : 0;

            if ($insertId === 0) {
                throw new \Exception('消息ID获取失败');
            }

            // 初始化计数器
            try {
                (new NotificationCounterService())->init($insertId);
            } catch (\Exception $e) {
                throw new \Exception('计数器初始化失败: ' . $e->getMessage());
            }
            $queueData = [
                'notification_id' => $insertId,
                'type'            => intval($data['receive_range']),
                'target_ids'      => $targetIds,
            ];
            // 推送到队列任务
            $queueResult = Queue::later(1, $this->queueDispatcher, $queueData, $this->queueName);

            if ($queueResult === false) {
                throw new \Exception('消息队列推送失败');
            }
            $this->model->commit();

            return [
                'status' => 1,
                'data'   => ['id' => $insertId],
            ];
        } catch (\Exception $e) {
            $this->model->rollback();
            Log::error('发送消息异常: ' . $e->getMessage() . "\n数据: " . json_encode($data, JSON_UNESCAPED_UNICODE));

            return [
                'status'  => 0,
                'message' => $e->getMessage(),
            ];
        }
    }

    /**
     * 获取用户接收的消息列表
     * @param int $userId
     * @param array $params
     * @return array
     */
    public function getReceivedList(int $userId, array $params = []): array
    {
        $page       = isset($params['page']) ? intval($params['page']) : 1;
        $pageSize   = isset($params['pageSize']) ? intval($params['pageSize']) : 20;
        $type       = isset($params['type']) ? intval($params['type']) : 0;
        $readStatus = isset($params['readStatus']) ? intval($params['readStatus']) : null;
        $keyword    = trim($params['keyword'] ?? '');
        $tenantId   = isset($params['tenant_id']) ? intval($params['tenant_id']) : null;

        // 构建基础查询
        $query = $this->model->alias('n')
            ->join('notification_queue q', 'n.id = q.notification_id')
            ->join('notification_counter c', 'n.id = c.notification_id', 'LEFT')
            ->join('system_user u', 'n.sender_id = u.id', 'LEFT')
            ->where('q.user_id', $userId)
            ->where('q.is_deleted', 0);

        // 添加租户筛选
        if (empty($tenantId)) {
            // 查询指定租户的消息或系统级消息(tenant_id=0)
            $query->where(function($query) use ($tenantId) {
                $query->whereOr([
                    ['n.tenant_id', '=', $tenantId],
                    ['n.tenant_id', '=', 0],
                ]);
            });
        }

        // 添加阅读状态筛选
        if ($readStatus !== null) {
            $query->where('q.is_read', $readStatus);
        }

        // 添加类型筛选
        if ($type > 0) {
            $query->where('n.type', $type);
        }

        // 添加关键词搜索
        if ($keyword !== '') {
            $query->where(function($query) use ($keyword) {
                $query->whereOr([
                    ['n.title', 'like', "%{$keyword}%"],
                    ['n.content', 'like', "%{$keyword}%"],
                ]);
            });
        }

        // 获取总数
        $total = $query->count();

        // 获取列表数据
        $list = $query
            ->field('n.*, q.is_read, q.interact_status, c.read_count, c.like_count, c.dislike_count, u.realname as sender_name')
            ->page($page, $pageSize)
            ->order('q.is_read', 'asc')
            ->order('n.id', 'desc')
            ->select();

        $data = [
            'total'    => $total,
            'page'     => $page,
            'pageSize' => $pageSize,
            'list'     => [],
        ];

        if (!$list->isEmpty()) {
            $listData = $list->toArray();

            // 处理接收范围的显示
            foreach ($listData as &$item) {
                // 添加接收范围描述
                $item['receive_range_text'] = $this->getReceiveRangeText($item['receive_range'], $item['target_ids']);
            }
            $data['list'] = $listData;
        }

        return $data;
    }

    /**
     * 获取用户发送的消息列表
     * @param int $userId
     * @param array $params
     * @throws \Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return array
     */
    public function getSentList(int $userId, array $params = []): array
    {
        $page         = (int)($params['page'] ?? 1);
        $pageSize     = (int)($params['pageSize'] ?? 20);
        $type         = (int)($params['type'] ?? 0);
        $keyword      = trim($params['keyword'] ?? '');
        $receiveRange = (int)($params['receive_range'] ?? 0);
        $tenantId     = isset($params['tenant_id']) ? intval($params['tenant_id']) : null;

        // 构建查询条件
        $query = $this->model->where('sender_id', $userId);

        // 添加租户筛选
        if (empty($tenantId)) {
            $query->where('tenant_id', $tenantId);
        }

        // 添加类型筛选
        if ($type > 0) {
            $query->where('type', $type);
        }

        // 添加接收范围筛选
        if ($receiveRange > 0) {
            $query->where('receive_range', $receiveRange);
        }

        // 添加关键词搜索
        if ($keyword !== '') {
            $query->where(function($query) use ($keyword) {
                $query->whereOr([
                    ['title', 'like', "%{$keyword}%"],
                    ['content', 'like', "%{$keyword}%"],
                ]);
            });
        }
        // 计算总数
        $total = $query->count();
        // 获取列表数据
        $list = $query->alias('n')
            ->field('n.*, nc.read_count, nc.like_count, nc.dislike_count')
            ->join('notification_counter nc', 'n.id = nc.notification_id', 'LEFT')
            ->page($page, $pageSize)
            ->order('n.id', 'desc')
            ->select();
        $data = [
            'total'    => $total,
            'page'     => $page,
            'pageSize' => $pageSize,
            'list'     => [],
        ];

        if (!$list->isEmpty()) {
            $listData       = $list->toArray();
            $counterService = new NotificationCounterService();

            foreach ($listData as &$item) {
                // 添加接收范围描述
                $item['receive_range_text'] = $this->getReceiveRangeText($item['receive_range'], $item['target_ids']);
                // 获取实时计数
                $counts = $counterService->getRealTimeCount($item['id']);
                $item   = array_merge($item, $counts);
            }
            $data['list'] = $listData;
        }

        return $data;
    }

    /**
     * 标记已读（批量处理）
     * @param mixed $notificationId
     * @param mixed $userId
     */
    public function markRead(int $notificationId, int $userId): void
    {
        // 1. 更新阅读状态
        NotificationQueueModel::where([
            'notification_id' => $notificationId,
            'user_id'         => $userId,
        ])->update(['is_read' => 1, 'read_at' => time()]);

        // 2. 更新计数
        (new NotificationCounterService())->incrReadCount($notificationId);
    }

    /**
     * 标记未读
     * @param int $notificationId
     * @param int $userId
     * @throws \Exception
     */
    public function markUnread(int $notificationId, int $userId): void
    {
        $this->model->startTrans();

        try {
            // 1. 检查消息是否存在且已读
            $queue = NotificationQueueModel::where([
                'notification_id' => $notificationId,
                'user_id'         => $userId,
                'is_deleted'      => 0,
            ])->find();

            if (!$queue) {
                throw new \Exception('消息不存在或已删除');
            }

            if ($queue->is_read == 0) {
                throw new \Exception('消息已经是未读状态');
            }

            // 2. 更新阅读状态
            $result = NotificationQueueModel::where([
                'notification_id' => $notificationId,
                'user_id'         => $userId,
            ])->update([
                'is_read'   => 0,
                'read_at'   => 0,
                'update_at' => time(),
            ]);

            // 3. 更新计数（减少已读数）
            (new NotificationCounterService())->decrReadCount($notificationId);

            $this->model->commit();
        } catch (\Exception $e) {
            $this->model->rollback();

            throw $e;
        }
    }

    /**
     * 批量标记已读
     * @param array $notificationIds
     * @param int $userId
     * @return array
     */
    public function batchMarkRead(array $notificationIds, int $userId): array
    {
        try {
            $this->model->startTrans();

            // 1. 更新阅读状态
            $queue  = new NotificationQueueModel();
            $result = $queue->batchUpdateReadStatus($notificationIds, $userId, 1);

            if (!$result) {
                throw new \Exception('更新阅读状态失败');
            }

            // 2. 更新计数
            $counterService = new NotificationCounterService();

            foreach ($notificationIds as $notificationId) {
                $counterService->incrReadCount($notificationId);
            }

            $this->model->commit();

            return ['status' => true, 'message' => '标记成功'];
        } catch (\Exception $e) {
            $this->model->rollback();
            Log::error('批量标记已读失败: ' . $e->getMessage());

            return ['status' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * 批量标记未读
     * @param array $notificationIds
     * @param int $userId
     * @return array
     */
    public function batchMarkUnread(array $notificationIds, int $userId): array
    {
        try {
            $this->model->startTrans();

            // 1. 更新阅读状态
            $queue  = new NotificationQueueModel();
            $result = $queue->batchUpdateReadStatus($notificationIds, $userId, 0);

            if (!$result) {
                throw new \Exception('更新阅读状态失败');
            }

            // 2. 更新计数
            $counterService = new NotificationCounterService();

            foreach ($notificationIds as $notificationId) {
                $counterService->decrReadCount($notificationId);
            }

            $this->model->commit();

            return ['status' => true, 'message' => '标记成功'];
        } catch (\Exception $e) {
            $this->model->rollback();
            Log::error('批量标记未读失败: ' . $e->getMessage());

            return ['status' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * 标记全部已读
     * @param int $userId
     * @return array
     */
    public function markAllRead(int $userId): array
    {
        try {
            $this->model->startTrans();

            // 1. 获取用户所有未读且未删除的消息ID
            $unreadIds = NotificationQueueModel::where([
                'user_id'    => $userId,
                'is_read'    => 0,
                'is_deleted' => 0,
            ])->column('notification_id');

            if (empty($unreadIds)) {
                return ['status' => true, 'message' => '没有未读消息'];
            }

            // 2. 更新阅读状态
            $queue = new NotificationQueueModel();
            $queue->where([
                ['notification_id', 'in', $unreadIds],
                ['user_id', '=', $userId],
                ['is_deleted', '=', 0],
            ])->update([
                'is_read'   => 1,
                'read_at'   => time(),
                'update_at' => time(),
            ]);

            // 3. 更新计数
            $counterService = new NotificationCounterService();

            foreach ($unreadIds as $notificationId) {
                $counterService->incrReadCount($notificationId);
            }
            $this->model->commit();

            return ['status' => true, 'message' => '标记成功'];
        } catch (\Exception $e) {
            $this->model->rollback();
            Log::error('标记全部已读失败: ' . $e->getMessage());

            return ['status' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * 互动（点赞/踩/取消）
     * @param int $notificationId
     * @param int $userId
     * @param int $type 1=点赞 -1=踩 0=取消
     * @return array
     */
    public function interact(int $notificationId, int $userId, int $type): array
    {
        try {
            $this->model->startTrans();
            // 1. 获取原互动状态
            $queue     = new NotificationQueueModel();
            $oldStatus = $queue->getInteractStatus($notificationId, $userId);

            // 如果是相同的操作，则改为取消操作
            if ($oldStatus === $type && $type !== 0) {
                $type = 0;
            }

            // 2. 更新数据库状态
            $result = $queue->updateInteractStatus($notificationId, $userId, $type);

            if (!$result) {
                throw new \Exception('更新互动状态失败');
            }

            // 3. 更新计数服务
            $counterService = new NotificationCounterService();

            // 如果之前有互动，先减少原有计数
            if ($oldStatus !== 0) {
                $counterService->decrInteractCount($notificationId, $oldStatus);
            }

            // 如果不是取消操作，则增加新的计数
            if ($type !== 0) {
                $counterService->incrInteractCount($notificationId, $type);
            }

            $this->model->commit();
            // 4. 返回最新的计数
            $counts = $counterService->getRealTimeCount($notificationId);

            return [
                'status'  => 1,
                'message' => '操作成功',
                'data'    => [
                    'action'          => $type === 0 ? 'cancel' : ($type === 1 ? 'like' : 'dislike'),
                    'interact_status' => $type,
                    'like_count'      => $counts['like_count'],
                    'dislike_count'   => $counts['dislike_count'],
                ],
            ];
        } catch (\Exception $e) {
            $this->model->rollback();
            Log::error('消息互动失败: ' . $e->getMessage());

            return ['status' => 0, 'message' => $e->getMessage()];
        }
    }

    /**
     * 获取用户对特定消息的互动状态
     * @param int $notificationId
     * @param int $userId
     * @return array
     */
    public function getUserInterAction(int $notificationId, int $userId): array
    {
        try {
            // 从缓存中获取互动状态
            $counterService = new NotificationCounterService();
            $cachedStatus   = $counterService->getCachedInteractionStatus($notificationId, $userId);

            if ($cachedStatus !== null) {
                return [
                    'status' => true,
                    'data'   => $cachedStatus,
                ];
            }

            // 缓存不存在则从数据库查询
            $status = (new NotificationQueueModel())->getInteractStatus($notificationId, $userId);
            // 将结果写入缓存
            $counterService->cacheInteractionStatus($notificationId, $userId, $status);

            return [
                'status' => true,
                'data'   => $status,
            ];
        } catch (\Exception $e) {
            Log::error('获取互动状态异常: ' . $e->getMessage());

            return [
                'status'  => false,
                'message' => $e->getMessage(),
            ];
        }
    }

    /**
     * 处理消息分发
     */
    public function dispatchNotification(array $data): void
    {
        $notificationId = (int)$data['notification_id'];
        $type           = (int)$data['type'];
        $targetIds      = is_array($data['target_ids'])
            ? $data['target_ids']
            : (json_decode($data['target_ids'], true) ?? []);
        $tenantId = isset($data['tenant_id']) ? (int)$data['tenant_id'] : null;

        // 如果没有提供租户ID，则从消息记录中获取
        if (empty($tenantId)) {
            $tenantId = $this->model->where('id', $notificationId)->value('tenant_id');
        }

        // 获取接收者ID列表
        $receiverIds = $this->getReceiverIds($type, $targetIds, $tenantId);

        if (!empty($receiverIds)) {
            // 批量创建消息队列记录
            $this->batchCreateQueueRecords($notificationId, $receiverIds);
        }
    }

    /**
     * 获取接收者ID列表
     * @param int $type 接收类型
     * @param array $targetIds 目标ID列表
     * @param int|null $tenantId 租户ID，如果提供则只获取该租户下的用户
     * @return array
     */
    private function getReceiverIds(int $type, array $targetIds, ?int $tenantId = null): array
    {
        $receiverIds = [];
        $userQuery   = UserModel::where('status', 1);

        // 如果指定了租户ID，则只获取该租户下的用户
        if ($tenantId !== null && $tenantId > 0) {
            $userQuery->where('tenant_id', $tenantId);
        }

        switch ($type) {
            case NotificationModel::RECEIVE_ALL:
                $receiverIds = $userQuery->column('id');
                break;
            case NotificationModel::RECEIVE_DEPARTMENT:
                if (!empty($targetIds)) {
                    $deptUserQuery = DepartmentUserModel::whereIn('dept_id', $targetIds);
                    $userIds       = $deptUserQuery->column('user_id');

                    if (!empty($userIds)) {
                        // 如果指定了租户ID，则需要进一步筛选用户
                        if ($tenantId !== null && $tenantId > 0) {
                            $receiverIds = $userQuery->whereIn('id', $userIds)->column('id');
                        } else {
                            $receiverIds = $userIds;
                        }
                    }
                }
                break;
            case NotificationModel::RECEIVE_USER:
                if (!empty($targetIds)) {
                    $receiverIds = $userQuery->whereIn('id', $targetIds)->column('id');
                }
                break;
        }

        return array_unique($receiverIds);
    }

    /**
     * 批量创建消息队列记录
     */
    private function batchCreateQueueRecords(int $notificationId, array $receiverIds): void
    {
        $now  = time();
        $data = [];

        // 获取消息的租户ID
        $tenantId = $this->model->where('id', $notificationId)->value('tenant_id') ?? 0;

        foreach ($receiverIds as $userId) {
            $data[] = [
                'notification_id' => $notificationId,
                'user_id'         => $userId,
                'tenant_id'       => $tenantId,
                'is_read'         => 0,
                'is_deleted'      => 0,
                'create_at'       => $now,
                'update_at'       => $now,
            ];
        }

        if (!empty($data)) {
            NotificationQueueModel::insertAll($data);
        }
    }

    /**
     * 软删除单条消息
     * @param int $notificationId
     * @param int $userId
     * @return bool
     */
    public function softDelete(int $notificationId, int $userId): bool
    {
        return $this->batchSoftDelete([$notificationId], $userId);
    }

    /**
     * 批量软删除用户的消息
     * @param array $notificationIds 消息ID数组
     * @param int $userId 用户ID
     * @return bool
     */
    public function batchSoftDelete(array $notificationIds, int $userId): bool
    {
        try {
            $result = NotificationQueueModel::where([
                ['notification_id', 'in', $notificationIds],
                ['user_id', '=', $userId],
            ])->update([
                'is_deleted' => 1,
                'delete_at'  => time(),
                'update_at'  => time(),
            ]);

            return (bool)$result;
        } catch (\Exception $e) {
            Log::error('批量删除消息失败: ' . $e->getMessage());

            return false;
        }
    }

    /**
     * 更改消息阅读状态（已读/未读）
     * @param int $notificationId
     * @param int $userId
     * @param string $action
     * @return array
     */
    public function changeReadStatus(int $notificationId, int $userId, string $action): array
    {
        try {
            if ($action === 'read') {
                $this->markRead($notificationId, $userId);

                return ['status' => true, 'message' => '标记已读成功'];
            }
            $this->markUnread($notificationId, $userId);

            return ['status' => true, 'message' => '标记未读成功'];
        } catch (\Exception $e) {
            Log::error('更改消息阅读状态失败: ' . $e->getMessage());

            return ['status' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * 获取接收范围的文字描述
     */
    private function getReceiveRangeText(int $receiveRange, string|array $targetIds): string
    {
        // 确保 targetIds 是数组
        if (is_string($targetIds)) {
            $targetIds = json_decode($targetIds, true) ?: [];
        }

        // 将多维数组扁平化处理
        if (!empty($targetIds)) {
            $flattenIds = [];
            array_walk_recursive($targetIds, function($id) use (&$flattenIds) {
                $flattenIds[] = $id;
            });
            $targetIds = array_unique($flattenIds);
        }

        switch ($receiveRange) {
            case NotificationModel::RECEIVE_ALL:
                return '全体人员';
            case NotificationModel::RECEIVE_DEPARTMENT:
                if (empty($targetIds)) {
                    return '未选择部门';
                }

                // 获取部门名称
                $depts = DepartmentModel::whereIn('id', $targetIds)->column('name');

                return implode('、', $depts);
            case NotificationModel::RECEIVE_USER:
                if (empty($targetIds)) {
                    return '未选择接收人';
                }

                // 获取用户真实姓名
                $users = UserModel::whereIn('id', $targetIds)->column('realname');

                return implode('、', $users);
            default:
                return '未知';
        }
    }
}
