<?php
declare(strict_types = 1);
namespace app\tenant\controller;

use app\system\controller\BaseController;
use app\tenant\repository\PermissionRepository;
use app\tenant\validate\Permission as PermissionValidate;
use think\App;
use think\exception\ValidateException;
use think\Response;

/**
 * 权限节点控制器（用于总后台）
 * <AUTHOR> <<EMAIL>>
 * @package app\tenant\controller
 */
class Menu extends BaseController
{
    protected PermissionRepository $repository;

    public function __construct(App $app)
    {
        parent::__construct($app);

        // 指定对应仓储类
        $this->repository = new PermissionRepository();
    }

    /**
     * 公共权限节点树接口
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return \think\Response
     */
    public function tree(): Response
    {
        $tree = $this->repository->getPermissionTree();

        return $this->success($tree);
    }

    /**
     * 权限节点列表（不分页，一次展示出来）
     * @throws \Throwable
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return \think\Response
     */
    public function list(): Response
    {
        $params = $this->request->param();
        $list   = $this->repository->getPermissionList($params, true);

        return $this->success($list);
    }

    /**
     * 获取权限节点信息
     * @return \think\Response
     */
    public function info(): Response
    {
        $params = $this->request->param();

        if (empty($params['id'])) {
            return $this->error('参数ID不能为空');
        }

        $info = $this->repository->getPermissionInfo(intval($params['id']));

        if (empty($info)) {
            return $this->error('无对应数据！');
        }

        return $this->success($info);
    }

    /**
     * 添加节点
     * @return \think\Response
     */
    public function add(): Response
    {
        $params = $this->request->param();

        try {
            validate(PermissionValidate::class)->scene('add')->check($params);
        } catch (ValidateException $e) {
            // 验证失败 输出错误信息
            return $this->error($e->getError());
        }

        $data = [
            'pid'       => isset($params['pid']) ? intval($params['pid']) : 0,
            'path'      => isset($params['path']) ? trim($params['path']) : '',
            'name'      => isset($params['name']) ? trim($params['name']) : '',
            'title'     => isset($params['title']) ? trim($params['title']) : '',
            'icon'      => isset($params['icon']) ? trim($params['icon']) : '',
            'type'      => isset($params['type']) ? trim($params['type']) : 'menu',
            'component' => isset($params['component']) ? trim($params['component']) : '',
            'redirect'  => isset($params['redirect']) ? trim($params['redirect']) : '',
            'active'    => isset($params['active']) ? trim($params['active']) : '',
            'tag'       => isset($params['tag']) ? trim($params['tag']) : '',
            'is_show'   => isset($params['is_show']) ? intval($params['is_show']) : 0,
            'fullpage'  => isset($params['fullpage']) ? intval($params['fullpage']) : 0,
            'sort'      => isset($params['sort']) ? intval($params['sort']) : 0,
            'create_at' => time(),
            'status'    => 1,
        ];

        // 如果路由的第一个字符不是/，主动加上
        if ($data['path'] && mb_substr($data['path'], 0, 1) != '/') {
            $data['path'] = '/' . $data['path'];
        }

        // 如果视图的第一个字符是/，主动取消
        if ($data['component'] && mb_substr($data['component'], 0, 1) === '/') {
            $data['component'] = substr_replace($data['component'], '', 0, 1);
        }

        try {
            $last_id = $this->repository->addPermission($data);

            if ($last_id) {
                // 写入事件日志
                $this->event_log('添加角色节点', $params, []);
                // 返回给客户端主键ID
                $data['id'] = $last_id;

                return $this->success($data);
            }

            return $this->error('添加失败');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 编辑节点
     * @return \think\Response
     */
    public function edit(): Response
    {
        $params = $this->request->param();

        try {
            validate(PermissionValidate::class)->scene('edit')->check($params);
        } catch (ValidateException $e) {
            // 验证失败 输出错误信息
            return $this->error($e->getError());
        }

        $data = [
            'pid'       => isset($params['pid']) ? intval($params['pid']) : 0,
            'path'      => isset($params['path']) ? trim($params['path']) : '',
            'name'      => isset($params['name']) ? trim($params['name']) : '',
            'title'     => isset($params['title']) ? trim($params['title']) : '',
            'icon'      => isset($params['icon']) ? trim($params['icon']) : '',
            'type'      => isset($params['type']) ? trim($params['type']) : 'menu',
            'component' => isset($params['component']) ? trim($params['component']) : '',
            'redirect'  => isset($params['redirect']) ? trim($params['redirect']) : '',
            'active'    => isset($params['active']) ? trim($params['active']) : '',
            'tag'       => isset($params['tag']) ? trim($params['tag']) : '',
            'is_show'   => isset($params['is_show']) ? intval($params['is_show']) : 0,
            'fullpage'  => isset($params['fullpage']) ? intval($params['fullpage']) : 0,
            'sort'      => isset($params['sort']) ? intval($params['sort']) : 0,
            'update_at' => time(),
            'status'    => isset($params['status']) ? intval($params['status']) : 1,
        ];

        // 如果路由的第一个字符不是/，主动加上
        if ($data['path'] && mb_substr($data['path'], 0, 1) != '/') {
            $data['path'] = '/' . $data['path'];
        }

        // 如果视图的第一个字符是/，主动取消
        if ($data['component'] && mb_substr($data['component'], 0, 1) === '/') {
            $data['component'] = substr_replace($data['component'], '', 0, 1);
        }

        // 接口权限列表
        $apiList = $params['apiList'] ?? [];

        try {
            $result = $this->repository->editPermission($params['id'], $data, $apiList);

            if ($result) {
                // 写入事件日志
                $this->event_log('更新角色节点', $data, []);

                return $this->success('更新成功');
            }

            return $this->error('更新失败');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 角色排序
     * @return \think\Response
     */
    public function sort(): Response
    {
        $params = $this->request->param();

        try {
            validate(PermissionValidate::class)->scene('sort')->check($params);
        } catch (ValidateException $e) {
            // 验证失败 输出错误信息
            return $this->error($e->getError());
        }

        $result = $this->repository->updateSort((int)$params['id'], (int)$params['sort']);

        if ($result) {
            // 写入事件日志
            $this->event_log('更新节点排序', $params, []);

            return $this->success('更新成功');
        }

        return $this->error('更新失败');
    }

    /**
     * 设置节点状态（禁用启用）
     * @return \think\Response
     */
    public function status(): Response
    {
        $params = $this->request->param();

        try {
            validate(PermissionValidate::class)->scene('status')->check($params);
        } catch (ValidateException $e) {
            // 验证失败 输出错误信息
            return $this->error($e->getError());
        }

        $id_arr = value2Array($params['id']);
        $status = intval($params['status']);

        try {
            if ($status === 1) {
                $this->repository->enable($id_arr);
            } else {
                $this->repository->disable($id_arr);
            }

            // 写入事件日志
            $this->event_log('设置角色节点状态', $params, []);

            return $this->success($status == 0 ? '禁用成功' : '启用成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 删除节点（软删除）
     * @return \think\Response
     */
    public function delete(): Response
    {
        $params = $this->request->param();

        if (empty($params['id'])) {
            return $this->error('节点ID不能为空');
        }

        $id_arr = value2Array($params['id']);

        try {
            $this->repository->recycle($id_arr);

            // 写入事件日志
            $this->event_log('删除角色节点', $params, []);

            return $this->success('删除成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}
