-- 表结构: system_department
CREATE TABLE `system_department` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `pid` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '上级部门ID',
  `name` varchar(50) NOT NULL DEFAULT '' COMMENT '部门名称',
  `create_at` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_at` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  `description` varchar(255) NOT NULL DEFAULT '' COMMENT '备注信息',
  `is_system` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否系统',
  `sort` smallint(4) NOT NULL DEFAULT '0' COMMENT '排序号',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '部门状态 1为有效 0为失效',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='系统部门表';
