<?php
declare(strict_types = 1);

namespace app\tenant\service;

use think\facade\Cache;
use think\facade\Config;

/**
 * 加密服务类
 * 提供加密、解密、签名等功能
 * <AUTHOR> <<EMAIL>>
 * @package app\tenant\service
 */
class EncryptionService
{
    /**
     * @var ?EncryptionService
     */
    private static ?EncryptionService $instance;

    /**
     * 获取单例实例
     * @return EncryptionService
     */
    public static function getInstance(): self
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }

        return self::$instance;
    }

    /**
     * 生成随机AES密钥
     * @throws \Random\RandomException
     * @return string
     */
    public function generateAesKey(): string
    {
        $keySize = Config::get('encryption.aes_key_size', 128) / 8;

        return bin2hex(random_bytes($keySize));
    }

    /**
     * 生成随机nonce
     * @throws \Random\RandomException
     * @return string
     */
    public function generateNonce(): string
    {
        $length = Config::get('encryption.nonce_length', 32);

        return bin2hex(random_bytes($length / 2));
    }

    /**
     * 验证时间戳是否在有效期内
     * @param int $timestamp
     * @return bool
     */
    public function validateTimestamp(int $timestamp): bool
    {
        $timeout = Config::get('encryption.request_timeout', 300000);
        $now     = time() * 1000;

        return abs($now - $timestamp) <= $timeout;
    }

    /**
     * 验证nonce是否有效
     * @param string $nonce
     * @return bool
     */
    public function validateNonce(string $nonce): bool
    {
        $key = 'nonce:' . $nonce;

        if (Cache::has($key)) {
            return false; // nonce已使用
        }

        $ttl = Config::get('encryption.nonce_ttl', 300);
        Cache::set($key, 1, $ttl);

        return true;
    }

    /**
     * RSA公钥加密
     * @param string $data
     * @return string
     */
    public function rsaEncrypt(string $data): string
    {
        $publicKey = Config::get('encryption.rsa_public_key');

        if (empty($publicKey)) {
            throw new \RuntimeException('RSA公钥未配置');
        }

        // 判断是否为文件路径
        if (is_file($publicKey)) {
            $publicKey = file_get_contents($publicKey);

            if ($publicKey === false) {
                throw new \RuntimeException('无法读取RSA公钥文件');
            }
        }

        // 验证并获取公钥
        $pubKeyId = openssl_pkey_get_public($publicKey);

        if ($pubKeyId === false) {
            throw new \RuntimeException('无效的RSA公钥格式，请确保公钥格式正确（包含BEGIN和END标记）');
        }

        $result = openssl_public_encrypt($data, $encrypted, $pubKeyId);

        if ($result === false) {
            throw new \RuntimeException('RSA加密失败');
        }

        return base64_encode($encrypted);
    }

    /**
     * RSA私钥解密
     * @param string $data
     * @return string
     */
    public function rsaDecrypt(string $data): string
    {
        $privateKey = Config::get('encryption.rsa_private_key');

        if (empty($privateKey)) {
            throw new \RuntimeException('RSA私钥未配置');
        }

        // 判断是否为文件路径
        if (is_file($privateKey)) {
            $privateKey = file_get_contents($privateKey);

            if ($privateKey === false) {
                throw new \RuntimeException('无法读取RSA私钥文件');
            }
        }

        // 验证并获取私钥
        $privKeyId = openssl_pkey_get_private($privateKey);

        if ($privKeyId === false) {
            throw new \RuntimeException('无效的RSA私钥格式，请确保私钥格式正确（包含BEGIN和END标记）');
        }

        try {
            $result = openssl_private_decrypt(base64_decode($data), $decrypted, $privKeyId);

            if ($result === false) {
                throw new \RuntimeException('RSA解密失败');
            }

            return $decrypted;
        } finally {
            // PHP 8.0+ 会自动管理资源，不再需要显式释放
        }
    }

    /**
     * AES加密
     * @param string $data
     * @param string $key
     * @return string
     */
    public function aesEncrypt(string $data, string $key): string
    {
        $iv     = random_bytes(16);
        $mode   = Config::get('encryption.aes_mode', 'CBC');
        $method = sprintf('AES-%d-%s', Config::get('encryption.aes_key_size', 128), $mode);

        $encrypted = openssl_encrypt(
            $data,
            $method,
            $key,
            OPENSSL_RAW_DATA,
            $iv
        );

        return base64_encode($iv . $encrypted);
    }

    /**
     * AES解密
     * @param string $data
     * @param string $key
     * @return string
     */
    public function aesDecrypt(string $data, string $key): string
    {
        $data      = base64_decode($data);
        $iv        = substr($data, 0, 16);
        $encrypted = substr($data, 16);

        $mode   = Config::get('encryption.aes_mode', 'CBC');
        $method = sprintf('AES-%d-%s', Config::get('encryption.aes_key_size', 128), $mode);

        return openssl_decrypt(
            $encrypted,
            $method,
            $key,
            OPENSSL_RAW_DATA,
            $iv
        );
    }

    /**
     * 生成请求签名
     * @param array $params 请求参数
     * @param string $appSecret 应用密钥
     * @return string
     */
    public function generateSignature(array $params, string $appSecret): string
    {
        // 1. 参数按键名升序排序
        ksort($params);

        // 2. 构建签名字符串
        $signStr = '';

        foreach ($params as $key => $value) {
            if ($key !== 'sign' && !is_array($value)) {
                $signStr .= $key . '=' . $value . '&';
            }
        }
        $signStr .= 'key=' . $appSecret;

        // 3. 计算MD5
        return strtoupper(md5($signStr));
    }

    /**
     * 验证请求签名
     * @param array $params 请求参数
     * @param string $sign 签名值
     * @param string $appSecret 应用密钥
     * @return bool
     */
    public function validateSignature(array $params, string $sign, string $appSecret): bool
    {
        $calculatedSign = $this->generateSignature($params, $appSecret);

        return $calculatedSign === $sign;
    }
}
