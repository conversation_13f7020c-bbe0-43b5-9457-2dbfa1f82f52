-- 表结构: tenant_device
CREATE TABLE `tenant_device` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `tenant_id` bigint(20) unsigned NOT NULL COMMENT '租户ID',
  `code` varchar(50) NOT NULL COMMENT '设备编码',
  `name` varchar(100) NOT NULL COMMENT '设备名称',
  `location` varchar(255) NOT NULL COMMENT '经纬度',
  `depth` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '深度(米)',
  `level` tinyint(2) unsigned NOT NULL DEFAULT '0' COMMENT '层位数',
  `create_at` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_at` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  `delete_at` int(11) NOT NULL DEFAULT '0' COMMENT '删除时间',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态:-1=已删除,0=停用,1=启用',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_tenant_code` (`tenant_id`,`code`),
  UNIQUE KEY `uk_tenant_name` (`tenant_id`,`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='租户设备表';
