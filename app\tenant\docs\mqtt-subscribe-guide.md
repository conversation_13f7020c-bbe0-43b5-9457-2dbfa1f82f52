# MQTT订阅指南

## 概述

本文档详细说明了系统中MQTT订阅的使用方法，包括心跳主题、设备数据传输和设备控制等功能的订阅和使用。通过MQTT协议，系统可以实时监控设备状态、接收设备数据并进行远程控制，确保设备的正常运行和数据传输。

## 安全认证与访问控制

### 服务器端连接验证
1. 服务端会对每个连接请求进行严格验证
2. 验证内容包括：
   - 客户端ID格式校验
   - Token有效性检查
   - 租户与设备绑定关系验证
3. 验证失败会立即断开连接并记录日志

### 设备认证机制

1. 连接认证
   - 每个设备必须使用唯一的设备ID作为MQTT客户端ID
   - 设备必须使用access_token作为MQTT连接的密码字段进行身份验证
   - access_token可以通过API接口获取，有效期通常为24小时
   - 连接时必须使用TLS/SSL加密传输
   - 连接成功建立后，后续消息无需再携带token

### 连接认证示例

```javascript
// MQTT客户端连接配置示例
const mqttOptions = {
    clientId: "tenant_code@user_name",  // 客户端ID，可以用下面的username，也可以使用随机字符串（全局保持唯一即可）
    username: "tenant_code@user_name",  // 租户ID@用户名格式
    password: "eyJhbGciOiJIUzI1NiIs...",  // 客户端登录系统时生成的access_token
    clean: true,  // 清除会话
    keepalive: 60,  // 心跳间隔(秒)
    reconnectPeriod: 5000,  // 重连间隔(毫秒)
    connectTimeout: 30 * 1000  // 连接超时(毫秒)
}
```

### 租户客户端认证方式
1. 支持两种认证方式：
   - tenant_code@user_name格式
   - 直接使用tenant_id
2. 两种方式都需配合有效的access_token使用
3. 系统会自动识别并转换为内部tenant_id

### 认证最佳实践

1. Token管理
   - 在token过期前及时更新
   - 实现token自动续期机制
   - 妥善保存token，避免泄露

2. 连接安全
   - 使用TLS/SSL加密传输
   - 避免在不安全网络中传输token
   - 定期更换token

2. Token验证机制
   - 采用JWT标准格式
   - 包含设备ID、租户ID和有效期信息
   - 服务端会验证签名和有效期
   - 支持自动续期机制

3. 租户隔离
   - 设备ID与租户进行绑定
   - 系统验证设备是否属于声明的租户
   - 防止跨租户访问

### 主题访问控制

1. 主题权限
   - 设备只能订阅和发布其所属租户的主题
   - 主题格式强制包含租户ID作为前缀
   - 系统通过ACL（访问控制列表）限制主题访问

2. 权限验证
   - 订阅时验证设备对主题的访问权限
   - 发布消息时验证发送者权限
   - 拒绝未授权的订阅和发布请求

### 安全防护措施

1. 传输加密
   - 启用TLS/SSL加密
   - 使用强加密算法
   - 定期更新证书

2. 密钥管理
   - 设备密钥安全存储
   - 定期轮换密钥
   - 支持密钥吊销机制

3. 异常检测
   - 监控异常连接行为
   - 检测异常消息模式
   - 自动阻止可疑活动

---

## 🔽 租户设备心跳主题 (客户端发布)

### 用途
监控设备在线状态，确保设备与服务端保持连接。

### 主题格式
```
helio/tenant/{tenant_id}/heartbeat
```

### 参数说明
- `tenant_id`: 租户ID
- `device_id`: 设备ID

### 订阅/发布方向
- 客户端发布
- 服务端订阅

### 消息示例
```json
{
    "type": "online|offline",  // 必填，消息类型，online表示在线心跳，offline表示下线
    "device_id": "device_001",  // 必填，设备ID
    "timestamp": 1234567890  // 必填，消息时间戳
}
```

### 必填字段说明
- `type`: 必须为"online"或"offline"，表示设备在线状态
- `device_id`: 设备ID，用于路由消息
- `timestamp`: 消息时间戳，用于记录和追踪

---

## 🔽 租户设备数据主题 (客户端发布)

### 用途
设备向服务端上报传感器数据、状态信息等。

### 主题格式
```
helio/tenant/{tenant_id}/device/{device_id}/data
```

### 参数说明
- `tenant_id`: 租户ID
- `device_id`: 设备ID

### 订阅/发布方向
- 客户端发布
- 服务端订阅

### 消息格式示例
```json
{
    "type": "data",    // 必填，消息类型，固定为"data"
    "device_id": "device_001",  // 必填，设备ID
    "timestamp": 1234567890,  // 必填，消息时间戳
    "payload": {}  // 必填，数据负载，可以是对象或数组
}
```

### 必填字段说明
- `type`: 消息类型，固定为"data"
- `device_id`: 设备ID，用于路由消息
- `timestamp`: 消息时间戳，用于记录和追踪
- `payload`: 数据负载，可以是对象或数组


---

## 🔼 设备控制主题 (服务端发布)

### 用途
服务端向设备发送控制指令，实现远程控制功能。

### 主题格式
```
helio/tenant/{tenant_id}/device/{device_id}/control
```

客户端只需要订阅：

```
helio/tenant/{tenant_id}/device/+/control
```

> 将tenant_id替换为实际的租户ID即可

### 参数说明
- `tenant_id`: 租户ID
- `device_id`: 设备ID

### 订阅/发布方向
- 服务端发布
- 客户端订阅

### 消息格式示例
```json
{
    "type": "control",    // 必填，消息类型，固定为"control"
    "device_id": "device_001",  // 必填，设备ID
    "timestamp": 1234567890,  // 必填，消息时间戳
    ...
}
```

### 必填字段说明
- `type`: 必须为"control"，表示控制消息
- `device_id`: 目标设备ID，用于路由消息
- `timestamp`: 消息时间戳，用于记录和追踪

其他字段可根据实际业务需求协商添加。

### 数据处理流程

1. 数据接收
   - 系统接收设备上报的MQTT消息
   - 验证消息格式和必要字段
   - 验证设备归属权限

2. 数据解析
   - 解析消息内容
   - 根据消息类型进行分类处理
   - 数据格式转换和单位统一

3. 数据存储
   - 原始数据存储
   - 处理后数据入库
   - 历史数据归档

4. 数据分发
   - 实时数据推送
   - 告警规则匹配
   - 触发相关业务处理

### 错误处理机制

1. 数据格式错误
   - 记录错误日志
   - 返回错误响应
   - 通知设备重新发送

2. 设备认证失败
   - 记录认证失败信息
   - 断开设备连接
   - 通知管理员处理

3. 数据处理异常
   - 记录异常信息
   - 重试处理机制
   - 失败数据备份

4. 存储失败处理
   - 启用备份存储
   - 异步重试机制
   - 告警通知

## 最佳实践

1. 心跳管理
   - 建议设备每30-60秒发送一次心跳
   - 根据网络状况适当调整心跳间隔
   - 避免过于频繁的心跳占用带宽

2. 设备端实践
   - 合理设置数据上报频率
   - 实现断线重连机制
   - 添加数据校验
   - 及时响应控制指令
   - 实现指令执行状态反馈

3. 服务端实践
   - 实现消息幂等性处理
   - 设置合理的QoS级别
   - 做好数据备份
   - 控制指令超时处理
   - 指令执行状态跟踪

4. 监控建议
   - 监控设备在线状态
   - 监控数据处理延迟
   - 监控存储使用情况
   - 监控指令执行成功率
   - 监控设备响应时间

## 常见问题

1. 心跳丢失
   - 检查网络连接
   - 验证MQTT配置
   - 确认设备时钟同步
   - 查看服务器负载

2. 消息延迟
   - 优化网络环境
   - 调整QoS级别
   - 检查服务器性能
   - 考虑使用消息队列

3. 设备离线
   - 验证设备电源
   - 检查网络状态
   - 确认心跳配置
   - 查看错误日志

4. 设备数据上报失败
   - 检查网络连接
   - 验证MQTT配置
   - 确认认证信息
   - 检查数据格式

5. 设备控制问题
   - 确认设备订阅状态
   - 检查指令格式
   - 验证设备响应
   - 查看超时设置
