<?php
declare(strict_types = 1);
namespace app\system\controller;

use app\system\repository\DepartmentRepository;
use app\system\validate\Department as DepartmentValidate;
use think\App;
use think\exception\ValidateException;
use think\Response;

/**
 * 部门管理控制器
 * <AUTHOR> <<EMAIL>>
 * @package app\system\controller
 */
class Department extends BaseController
{
    /**
     * @var DepartmentRepository
     */
    protected DepartmentRepository $repository;

    public function __construct(App $app)
    {
        parent::__construct($app);

        // 指定对应仓储类
        $this->repository = new DepartmentRepository();
    }

    /**
     * 部门列表
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return \think\Response
     */
    public function list(): Response
    {
        $params   = $this->request->param();
        $pageNo   = $this->request->param('page', 1, 'intval');
        $pageSize = $this->request->param('pageSize', 20, 'intval');
        $treeMode = $this->request->param('tree_mode', 1) == 1;

        $result = $this->repository->getList($params, $pageNo, $pageSize, $treeMode);

        return $this->success($result);
    }

    /**
     * 添加部门
     * @return \think\Response
     */
    public function add(): Response
    {
        $params = $this->request->param();

        try {
            validate(DepartmentValidate::class)->scene('add')->check($params);
        } catch (ValidateException $e) {
            return $this->error($e->getError());
        }

        if ($this->repository->addDepartment($params)) {
            $this->event_log('添加部门组', $params, []);

            return $this->success('添加成功');
        }

        return $this->error('添加失败');
    }

    /**
     * 编辑部门
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \Exception
     * @return \think\Response
     */
    public function edit(): Response
    {
        $params = $this->request->param();

        try {
            validate(DepartmentValidate::class)->scene('edit')->check($params);
        } catch (ValidateException $e) {
            return $this->error($e->getError());
        }

        $info = $this->repository->getInfo($params['id']);

        // 检查状态是否发生变化
        if (isset($params['status']) && $info['status'] != $params['status']) {
            // 先更新当前部门
            if ($this->repository->updateDepartment($params['id'], $params)) {
                // 同步更新子部门状态
                $this->repository->setStatus($params['id'], (int)$params['status']);
                $this->event_log('更新部门组', $params, []);

                return $this->success('更新成功');
            }
        } elseif ($this->repository->updateDepartment($params['id'], $params)) {
            $this->event_log('更新部门组', $params, []);

            return $this->success('更新成功');
        }

        return $this->error('更新失败');
    }

    /**
     * 设置部门状态（禁用启用）
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return \think\Response
     */
    public function status(): Response
    {
        $params = $this->request->param();

        try {
            validate(DepartmentValidate::class)->scene('status')->check($params);
        } catch (ValidateException $e) {
            return $this->error($e->getError());
        }

        // 处理ID参数，将字符串或数组转换为ID数组
        $id_arr = value2Array($params['id']);

        if (empty($id_arr)) {
            return $this->error('请选择要操作的数据');
        }

        $result = $this->repository->setStatus($id_arr, (int)$params['status']);

        if ($result) {
            $this->event_log('设置部门状态', $params, []);

            return $this->success($params['status'] == 0 ? '冻结成功' : '解冻成功');
        }

        return $this->error('操作失败');
    }

    /**
     * 删除部门（软删除）
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return \think\Response
     */
    public function delete(): Response
    {
        $params = $this->request->param();

        try {
            validate(DepartmentValidate::class)->scene('delete')->check($params);
        } catch (ValidateException $e) {
            return $this->error($e->getError());
        }

        // 处理ID参数，将字符串或数组转换为ID数组
        $id_arr = value2Array($params['id']);

        if (empty($id_arr)) {
            return $this->error('请选择要操作的数据');
        }

        if ($this->repository->setStatus($id_arr, -1)) {
            $this->event_log('删除部门', $params, []);

            return $this->success('删除成功');
        }

        return $this->error('删除失败');
    }

    /**
     * 部门排序
     * @return \think\Response
     */
    public function sort(): Response
    {
        $params = $this->request->param();

        try {
            validate(DepartmentValidate::class)->scene('sort')->check($params);
        } catch (ValidateException $e) {
            return $this->error($e->getError());
        }

        if ($this->repository->updateSort($params['id'], $params['sort'])) {
            $this->event_log('更新部门排序', $params, []);

            return $this->success('更新成功');
        }

        return $this->error('更新失败');
    }
}
