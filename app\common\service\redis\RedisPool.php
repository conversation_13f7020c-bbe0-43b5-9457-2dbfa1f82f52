<?php
declare(strict_types = 1);

namespace app\common\service\redis;

use Swoole\Coroutine\Channel;
use think\facade\Config;
use think\facade\Log;

/**
 * Redis连接池
 * 基于Swoole协程Channel实现的Redis连接池
 * <AUTHOR> <<EMAIL>>
 * @package app\common\service\redis
 */
class RedisPool
{
    /**
     * 单例实例
     * @var array
     */
    private static array $instances = [];

    /**
     * 连接池
     * @var Channel
     */
    private Channel $pool;

    /**
     * 连接池大小
     * @var int
     */
    private int $poolSize;

    /**
     * 当前连接数
     * @var int
     */
    private int $currentCount = 0;

    /**
     * 连接统计
     * @var array
     */
    private array $stats = [
        'connection_times'   => 0,
        'active_connections' => 0,
    ];

    /**
     * Redis配置
     * @var array
     */
    private array $config;

    /**
     * 构造函数
     * @param int $poolSize 连接池大小
     */
    private function __construct(int $poolSize)
    {
        $this->poolSize = $poolSize;
        $this->pool     = new Channel($poolSize);
        $this->config   = Config::get('cache.stores.redis', []);

        // 预创建连接
        $this->initConnections();
    }

    /**
     * 获取连接池实例
     * @param int $poolSize 连接池大小
     * @return self
     */
    public static function getInstance(int $poolSize = 10): self
    {
        $key = "pool_{$poolSize}";

        if (!isset(self::$instances[$key])) {
            self::$instances[$key] = new self($poolSize);
        }

        return self::$instances[$key];
    }

    /**
     * 初始化连接
     */
    private function initConnections(): void
    {
        for ($i = 0; $i < $this->poolSize; $i++) {
            $redis = $this->createConnection();

            if ($redis) {
                $this->pool->push($redis);
                $this->currentCount++;
            }
        }
    }

    /**
     * 创建Redis连接
     * @return \Redis|null
     */
    private function createConnection(): ?\Redis
    {
        try {
            $redis = new \Redis();

            $host     = $this->config['host'] ?? '127.0.0.1';
            $port     = (int)($this->config['port'] ?? 6379);
            $timeout  = (float)($this->config['timeout'] ?? 3.0);
            $password = $this->config['password'] ?? '';
            $database = (int)($this->config['select'] ?? 0);

            // 连接Redis
            if (!$redis->connect($host, $port, $timeout)) {
                Log::error("Redis连接失败: {$host}:{$port}");

                return null;
            }

            // 认证
            if (!empty($password)) {
                if (!$redis->auth($password)) {
                    Log::error('Redis认证失败');
                    $redis->close();

                    return null;
                }
            }

            // 选择数据库
            if ($database > 0) {
                if (!$redis->select($database)) {
                    Log::error("Redis选择数据库失败: {$database}");
                    $redis->close();

                    return null;
                }
            }

            // 设置选项
            $redis->setOption(\Redis::OPT_SERIALIZER, \Redis::SERIALIZER_PHP);
            $redis->setOption(\Redis::OPT_PREFIX, $this->config['prefix'] ?? '');

            $this->stats['connection_times']++;

            return $redis;
        } catch (\Exception $e) {
            Log::error('创建Redis连接异常: ' . $e->getMessage());

            return null;
        }
    }

    /**
     * 获取连接
     * @param float $timeout 超时时间
     * @return \Redis|null
     */
    public function get(float $timeout = 3.0): ?\Redis
    {
        // 尝试从连接池获取连接
        $redis = $this->pool->pop($timeout);

        if ($redis === false) {
            // 超时或连接池为空，尝试创建新连接
            $redis = $this->createConnection();

            if (!$redis) {
                Log::warning('无法获取Redis连接');

                return null;
            }
        }

        // 检查连接是否有效
        if (!$this->isConnectionValid($redis)) {
            // 连接无效，重新创建
            $redis = $this->createConnection();

            if (!$redis) {
                Log::warning('重新创建Redis连接失败');

                return null;
            }
        }

        $this->stats['active_connections']++;

        return $redis;
    }

    /**
     * 归还连接
     * @param \Redis $redis
     */
    public function put(\Redis $redis): void
    {
        if ($this->isConnectionValid($redis)) {
            // 连接有效，归还到连接池
            if ($this->pool->length() < $this->poolSize) {
                $this->pool->push($redis);
            } else {
                // 连接池已满，关闭连接
                $redis->close();
                $this->currentCount--;
            }
        } else {
            // 连接无效，关闭连接
            $redis->close();
            $this->currentCount--;
        }

        $this->stats['active_connections'] = max(0, $this->stats['active_connections'] - 1);
    }

    /**
     * 检查连接是否有效
     * @param \Redis $redis
     * @return bool
     */
    private function isConnectionValid(\Redis $redis): bool
    {
        try {
            return $redis->ping() === '+PONG';
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 获取连接池统计信息
     * @return array
     */
    public function stats(): array
    {
        return [
            'pool_size'          => $this->poolSize,
            'current_count'      => $this->currentCount,
            'available'          => $this->pool->length(),
            'waiting'            => $this->pool->stats()['consumer_num'] ?? 0,
            'connection_times'   => $this->stats['connection_times'],
            'active_connections' => $this->stats['active_connections'],
        ];
    }

    /**
     * 关闭连接池
     */
    public function close(): void
    {
        // 关闭所有连接
        while (!$this->pool->isEmpty()) {
            $redis = $this->pool->pop(0.1);

            if ($redis && $redis instanceof \Redis) {
                $redis->close();
            }
        }

        $this->pool->close();
        $this->currentCount                = 0;
        $this->stats['active_connections'] = 0;
    }

    /**
     * 析构函数
     */
    public function __destruct()
    {
        $this->close();
    }
}
