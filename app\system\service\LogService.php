<?php
declare(strict_types = 1);

namespace app\system\service;

use app\common\service\FingerService;
use app\system\model\OperationLog as OperationLogModel;

/**
 * 日志服务类
 * <AUTHOR> <<EMAIL>>
 * @package app\system\service
 */
class LogService extends BaseService
{
    /**
     * 操作日志写入
     * @param $userInfo
     * @param $event
     * @param $path
     * @param $params
     * @param $description
     * @return void
     */
    public static function write($userInfo, $event, $path, $params, $description): void
    {
        if ($description && !is_array($description)) {
            $description = [$description];
        }

        // 运行时间计算
        $runtime = number_format(microtime(true) - INIT_MICROTIME, 10, '.', '');

        if (isset($params['__domain_host'])) {
            unset($params['__domain_host']);
        }

        if (isset($params['password'])) {
            $params['password'] = '******';
        }

        $finger_id = FingerService::getClientFingerId();

        $data = [
            'user_id'     => $userInfo['id'] ?? 0,
            'finger_id'   => $finger_id ?: '',
            'request_id'  => request()->requestId ?: '',
            'event'       => $event ?: 'Unknown',
            'path'        => $path ?: 'Unknown',
            'params'      => json_encode($params, JSON_UNESCAPED_UNICODE) ?: '', // 参数
            'description' => json_encode($description, JSON_UNESCAPED_UNICODE) ?: '', // 详情
            'client_ip'   => ip2long(request()->ip()),
            'user_agent'  => !empty($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : 'Unknown',
            'mem'         => number_format((memory_get_usage() - INIT_MEMORY) / 1024, 2, '.', ''), // 内存消耗
            'reqs'        => number_format(1 / $runtime, 2, '.', ''), // 吞吐量
            'runtime'     => number_format((float) $runtime, 6, '.', ''), // 响应时间
            'create_at'   => time(),
        ];

        // 插入日志记录
        OperationLogModel::addLog($data);
    }
}
