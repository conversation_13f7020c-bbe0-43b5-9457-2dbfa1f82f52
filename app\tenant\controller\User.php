<?php
declare(strict_types = 1);

namespace app\tenant\controller;

use app\tenant\repository\UserDeviceRepository;
use app\tenant\repository\UserRepository;
use app\tenant\repository\UserRoleRepository;
use app\tenant\validate\User as UserValidate;
use think\App;
use think\facade\Db;
use think\Response;

/**
 * 租户用户控制器
 * <AUTHOR> <<EMAIL>>
 * @package app\tenant\controller
 */
class User extends AuthBase
{
    public function __construct(App $app)
    {
        parent::__construct($app);

        // 检查认证token权限
        $this->checkAuth();
    }

    /**
     * 租户用户列表
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return \think\Response
     */
    public function list(): Response
    {
        $params   = $this->request->param();
        $pageNo   = $this->request->param('page', 1, 'intval');
        $pageSize = $this->request->param('pageSize', 20, 'intval');

        // 指定只能操作当租户ID
        $params['tenant_id'] = $this->getTenantId();

        $result = (new UserRepository())->getList($params, $pageNo, $pageSize);

        return $this->success($result);
    }

    /**
     * 新增租户用户
     * @return \think\Response
     */
    public function add(): Response
    {
        $params = $this->request->post();

        // 附加租户ID，让其严格验证
        $params['tenant_id'] = $this->getTenantId();

        $validate = validate(UserValidate::class);

        if (!$validate->scene('add_user')->check($params)) {
            return $this->error($validate->getError());
        }

        Db::startTrans();

        try {
            $result = (new UserRepository())->createTenantUser($params);

            if ($result) {
                // 记录操作日志
                $this->logService->recordOperationLog(
                    '新增租户用户',
                    $params
                );

                Db::commit();

                return $this->success('添加成功');
            }

            Db::rollback();

            return $this->error('添加失败');
        } catch (\Exception $e) {
            Db::rollback();

            return $this->error($e->getMessage());
        }
    }

    /**
     * 编辑用户
     * @return \think\Response
     */
    public function edit(): Response
    {
        $params = $this->request->post();
        $id     = $this->request->param('id', 0, 'intval');

        if (empty($id)) {
            return $this->error('参数错误');
        }

        // 附加租户ID，让其严格验证
        $params['tenant_id'] = $this->getTenantId();

        $userRepository = new UserRepository();

        // 查询当前租户下有无该用户
        $user = $userRepository->getTenantUserById($id);

        // 已删除的用户无法让其继续
        if (!$user) {
            return $this->error('当前租户下无该用户');
        }

        $validate = validate(UserValidate::class);

        if (!$validate->scene('edit_user')->check($params)) {
            return $this->error($validate->getError());
        }

        Db::startTrans();

        try {
            unset(
                $params['id'],
                $params['tenant_id'],
                $params['founder'],
                $params['create_at'],
                $params['delete_at'],
                $params['login_times'],
                $params['last_login_ip'],
                $params['last_login_time'],
            );

            // 更新用户数据
            $result = $userRepository->updateUser($id, $params);

            if ($result) {
                // 记录操作日志
                $this->logService->recordOperationLog(
                    '编辑租户用户',
                    $params
                );

                Db::commit();

                return $this->success('修改成功');
            }

            Db::rollback();

            return $this->error('修改失败');
        } catch (\Exception $e) {
            Db::rollback();

            return $this->error($e->getMessage());
        }
    }

    /**
     * 启用用户
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return \think\Response
     */
    public function enable(): Response
    {
        $id = $this->request->param('id', 0, 'intval');

        if (!$id) {
            return $this->error('参数错误');
        }

        $userRepository = new UserRepository();

        // 查询当前租户下有无该用户
        $user = $userRepository->getTenantUserById($id);

        // 已删除的用户无法让其继续
        if (!$user) {
            return $this->error('当前租户下无该用户');
        }

        // 检查用户名和手机号唯一性
        $existingUser = $userRepository->findByUsernameOrMobile(
            $user->username,
            $user->mobile,
            $id
        );

        if ($existingUser) {
            return $this->error('当前租户下已存在相同的用户名或手机号');
        }

        Db::startTrans();

        try {
            $result = $userRepository->enable($id);

            if ($result) {
                // 记录操作日志
                $this->logService->recordOperationLog(
                    '启用租户用户',
                    ['id' => $id]
                );

                Db::commit();

                return $this->success('启用成功');
            }

            Db::rollback();

            return $this->error('启用失败');
        } catch (\Exception $e) {
            Db::rollback();

            return $this->error($e->getMessage());
        }
    }

    /**
     * 禁用用户
     * @return \think\Response
     */
    public function disable(): Response
    {
        $id = $this->request->param('id', 0, 'intval');

        if (!$id) {
            return $this->error('参数错误');
        }

        $userRepository = new UserRepository();

        // 查询当前租户下有无该用户
        $user = $userRepository->getTenantUserById($id);

        // 已删除的用户无法让其继续
        if (!$user) {
            return $this->error('当前租户下无该用户');
        }

        Db::startTrans();

        try {
            $result = $userRepository->disable($id);

            if ($result) {
                // 记录操作日志
                $this->logService->recordOperationLog(
                    '禁用租户用户',
                    ['id' => $id]
                );

                Db::commit();

                return $this->success('禁用成功');
            }

            Db::rollback();

            return $this->error('禁用失败');
        } catch (\Exception $e) {
            Db::rollback();

            return $this->error($e->getMessage());
        }
    }

    /**
     * 删除用户
     * @return \think\Response
     */
    public function delete(): Response
    {
        $id = $this->request->param('id', 0, 'intval');

        if (!$id) {
            return $this->error('参数错误');
        }

        $userRepository = new UserRepository();

        // 查询当前租户下有无该用户
        $user = $userRepository->getTenantUserById($id);

        // 已删除的用户无法让其继续
        if (!$user) {
            return $this->error('当前租户下无该用户');
        }

        Db::startTrans();

        try {
            $result = $userRepository->recycle($id);

            if ($result) {
                // 记录操作日志
                $this->logService->recordOperationLog(
                    '删除租户用户',
                    ['id' => $id]
                );

                Db::commit();

                return $this->success('删除成功');
            }

            Db::rollback();

            return $this->error('删除失败');
        } catch (\Exception $e) {
            Db::rollback();

            return $this->error($e->getMessage());
        }
    }

    /**
     * 分配角色
     * @return \think\Response
     */
    public function assignRoles(): Response
    {
        $userId             = $this->request->param('id/d');
        $roleIds            = $this->request->param('role_ids');
        $specialPermissions = $this->request->param('special_permissions/a', [], 'value2Array');

        if (!$userId) {
            return $this->error('参数错误');
        }

        if (!is_array($roleIds)) {
            return $this->error('参数role_ids必须为数组');
        }

        try {
            // 分配角色给用户
            (new UserRoleRepository())->assignRoles($userId, $roleIds, $specialPermissions);

            return $this->success('分配成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取用户直接分配的设备ID列表
     * @return \think\Response
     */
    public function getDeviceIds(): Response
    {
        $userId = $this->request->param('id/d');

        if (!$userId) {
            return $this->error('参数错误');
        }

        try {
            // 获取用户直接分配的设备ID列表
            $deviceIds = (new UserDeviceRepository())->getAssignedDeviceIds($userId);

            return $this->success(['device_ids' => $deviceIds]);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 分配设备到用户
     * @return \think\Response
     */
    public function assignDevices(): Response
    {
        $userId    = $this->request->param('id/d');
        $deviceIds = $this->request->param('device_ids');

        if (!$userId) {
            return $this->error('参数错误');
        }

        if (!is_array($deviceIds)) {
            return $this->error('参数device_ids必须为数组');
        }

        try {
            // 分配设备给用户
            (new UserDeviceRepository())->assignDevices($userId, $deviceIds);

            // 记录操作日志
            $this->logService->recordOperationLog(
                '分配设备给用户',
                ['user_id' => $userId, 'device_ids' => $deviceIds]
            );

            return $this->success('分配成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}
