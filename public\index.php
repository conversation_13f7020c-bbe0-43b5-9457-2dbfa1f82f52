<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006-2019 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------

// [ 应用入口文件 ]
namespace think;

require __DIR__ . '/../vendor/autoload.php';

// 检查必要的PHP扩展
if (!extension_loaded('redis')) {
    exit('Error: Redis extension is not installed');
}

if (!extension_loaded('fileinfo')) {
    exit('Error: Fileinfo extension is not installed');
}

// 检查必要的PHP函数
if (!function_exists('putenv') || in_array('putenv', explode(',', ini_get('disable_functions')))) {
    exit('Error: putenv function is disabled');
}

if (!function_exists('proc_open') || in_array('proc_open', explode(',', ini_get('disable_functions')))) {
    exit('Error: proc_open function is disabled');
}

define('INIT_MEMORY', memory_get_usage()); // 初始内存占用
define('INIT_MICROTIME', microtime(true)); // 初始时间

// 执行HTTP应用并响应
$http = (new App())->http;

$response = $http->run();

$response->send();

$http->end($response);
