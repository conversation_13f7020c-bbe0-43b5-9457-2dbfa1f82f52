<?php
declare(strict_types = 1);

namespace app\system\model;

use think\db\Query;
use think\model\relation\BelongsToMany;

/**
 * 角色模型
 * <AUTHOR> <<EMAIL>>
 * @package app\system\model
 */
class Role extends BaseModel
{
    protected $name = 'system_role';

    protected $field = [];

    /**
     * 获取角色下的所有用户
     * @return BelongsToMany
     */
    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, RoleUser::class, 'user_id', 'role_id');
    }

    /**
     * 获取角色的所有权限
     * @return BelongsToMany
     */
    public function permissions(): BelongsToMany
    {
        return $this->belongsToMany(Permission::class, PermissionRole::class, 'permission_id', 'role_id');
    }

    /**
     * 查询启用状态的角色
     * @param Query $query
     * @return \think\db\Query
     */
    public function scopeEnabled(Query $query): Query
    {
        return $query->where('status', 1);
    }
}
