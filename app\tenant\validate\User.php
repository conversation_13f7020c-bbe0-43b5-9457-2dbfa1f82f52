<?php
declare(strict_types = 1);

namespace app\tenant\validate;

use app\tenant\constant\LoginType as LoginTypeConstant;
use app\tenant\model\User as UserModel;

/**
 * 租户用户验证器
 * <AUTHOR> <ch<PERSON><EMAIL>>
 * @package app\tenant\validate
 */
class User extends BaseValidate
{
    /**
     * 验证规则
     */
    protected $rule = [
        'tenant_code' => 'require|length:4,20|alphaNum',
        'username'    => 'require|length:4,20|alphaNumSymbol|checkUsernameExist',
        'password'    => 'require|length:6,64',
        'mobile'      => 'require|mobile|checkMobileExist',
        'email'       => 'require|email|checkEmailExist',
        'realname'    => 'require',
        'code'        => 'require|length:4,20', // 手机短信、邮件验证码
        'captcha'     => 'require|length:4,20', // 图形验证码
        'login_type'  => 'require|checkLoginType',
    ];

    /**
     * 错误信息
     */
    protected $message = [
        'tenant_code.require'         => '租户编码不能为空',
        'tenant_code.length'          => '租户编码长度必须在4-20个字符之间',
        'username.require'            => '用户名不能为空',
        'username.length'             => '用户名长度必须在4-20个字符之间',
        'username.alphaNumSymbol'     => '用户名只能包含字母、数字、下划线、横线、@和点号',
        'username.checkUsernameExist' => '用户名已存在',
        'password.require'            => '密码不能为空',
        'password.length'             => '密码长度必须在6-64个字符之间',
        'mobile.require'              => '手机号不能为空',
        'mobile.mobile'               => '手机号格式不正确',
        'mobile.checkMobileExist'     => '手机号已存在',
        'email.require'               => '邮箱号不能为空',
        'email.email'                 => '邮箱号格式不正确',
        'email.checkEmailExist'       => '邮箱号已存在',
        'realname.require'            => '真实姓名不能为空',
        'captcha.require'             => '验证码不能为空',
        'captcha.length'              => '验证码长度必须在4-20个字符之间',
        'code.require'                => '验证码不能为空',
        'code.length'                 => '验证码长度必须在4-20个字符之间',
        'login_type.require'          => '登录类型不能为空',
        'login_type.checkLoginType'   => '不支持的登录类型',
    ];

    /**
     * 验证场景
     */
    protected $scene = [
        // 账号密码登录验证场景
        'login_account' => [
            'login_type'  => 'require|checkLoginType',
            'tenant_code' => 'require|length:4,20|alphaNum',
            'username'    => 'require|alphaNumSymbol',
            'password'    => 'require|length:6,64',
            'captcha'     => 'require|length:4,20|alphaNum',
        ],
        // 手机验证码登录验证场景
        'login_mobile' => [
            'login_type'  => 'require|checkLoginType',
            'tenant_code' => 'require|length:4,20|alphaNum',
            'mobile'      => 'require|mobile',
            'code'        => 'require|length:4,20|alphaNum',
        ],
        // 邮箱验证码登录验证场景
        'login_email' => [
            'login_type'  => 'require|checkLoginType',
            'tenant_code' => 'require|length:4,20|alphaNum',
            'email'       => 'require|email',
            'code'        => 'require|length:4,20|alphaNum',
        ],
        // 添加租户用户验证场景
        'add_user' => [
            'username' => 'require|length:4,20|alphaNumSymbol|checkUsernameExist',
            'password' => 'require|length:6,64',
            'mobile'   => 'require|mobile|checkMobileExist',
            'email'    => 'require|email|checkEmailExist',
            'realname' => 'require',
        ],
        // 编辑租户用户验证场景
        'edit_user' => [
            'username' => 'require|length:4,20|alphaNumSymbol|checkUsernameExist',
            'password' => 'length:6,64',
            'mobile'   => 'require|mobile|checkMobileExist',
            'email'    => 'require|email|checkEmailExist',
            'realname' => 'require',
        ],
        // 用户资料修改验证场景
        'update_profile' => [
            'username' => 'require|length:4,20|alphaNumSymbol|checkUsernameExist',
            'password' => 'length:6,64',
            'mobile'   => 'require|mobile|checkMobileExist',
            'email'    => 'require|email|checkEmailExist',
            'realname' => 'require',
        ],
    ];

    /**
     * 检查用户名在当前租户下是否唯一
     * @param mixed $value
     * @param mixed $rule
     * @param array $data
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return bool
     */
    protected function checkUsernameExist(mixed $value, mixed $rule, array $data = []): bool
    {
        $tenantId = $data['tenant_id'];
        $exists   = UserModel::where('username', $value)
            ->where('tenant_id', $tenantId)
            ->where('status', '>=', 0)
            ->when(isset($data['id']), function($query) use ($data) {
                $query->where('id', '<>', $data['id']);
            })
            ->find();

        return is_null($exists);
    }

    /**
     * 检查手机号在当前租户下是否唯一
     * @param mixed $value
     * @param mixed $rule
     * @param array $data
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return bool
     */
    protected function checkMobileExist(mixed $value, mixed $rule, array $data = []): bool
    {
        $tenantId = $data['tenant_id'];
        $exists   = UserModel::where('mobile', $value)
            ->where('tenant_id', $tenantId)
            ->where('status', '>=', 0)
            ->when(isset($data['id']), function($query) use ($data) {
                $query->where('id', '<>', $data['id']);
            })
            ->find();

        return is_null($exists);
    }

    /**
     * 检查邮箱号在当前租户下是否唯一
     * @param mixed $value
     * @param mixed $rule
     * @param array $data
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return bool
     */
    protected function checkEmailExist(mixed $value, mixed $rule, array $data = []): bool
    {
        $tenantId = $data['tenant_id'];
        $exists   = UserModel::where('email', $value)
            ->where('tenant_id', $tenantId)
            ->where('status', '>=', 0)
            ->when(isset($data['id']), function($query) use ($data) {
                $query->where('id', '<>', $data['id']);
            })
            ->find();

        return is_null($exists);
    }

    /**
     * 检查登录类型是否合法
     * @param mixed $value
     * @param mixed $rule
     * @param array $data
     * @return bool
     */
    protected function checkLoginType(mixed $value, mixed $rule, array $data = []): bool
    {
        return !is_null(LoginTypeConstant::getConfig($value));
    }
}
