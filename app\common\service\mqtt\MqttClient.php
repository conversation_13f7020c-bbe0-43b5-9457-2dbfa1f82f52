<?php
namespace app\common\service\mqtt;

use Simps\MQTT\Client;
use Simps\MQTT\Config\ClientConfig;
use think\facade\Log;

/**
 * MQTT客户端类
 * 封装了MQTT客户端的基本操作
 * <AUTHOR> <<EMAIL>>
 * @package app\common\service
 */
class MqttClient
{
    /**
     * MQTT客户端
     * @var ?Client
     */
    private ?Client $client = null;

    /**
     * MQTT配置
     * @var array
     */
    private array $config = [];

    /**
     * 协议版本，默认使用v5
     * @var int
     */
    private int $protocolVersion;

    /**
     * 是否已连接
     * @var bool
     */
    private bool $connected = false;

    /**
     * 连接配置
     * @var ?ClientConfig
     */
    private ?ClientConfig $clientConfig = null;

    /**
     * 客户端ID
     * @var string
     */
    private string $clientId;

    /**
     * 构造函数
     * @param array $config MQTT配置
     * @param string $clientIdSuffix 客户端ID后缀
     */
    public function __construct(array $config, string $clientIdSuffix = '')
    {
        $this->config          = $config;
        $this->protocolVersion = intval($config['protocol_version'] ?? 5);
        // 生成客户端ID
        $this->clientId = $this->config['client_id_prefix'] . ($clientIdSuffix ?: Client::genClientID());
    }

    /**
     * 获取连接配置
     * @return \Simps\MQTT\Config\ClientConfig
     */
    protected function getConnectConfig(): ClientConfig
    {
        if ($this->clientConfig !== null) {
            return $this->clientConfig;
        }
        $this->clientConfig = new ClientConfig();
        $this->clientConfig->setClientId($this->clientId)->setKeepAlive((int)$this->config['keepalive'])->setDelay(3000) // 重连时的延迟时间 (毫秒)
        ->setMaxAttempts(-1); // 最大重连次数。默认-1，表示不限制

        // 设置基本认证信息
        // 如果配置了服务器专用认证信息，优先使用服务器认证
        if ($this->config['server_username'] && $this->config['server_password']) {
            $this->clientConfig->setUserName($this->config['server_username'])->setPassword($this->config['server_password']);
        } elseif ($this->config['username'] && $this->config['password']) {
            $this->clientConfig->setUserName($this->config['username'])->setPassword($this->config['password']);
        }

        if ($this->protocolVersion == 5) {
            // MQTT v5 特有配置
            $this->clientConfig->setProtocolLevel(5);
            $this->clientConfig->setProperties([ // MQTT5 中所需要的属性
                'session_expiry_interval' => 0, // 会话过期间隔，0表示永不过期
                'receive_maximum'         => 100, // 接收最大值
                'topic_alias_maximum'     => 10, // 主题别名最大值
            ]);
        } else {
            // MQTT v3 配置
            $this->clientConfig->setProtocolLevel(4); // MQTT 3.1.1
        }
        $swooleConfig = [
            'open_mqtt_protocol' => true,
            'package_max_length' => 2 * 1024 * 1024,
            'connect_timeout'    => (float)$this->config['connect_timeout'],
            'write_timeout'      => (float)$this->config['write_timeout'],
            'read_timeout'       => (float)$this->config['read_timeout'],
        ];
        $this->clientConfig->setSwooleConfig($swooleConfig);

        return $this->clientConfig;
    }

    /**
     * 连接MQTT服务器
     * @param array $willMessage 遗嘱消息，可根据需要配置
     * @return bool 是否连接成功
     */
    public function connect(array $willMessage = []): bool
    {
        if ($this->connected && $this->client) {
            return true;
        }

        try {
            // 创建客户端实例，在协程环境中使用
            $this->client = new Client($this->config['host'], (int)$this->config['port'], $this->getConnectConfig());
            // 连接服务器
            $result = $this->client->connect((bool)$this->config['clean_session'], $willMessage);
            // 检查连接结果
            $this->connected = $result['code'] === 0; // 0 表示成功

            if ($this->connected) {
                Log::info("MQTT客户端 {$this->clientId} 连接成功: {$this->config['host']}:{$this->config['port']},协议版本：v{$this->protocolVersion}");
            } else {
                $errorMessage = "MQTT客户端 {$this->clientId} 连接失败: {$this->config['host']}:{$this->config['port']},协议版本：v{$this->protocolVersion}";
                $errorMessage .= "\n用户名: {$this->config['username']}";
                $errorMessage .= "\n错误代码: {$result['code']}";

                if (isset($result['properties'])) {
                    $errorMessage .= "\n错误属性: " . json_encode($result['properties'], JSON_UNESCAPED_UNICODE);
                }
                Log::error($errorMessage);
            }

            return $this->connected;
        } catch (\Exception $e) {
            Log::error("MQTT客户端 {$this->clientId} 连接异常: " . $e->getMessage());
            $this->connected = false;

            return false;
        }
    }

    /**
     * 发布消息到指定主题
     * @param string $topic 主题
     * @param string $message 消息内容
     * @param int|null $qos QoS级别
     * @param int|null $dup 重发标志，默认 0
     * @param int|null $retain 是否保留消息
     * @param array $properties 属性，MQTT5 中需要，可选
     * @return bool 是否发布成功
     */
    public function publish(
        string $topic,
        string $message,
        ?int $qos = 0,
        ?int $dup = 0,
        ?int $retain = 0,
        array $properties = []
    ): bool {
        if (!$this->connected && !$this->connect()) {
            return false;
        }

        try {
            $qos    ??= (int)$this->config['qos'];
            $retain ??= (int)$this->config['retain'];

            if ($this->protocolVersion == 5) {
                // MQTT v5 发布
                $result = $this->client->publish($topic, $message, $qos, $dup, $retain, $properties);

                if (is_bool($result)) {
                    return $result;
                }

                return $result['reason_code'] === 0; // 0 表示成功
            }

            // MQTT v3 发布
            return $this->client->publish($topic, $message, $qos, $dup, $retain);
        } catch (\Exception $e) {
            Log::error("MQTT客户端 {$this->clientId} 发布消息异常: " . $e->getMessage());

            return false;
        }
    }

    /**
     * 断开连接
     * @param int $resCode 响应码
     * @param array $properties 属性，MQTT5 中需要
     * @return bool 是否断开成功
     */
    public function disconnect(int $resCode = 0, array $properties = []): bool
    {
        if (!$this->connected || !$this->client) {
            return true;
        }

        try {
            if ($this->protocolVersion == 5) {
                // MQTT v5 断开连接
                $this->client->close($resCode, $properties);
            } else {
                // MQTT v3 断开连接
                $this->client->close();
            }
            $this->connected = false;
            $this->client    = null;

            return true;
        } catch (\Exception $e) {
            Log::error("MQTT客户端 {$this->clientId} 断开连接异常: " . $e->getMessage());
            $this->connected = false;
            $this->client    = null;

            return false;
        }
    }

    /**
     * 获取当前连接状态
     * @return bool 是否已连接
     */
    public function isConnected(): bool
    {
        return $this->connected && $this->client !== null;
    }

    /**
     * 析构函数
     * 确保在对象销毁时关闭MQTT连接
     */
    public function __destruct()
    {
        $this->disconnect();
    }
}
