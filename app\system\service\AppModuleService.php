<?php
declare(strict_types = 1);
namespace app\system\service;

use app\system\constant\AppModuleConstant;
use app\system\exception\AppModuleException;
use app\system\model\App as AppModel;
use app\system\model\Permission as PermissionModel;
use think\facade\Cache;
use think\facade\Db;
use think\facade\Log;

/**
 * 应用模块服务层
 * <AUTHOR> <<EMAIL>>
 * @package app\system\service
 */
class AppModuleService extends BaseService
{
    protected AppModel $AppModel;

    protected PermissionModel $PermissionModel;

    public function __construct()
    {
        parent::__construct();

        $this->AppModel        = new AppModel();
        $this->PermissionModel = new PermissionModel();
    }

    /**
     * 缓存前缀和表名常量
     */
    protected const CACHE_PREFIX = 'app_module:';

    /**
     * 统一的缓存处理方法
     */
    protected function handleCache(string $key, callable $callback, int $ttl = 600)
    {
        $cacheKey = $this->getCacheKey($key);

        try {
            $data = Cache::get($cacheKey);

            if ($data !== null) {
                return $data;
            }
        } catch (\Exception $e) {
            Log::error('Cache get error: ' . $e->getMessage());
        }
        $data = $callback();

        try {
            Cache::set($cacheKey, $data, $ttl);
        } catch (\Exception $e) {
            Log::error('Cache set error: ' . $e->getMessage());
        }

        return $data;
    }

    /**
     * 获取缓存键
     */
    protected function getCacheKey(string $key): string
    {
        return self::CACHE_PREFIX . $key;
    }

    /**
     * 获取应用实例
     * @param string $appCode
     * @return mixed
     */
    public function getAppInstance(string $appCode): mixed
    {
        $appClass = "\\app\\{$appCode}\\{$appCode}App";

        return class_exists($appClass) ? new $appClass() : null;
    }

    /**
     * 获取应用列表
     * @param array $where
     * @param int $page
     * @param int $pageSize
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return array
     */
    public function getList(array $where = [], int $page = 1, int $pageSize = 15): array
    {
        $count = $this->AppModel->where($where)->count();
        $list  = [];

        if ($count > 0) {
            $list = $this->AppModel->where($where)->page($page, $pageSize)->select()->toArray();
        }

        return [
            'list'     => $list,
            'count'    => $count,
            'page'     => $page,
            'pageSize' => $pageSize,
        ];
    }

    /**
     * 安装应用
     * @param $appCode
     * @throws \app\system\exception\AppModuleException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return bool
     */
    public function install($appCode): bool
    {
        // 检查应用是否存在
        $appClass = $this->getAppClass($appCode);

        if (!class_exists($appClass)) {
            throw new AppModuleException(
                AppModuleConstant::APP_NOT_FOUND['message'],
                AppModuleConstant::APP_NOT_FOUND['code']
            );
        }

        // 创建应用实例
        $app = new $appClass();
        // 获取应用信息并验证
        $appInfo = $app->getInfo();

        $this->validateAppInfo($appInfo);

        // 开启事务
        Db::startTrans();

        try {
            // 写入应用信息
            $appId = $this->AppModel->insertGetId([
                'name'        => $appInfo['name'],
                'code'        => $appInfo['code'],
                'version'     => $appInfo['version'],
                'description' => $appInfo['description'],
                'author'      => $appInfo['author'],
                'website'     => $appInfo['website'],
                'create_at'   => time(),
                'update_at'   => time(),
            ]);

            // 注册应用菜单
            $this->installMenu($appId, $appCode);
            // 执行应用安装
            $app->install();

            Db::commit();

            return true;
        } catch (\Exception $e) {
            Db::rollback();

            throw new AppModuleException(
                AppModuleConstant::APP_INSTALL_FAILED['message'] . ': ' . $e->getMessage(),
                AppModuleConstant::APP_INSTALL_FAILED['code']
            );
        }
    }

    /**
     * 卸载应用
     * @param string $appCode
     * @throws \app\system\exception\AppModuleException
     * @return bool
     */
    public function uninstall(string $appCode): bool
    {
        $app = $this->getAppInstance($appCode);

        if (!$app) {
            throw new AppModuleException(
                AppModuleConstant::APP_NOT_FOUND['message'],
                AppModuleConstant::APP_NOT_FOUND['code']
            );
        }

        try {
            // 删除应用菜单之前先检查是否有依赖
            $dependencies = $this->checkDependencies($appCode);

            if (!empty($dependencies)) {
                throw new AppModuleException(AppModuleConstant::APP_HAS_DEPENDENCIES['message'] . ': ' . implode(
                    ',',
                    $dependencies
                ), AppModuleConstant::APP_HAS_DEPENDENCIES['code']);
            }
            Db::startTrans();

            // 删除应用信息
            $this->AppModel->where('code', $appCode)->delete();
            // 删除应用菜单
            $this->PermissionModel->where('app_code', $appCode)->delete();
            // 执行应用卸载
            $app->uninstall();

            Db::commit();
            // 统一使用clearAppCache方法
            $this->clearAppCache($appCode);

            return true;
        } catch (\Exception $e) {
            Db::rollback();

            throw new AppModuleException(
                AppModuleConstant::APP_UNINSTALL_FAILED['message'] . ': ' . $e->getMessage(),
                AppModuleConstant::APP_UNINSTALL_FAILED['code']
            );
        }
    }

    /**
     * 启用应用
     * @param $appCode
     * @throws \app\system\exception\AppModuleException
     * @throws \think\db\exception\DbException
     * @return bool
     */
    public function enable($appCode): bool
    {
        $app = $this->getAppInstance($appCode);

        if (!$app) {
            throw new AppModuleException(
                AppModuleConstant::APP_NOT_FOUND['message'],
                AppModuleConstant::APP_NOT_FOUND['code']
            );
        }
        Db::startTrans();

        try {
            // 更新应用状态
            $this->AppModel->where('code', $appCode)->update(['status' => 1]);
            // 启用应用菜单
            $this->PermissionModel->where('app_code', $appCode)->update(['status' => 1]);
            // 执行应用启用
            $app->enable();
            Db::commit();

            return true;
        } catch (\Exception $e) {
            Db::rollback();

            throw new AppModuleException(
                AppModuleConstant::APP_ENABLE_FAILED['message'] . ': ' . $e->getMessage(),
                AppModuleConstant::APP_ENABLE_FAILED['code']
            );
        }
    }

    /**
     * 禁用应用
     * @param $appCode
     * @throws \think\db\exception\DbException
     * @throws \Exception
     * @return bool
     */
    public function disable($appCode): bool
    {
        $app = $this->getAppInstance($appCode);

        if (!$app) {
            throw new AppModuleException(
                AppModuleConstant::APP_NOT_FOUND['message'],
                AppModuleConstant::APP_NOT_FOUND['code']
            );
        }
        Db::startTrans();

        try {
            // 更新应用状态
            $this->AppModel->where('code', $appCode)->update(['status' => 0]);
            // 禁用应用菜单
            $this->PermissionModel->where('app_code', $appCode)->update(['status' => 0]);
            // 执行应用禁用
            $app->disable();
            Db::commit();

            return true;
        } catch (\Exception $e) {
            Db::rollback();

            throw new AppModuleException(
                AppModuleConstant::APP_DISABLE_FAILED['message'] . ': ' . $e->getMessage(),
                AppModuleConstant::APP_DISABLE_FAILED['code']
            );
        }
    }

    /**
     * 升级应用
     * @param string $appCode
     * @param string $version
     * @throws AppModuleException
     * @return bool
     */
    public function upgrade(string $appCode, string $version): bool
    {
        if (!$this->isValidVersion($version)) {
            throw new AppModuleException(
                AppModuleConstant::APP_VERSION_INVALID['message'],
                AppModuleConstant::APP_VERSION_INVALID['code']
            );
        }
        // 验证版本兼容性
        $this->verifyVersion($appCode, $version);
        $app = $this->getAppInstance($appCode);

        if (!$app) {
            throw new AppModuleException(
                AppModuleConstant::APP_NOT_FOUND['message'],
                AppModuleConstant::APP_NOT_FOUND['code']
            );
        }

        try {
            Db::startTrans();

            // 更新应用版本信息
            $this->AppModel->where('code', $appCode)->update([
                'version'   => $version,
                'update_at' => time(),
            ]);
            // 执行应用升级
            $app->upgrade($version);

            Db::commit();
            // 清除应用缓存
            Cache::delete('app_status_' . $appCode);

            return true;
        } catch (\Exception $e) {
            Db::rollback();

            throw new AppModuleException(
                AppModuleConstant::APP_UPGRADE_FAILED['message'] . ': ' . $e->getMessage(),
                AppModuleConstant::APP_UPGRADE_FAILED['code']
            );
        }
    }

    /**
     * 获取应用详情
     * @param string $appCode
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return array|null
     */
    public function getDetail(string $appCode): ?array
    {
        $app = $this->AppModel->where('code', $appCode)->find();

        if (!$app) {
            return null;
        }
        // 获取应用配置信息
        $instance = $this->getAppInstance($appCode);
        $info     = $instance ? $instance->getInfo() : [];

        return array_merge($app->toArray(), $info);
    }

    /**
     * 获取应用类名
     * @param $appCode
     * @return string
     */
    protected function getAppClass($appCode): string
    {
        return "\\app\\{$appCode}\\{$appCode}App";
    }

    /**
     * 安装应用菜单
     * @param $appId
     * @param $appCode
     * @throws \app\system\exception\AppModuleException
     * @return void
     */
    protected function installMenu($appId, $appCode)
    {
        $menuFile = app_path() . $appCode . '/config/menu.php';

        if (!file_exists($menuFile)) {
            throw new AppModuleException(
                AppModuleConstant::MENU_FILE_NOT_FOUND['message'],
                AppModuleConstant::MENU_FILE_NOT_FOUND['code']
            );
        }
        $menus = include $menuFile;

        if (!is_array($menus)) {
            throw new AppModuleException(
                AppModuleConstant::MENU_CONFIG_INVALID['message'] . ': 配置文件格式错误',
                AppModuleConstant::MENU_CONFIG_INVALID['code']
            );
        }

        foreach ($menus as $menu) {
            $this->validateMenu($menu);
            $this->createMenu($menu, 0, $appCode);
        }
    }

    /**
     * 创建菜单
     * @param $menu
     * @param int $pid
     * @param string $appCode
     * @return void
     */
    protected function createMenu($menu, int $pid = 0, string $appCode = ''): void
    {
        $data = [
            'pid'       => $pid,
            'type'      => $menu['type'] ?? 'menu',
            'title'     => $menu['title'],
            'name'      => $menu['name'],
            'path'      => $menu['path'] ?? '',
            'icon'      => $menu['icon'] ?? '',
            'app_code'  => $appCode,
            'status'    => 1,
            'create_at' => time(),
            'update_at' => time(),
        ];
        $id = $this->PermissionModel->insertGetId($data);

        if (!empty($menu['children'])) {
            foreach ($menu['children'] as $child) {
                $this->createMenu($child, $id, $appCode);
            }
        }
    }

    /**
     * 验证菜单配置
     * @throws \app\system\exception\AppModuleException
     */
    protected function validateMenu(array $menu): void
    {
        $requiredFields = ['name', 'title'];

        foreach ($requiredFields as $field) {
            if (empty($menu[$field])) {
                throw new AppModuleException(
                    AppModuleConstant::MENU_FIELD_REQUIRED['message'] . ': ' . $field,
                    AppModuleConstant::MENU_FIELD_REQUIRED['code']
                );
            }
        }
    }

    /**
     * 批量启用/禁用应用
     * @param array $appCodes
     * @param int $status
     * @throws \Exception
     * @return bool
     */
    public function batchUpdateStatus(array $appCodes, int $status): bool
    {
        if (!in_array($status, [1, 0])) {
            throw new AppModuleException(
                AppModuleConstant::APP_INVALID_STATUS['message'],
                AppModuleConstant::APP_INVALID_STATUS['code']
            );
        }
        Db::startTrans();

        try {
            foreach ($appCodes as $appCode) {
                $app = $this->getAppInstance($appCode);

                if (!$app) {
                    throw new AppModuleException(
                        AppModuleConstant::APP_NOT_FOUND['message'],
                        AppModuleConstant::APP_NOT_FOUND['code']
                    );
                }
                // 更新应用状态
                $this->AppModel->where('code', $appCode)->update([
                    'status'    => $status,
                    'update_at' => time(),
                ]);
                // 更新菜单状态
                $this->PermissionModel->where('app_code', $appCode)->update(['status' => $status]);

                // 执行应用启用/禁用
                if ($status == 1) {
                    $app->enable();
                } else {
                    $app->disable();
                }
                // 统一使用clearAppCache方法
                $this->clearAppCache($appCode);
            }
            Db::commit();

            return true;
        } catch (\Exception $e) {
            Db::rollback();

            throw new AppModuleException(
                AppModuleConstant::APP_BATCH_UPDATE_STATUS_FAILED['message'] . ': ' . $e->getMessage(),
                AppModuleConstant::APP_BATCH_UPDATE_STATUS_FAILED['code']
            );
        }
    }

    /**
     * 获取应用配置
     * @param string $appCode
     * @return array
     */
    public function getConfig(string $appCode): array
    {
        $configFile = app_path() . $appCode . '/config/config.php';

        if (!file_exists($configFile)) {
            return [];
        }

        return include $configFile;
    }

    /**
     * 保存应用配置
     * @param string $appCode
     * @param array $config
     * @throws \app\system\exception\AppModuleException
     * @return bool
     */
    public function saveConfig(string $appCode, array $config): bool
    {
        $configFile = app_path() . $appCode . '/config/config.php';
        $content    = "<?php\nreturn " . var_export($config, true) . ";\n";

        if (!@file_put_contents($configFile, $content)) {
            throw new AppModuleException(
                AppModuleConstant::CONFIG_SAVE_FAILED['message'],
                AppModuleConstant::CONFIG_SAVE_FAILED['code']
            );
        }

        return true;
    }

    /**
     * 检查应用依赖
     * @param string $appCode
     * @return array
     */
    protected function checkDependencies(string $appCode): array
    {
        // 检查其他应用是否依赖此应用
        $dependencies = [];
        $apps         = Db::name('system_app')->where('status', 1)->select();

        foreach ($apps as $app) {
            if ($app['code'] === $appCode) {
                continue;
            }
            $instance = $this->getAppInstance($app['code']);

            if (!$instance) {
                continue;
            }
            $info = $instance->getInfo();

            if (!empty($info['require'])) {
                if (isset($info['require'][$appCode])) {
                    $dependencies[] = $app['name'];
                }
            }
        }

        return $dependencies;
    }

    /**
     * 验证应用版本兼容性
     * @param string $appCode
     * @param string $version
     * @throws \app\system\exception\AppModuleException
     */
    protected function verifyVersion(string $appCode, string $version): void
    {
        $app = $this->getAppInstance($appCode);

        if (!$app) {
            throw new AppModuleException(
                AppModuleConstant::APP_NOT_FOUND['message'],
                AppModuleConstant::APP_NOT_FOUND['code']
            );
        }
        $info = $app->getInfo();

        if (version_compare($version, $info['version'], '<')) {
            throw new AppModuleException(
                AppModuleConstant::APP_VERSION_LOW['message'],
                AppModuleConstant::APP_VERSION_LOW['code']
            );
        }
    }

    /**
     * 验证版本号格式
     * @param string $version
     * @return bool
     */
    protected function isValidVersion(string $version): bool
    {
        return (bool)preg_match(
            '/^\d+\.\d+\.\d+(?:-[0-9A-Za-z-]+(?:\.[0-9A-Za-z-]+)*)?(?:\+[0-9A-Za-z-]+)?$/',
            $version
        );
    }

    /**
     * 清除应用相关缓存
     * @param string $appCode
     */
    protected function clearAppCache(string $appCode): void
    {
        Cache::delete($this->getCacheKey('status:' . $appCode));
        Cache::delete($this->getCacheKey('info:' . $appCode));
    }

    /**
     * 验证应用信息
     * @param array $appInfo
     * @throws \app\system\exception\AppModuleException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return void
     */
    protected function validateAppInfo(array $appInfo): void
    {
        $requiredFields = ['name', 'code', 'version', 'description'];

        foreach ($requiredFields as $field) {
            if (empty($appInfo[$field])) {
                throw new AppModuleException(
                    AppModuleConstant::APP_INFO_INVALID['message'] . ': ' . $field . ' 不能为空',
                    AppModuleConstant::APP_INFO_INVALID['code']
                );
            }
        }
        // 检查应用是否已安装
        $exists = Db::name('system_app')->where('code', $appInfo['code'])->find();

        if ($exists) {
            throw new AppModuleException(
                AppModuleConstant::APP_ALREADY_INSTALLED['message'],
                AppModuleConstant::APP_ALREADY_INSTALLED['code']
            );
        }
    }

    /**
     * 检查应用模块状态
     * @param string $appCode
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return array
     */
    public function checkAppModuleStatus(string $appCode): array
    {
        return $this->handleCache('status:' . $appCode, function() use ($appCode) {
            $app = $this->AppModel->where('code', $appCode)->find();

            return [
                'installed' => !empty($app),
                'enabled'   => !empty($app) && $app['status'] == 1,
                'version'   => $app['version'] ?? '',
                'app'       => !empty($app) ? $app->toArray() : [],
            ];
        });
    }
}
