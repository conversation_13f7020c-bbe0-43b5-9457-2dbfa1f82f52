<?php
declare(strict_types = 1);

namespace app\tenant\controller;

use app\common\controller\CommonController;
use think\Response;

/**
 * 公共接口控制器
 * <AUTHOR> <<EMAIL>>
 * @package app\api\controller
 */
class Common extends CommonController
{
    /**
     * 客户端认证
     * @return \think\Response
     */
    public function info(): Response
    {
        $result = [
            'config' => [
                'system_close'       => false,
                'system_copyright'   => "<span class='copyright'>©多租户管理系统</span><span class='icp_code'>京ICP备********号</span>",
                'system_description' => 'Multi tenant management system',
                'system_keyword'     => '多租户管理系统',
                'system_login_type'  => 'account_login',
                'system_title'       => '多租户管理系统',
                'system_url'         => 'https://x1.devmix.cn',
            ],
            'userinfo' => [],
        ];

        return $this->success($result);
    }
}
