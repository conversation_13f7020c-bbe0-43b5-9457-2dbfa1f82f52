<?php
declare(strict_types = 1);

namespace app\tenant\model;

use think\model\relation\BelongsTo;

/**
 * 用户-角色关联模型类
 * <AUTHOR> <<EMAIL>>
 * @package app\tenant\controller
 */
class UserRole extends BaseModel
{
    protected $name = 'tenant_user_role';

    // 移除主键定义，使用复合主键
    protected $pk = ['tenant_id', 'user_id', 'role_id'];

    // special_permissions字段json自动转换
    protected $json = ['special_permissions'];

    protected $jsonAssoc = true;

    // 关闭自动维护时间戳
    protected $autoWriteTimestamp = false;

    /**
     * 定义与用户表的关联
     * @return \think\model\relation\BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    /**
     * 定义与角色表的关联
     * @return \think\model\relation\BelongsTo
     */
    public function role(): BelongsTo
    {
        return $this->belongsTo(Role::class, 'role_id', 'id');
    }
}
