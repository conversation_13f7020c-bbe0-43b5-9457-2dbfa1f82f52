<?php
declare(strict_types = 1);

namespace app\system\controller;

use app\common\service\MonitorService;
use think\Response;

/**
 * 公共接口控制器
 * <AUTHOR> <<EMAIL>>
 * @package app\system\controller
 */
class Common extends BaseController
{
    /**
     * 系统信息接口
     * @return \think\Response
     */
    public function info(): Response
    {
        $result = [
            'config' => [
                'system_close'       => false,
                'system_copyright'   => "<span class='copyright'>©DevOps运维管理系统</span><span class='icp_code'>京ICP备********号</span>",
                'system_description' => 'DevOps Operations Management System',
                'system_keyword'     => 'DevOps运维管理系统',
                'system_login_type'  => 'account_login,sms_login,wechat_login',
                'system_title'       => 'DevOps运维管理系统',
                'system_url'         => 'https://x1.devmix.cn',
            ],
            'userinfo' => $this->getUserInfo() ?: [],
        ];

        return $this->success($result);
    }

    /**
     * 获取服务信息
     * @return \think\Response
     */
    public function monitor(): Response
    {
        $params   = $this->request->param();
        $type     = $params['type']     ?? '';
        $specific = $params['specific'] ?? false;

        $monitorService = new MonitorService();

        if (empty($type)) {
            $data = [
                'app'   => $monitorService->app($params, $specific),
                'php'   => $monitorService->php($specific),
                'mysql' => $monitorService->mysql($specific),
                'redis' => $monitorService->redis($specific),
            ];
        } else {
            switch ($type) {
                case 'app':
                    $data[$type] = $monitorService->app($params, $specific);
                    break;
                case 'php':
                    $data[$type] = $monitorService->php($specific);
                    break;
                case 'mysql':
                    $data[$type] = $monitorService->mysql($specific);
                    break;
                case 'redis':
                    $data[$type] = $monitorService->redis($specific);
                    break;
                default:
                    $data = [];
                    break;
            }
        }

        if (empty($type) || $type == 'app') {
            // 响应时间加到最后，格式化成毫秒
            $data['app']['request_time'] = ((microtime(true) - INIT_MICROTIME) * 1000) . ' ms';
        }

        return $this->success($data);
    }
}
