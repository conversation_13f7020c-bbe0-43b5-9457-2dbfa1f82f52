# 租户认证指南

## 概述

本文档详细说明了租户系统的认证机制，包括登录流程和双token认证机制。系统采用了基于JWT的双token认证策略，包含access_token和refresh_token，以提供安全可靠的身份验证服务。

## 认证流程

### 登录流程

1. 用户提供账号和密码进行登录
2. 服务器验证凭据
3. 验证通过后，同时生成access_token和refresh_token
4. 返回token信息给客户端

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Server as 服务器
    participant DB as 数据库
    
    Client->>Server: 发送登录请求(用户名/密码)
    Server->>DB: 验证用户凭据
    DB-->>Server: 返回验证结果
    Server->>Server: 生成access_token和refresh_token
    Server-->>Client: 返回token信息
```

## 双Token机制

### Access Token

- 用途：用于访问API接口的主要令牌
- 特点：
  - 短期有效（通常15-30分钟）
  - 包含用户基本信息
  - 用于API请求的身份验证

### Refresh Token

- 用途：用于获取新的access_token
- 特点：
  - 长期有效（通常7-30天）
  - 只能用于刷新access_token
  - 存储在数据库中便于管理

## Token生命周期

### Access Token生命周期

1. 生成：登录成功或刷新token时生成
2. 有效期：默认15分钟
3. 失效处理：
   - 到期自动失效
   - 可通过refresh_token获取新的access_token

### Refresh Token生命周期

1. 生成：仅在登录时生成
2. 有效期：默认7天
3. 失效处理：
   - 到期需要重新登录
   - 可配置是否允许自动续期

## Token刷新流程

当access_token过期时，客户端需要使用refresh_token获取新的access_token：

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Server as 服务器
    
    Client->>Server: API请求(过期的access_token)
    Server-->>Client: 返回401错误
    Client->>Server: 请求刷新token(refresh_token)
    Server->>Server: 验证refresh_token
    Server-->>Client: 返回新的access_token
```

## 安全性考虑

1. Token存储
   - 客户端：access_token存储在内存中
   - 服务端：refresh_token存储在数据库中

2. Token传输
   - 使用HTTPS传输
   - 将token放在Authorization头中

3. Token撤销
   - 支持手动撤销refresh_token
   - 支持批量撤销用户的所有token

## 客户端集成指南

### 1. 接口认证

所有需要认证的API请求都需要在Header中携带access_token：

```http
GET /api/endpoint
Authorization: Bearer <access_token>
```

### 2. 无感知Token刷新

为了提供更好的用户体验，系统实现了无感知token刷新机制。主要特点：

1. 自动拦截所有API请求
2. 统一处理token过期
3. 并发请求队列管理
4. 自动重试失败请求

示例代码：

```javascript
// 请求队列，用于存储401状态下的请求
let pendingRequests = [];
// 标记是否正在刷新token
let isRefreshing = false;

async function handleApiRequest(config) {
  try {
    // 发起API请求
    const response = await fetch(config.url, {
      ...config,
      headers: {
        ...config.headers,
        'Authorization': `Bearer ${getAccessToken()}`
      }
    });
    
    // 处理401错误
    if (response.status === 401) {
      // 将当前请求添加到队列
      const retryRequest = new Promise((resolve, reject) => {
        pendingRequests.push({
          config,
          resolve,
          reject
        });
      });

      if (!isRefreshing) {
        isRefreshing = true;
        const newToken = await refreshAccessToken();
        isRefreshing = false;

        if (newToken) {
          // 重试队列中的所有请求
          pendingRequests.forEach(({config, resolve}) => {
            config.headers['Authorization'] = `Bearer ${newToken}`;
            resolve(fetch(config.url, config));
          });
          pendingRequests = [];
        } else {
          // refresh_token过期，清空队列并跳转登录
          pendingRequests.forEach(({reject}) => {
            reject(new Error('需要重新登录'));
          });
          pendingRequests = [];
          redirectToLogin();
          return null;
        }
      }

      return retryRequest;
    }
    
    return response;
  } catch (error) {
    console.error('API请求错误:', error);
    throw error;
  }
}

// 刷新token的函数
async function refreshAccessToken() {
  try {
    const response = await fetch('/api/auth/refresh', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${getRefreshToken()}`
      }
    });
    
    if (response.ok) {
      const data = await response.json();
      saveAccessToken(data.access_token);
      return data.access_token;
    }
    
    return null;
  } catch (error) {
    console.error('刷新token失败:', error);
    return null;
  }
}

// 重定向到登录页
function redirectToLogin() {
  // 清除本地存储的token
  clearTokens();
  // 跳转到登录页
  window.location.href = '/login';
}
```

## API接口说明

### 登录接口

```http
POST /api/auth/login
Content-Type: application/json

{
  "username": "string",
  "password": "string"
}
```

响应：

```json
{
  "code": 200,
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIs...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIs...",
    "expires_in": 900
  }
}
```

### 刷新Token接口

```http
POST /api/auth/refresh
Authorization: Bearer <refresh_token>
```

响应：

```json
{
  "code": 200,
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIs...",
    "expires_in": 900
  }
}
```

## 配置说明

系统支持以下配置项：

### Token过期时间配置

```php
// config/token.php
return [
    // Token过期时间配置（秒）
    'expire_time' => [
        'access_token' => (int)env('TOKEN.ACCESS_TOKEN_EXPIRE', 2 * 24 * 60 * 60),  // 默认48小时
        'refresh_token' => (int)env('TOKEN.REFRESH_TOKEN_EXPIRE', 7 * 24 * 60 * 60), // 默认7天
    ],
];
```

### Token风格配置

系统支持三种不同的Token生成风格：MD5、UUID和JWT。可以通过配置文件选择使用哪种风格：

```php
// config/token.php
return [
    // Token风格配置，支持md5、uuid、jwt三种方式
    'token_style' => env('TOKEN.STYLE', 'md5'),
    
    // 各种Token风格的配置
    'styles' => [
        // MD5风格配置（默认）
        'md5' => [
            // 是否添加随机数前缀
            'use_random_prefix' => true,
        ],

        // UUID风格配置
        'uuid' => [
            // UUID版本，支持1-5
            'version' => 4,
        ],

        // JWT风格配置
        'jwt' => [
            // JWT签名算法
            'algorithm' => env('TOKEN.JWT_ALG', 'HS256'),
            // 签发者
            'issuer' => env('TOKEN.JWT_ISSUER', 'helio'),
            // 接收者
            'audience' => env('TOKEN.JWT_AUDIENCE', 'user'),
            // 是否在payload中包含用户IP
            'include_ip' => env('TOKEN.JWT_INCLUDE_IP', false),
            // 是否在payload中包含设备信息
            'include_device' => env('TOKEN.JWT_INCLUDE_DEVICE', false),
        ],
    ],
    
    // JWT密钥配置
    'jwt_key' => env('TOKEN.JWT_KEY', 'a666a7aa-0c4c-4602-5f8c-82cc9e18c9ca'),
];
```

## Token风格详解

系统支持三种不同的Token生成风格，每种风格有其特点和适用场景：

### 1. MD5风格（默认）

**特点：**
- 基于MD5哈希算法生成32位的字符串
- 可配置是否添加随机数前缀增强唯一性
- 生成速度快，占用空间小

**配置参数：**
- `use_random_prefix`：是否添加随机数前缀，默认为true

**适用场景：**
- 对token格式没有特殊要求的一般应用
- 追求高性能的场景
- 不需要在token中包含额外信息的场景

### 2. UUID风格

**特点：**
- 生成符合UUID标准的字符串
- 支持不同版本的UUID（v1基于时间，v4完全随机）
- 全球唯一性更好，碰撞概率极低

**配置参数：**
- `version`：UUID版本，支持1-5，默认为4

**适用场景：**
- 需要全球唯一标识符的场景
- 分布式系统中避免ID冲突
- 对token格式有标准要求的场景

### 3. JWT风格

**特点：**
- 基于JSON Web Token标准
- 可在token中包含用户信息和元数据
- 支持签名验证，提供更高的安全性
- 客户端可以解析token内容（但不能篡改）

**配置参数：**
- `algorithm`：签名算法，默认为HS256
- `issuer`：签发者，默认为'helio'
- `audience`：接收者，默认为'user'
- `include_ip`：是否在payload中包含用户IP，默认为false
- `include_device`：是否在payload中包含设备信息，默认为false

**适用场景：**
- 需要在token中包含用户信息的场景
- 对安全性要求较高的应用
- 需要跨服务验证身份的分布式系统
- 无状态认证的RESTful API

### 如何切换Token风格

可以通过修改配置文件或环境变量来切换Token风格：

1. **修改配置文件**：在`app/tenant/config/token.php`中修改`token_style`的值

2. **通过环境变量**：在`.env`文件中设置`TOKEN.STYLE`变量
   ```
   TOKEN.STYLE=jwt
   ```

3. **动态切换**：也可以在运行时通过修改配置来切换风格（不推荐）
   ```php
   // 临时切换为JWT风格
   config(['token.token_style' => 'jwt']);
   ```

切换Token风格后，新生成的token将使用新的风格，但不会影响已经存在的token。如果需要强制所有用户使用新风格的token，可以通过清除token缓存或设置token过期时间来实现。
```

## 最佳实践

1. Token管理
   - 妥善保管refresh_token
   - 定期清理过期的token记录
   - 实现token黑名单机制

2. 安全建议
   - 使用HTTPS传输
   - 设置合理的token有效期
   - 实现登录失败次数限制
   - 记录重要的认证操作日志

3. 客户端实现
   - 实现token自动刷新机制
   - 统一处理401错误
   - 注意并发请求的token刷新问题