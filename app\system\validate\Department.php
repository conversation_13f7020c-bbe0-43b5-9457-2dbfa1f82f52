<?php
namespace app\system\validate;

use app\system\repository\DepartmentRepository;

/**
 * 部门验证器类
 * <AUTHOR> <<EMAIL>>
 * @package app\system\validate
 */
class Department extends BaseValidate
{
    protected $rule = [
        'id'     => 'require|integer|checkDepartmentInfo',
        'pid'    => 'integer|egt:0',
        'name'   => 'require|string|max:50|checkNameExist',
        'code'   => 'alphaNum|max:30|unique:system_department',
        'sort'   => 'integer|between:-1000,1000',
        'status' => 'require|in:0,1',
        'remark' => 'max:255',
    ];

    protected $message = [
        'id.require'     => 'ID不能为空',
        'id.integer'     => 'ID必须为整数',
        'pid.integer'    => '父部门ID必须为整数',
        'pid.egt'        => '父部门ID不能为负数',
        'name.require'   => '部门名称不能为空',
        'name.string'    => '部门名称必须为字符串',
        'name.max'       => '部门名称最多不能超过50个字符',
        'code.alphaNum'  => '部门编码只能是字母和数字',
        'code.max'       => '部门编码最多不能超过30个字符',
        'code.unique'    => '部门编码已存在',
        'sort.integer'   => '排序号必须为整数',
        'sort.between'   => '排序号只能是-1000到1000之间的整数值',
        'status.require' => '状态不能为空',
        'status.in'      => '状态值错误',
        'remark.max'     => '备注最多不能超过255个字符',
    ];

    protected $scene = [
        'add'    => ['pid','name','code','sort','status','remark'],
        'edit'   => ['id','pid','name','code','sort','status','remark'],
        'sort'   => ['id', 'sort'],
        'status' => ['id', 'status'],
        'delete' => ['id'],
    ];

    /**
     * 检查部门名称是否已存在
     * @param mixed $value 字段值
     * @param mixed $rule 验证规则
     * @param array $data 全部数据
     * @return bool|string
     */
    protected function checkNameExist(mixed $value, mixed $rule, array $data): bool|string
    {
        $departmentRepository = new DepartmentRepository();

        $id = isset($data['id']) ? intval($data['id']) : 0;

        if ($departmentRepository->checkExist('name', $value, $id)) {
            return '部门名称已存在';
        }

        return true;
    }

    /**
     * 检查部门信息是否正确
     * @param mixed $value 字段值
     * @param mixed $rule 验证规则
     * @param array $data 全部数据
     * @return bool|string
     */
    protected function checkDepartmentInfo(mixed $value, mixed $rule, array $data): bool|string
    {
        $departmentRepository = new DepartmentRepository();

        $info = $departmentRepository->getInfo($value);

        if (empty($info)) {
            return '部门不存在';
        }

        if ($info['status'] == -1) {
            return '该部门已删除，无法操作';
        }

        return true;
    }
}
