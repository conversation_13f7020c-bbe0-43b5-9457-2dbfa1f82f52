<?php

/**
 * php /www/wwwroot/helioCloud-backend/app/tenant/config/generate_keys.php
 */

// 配置RSA密钥生成参数
$config = [
    'private_key_bits' => 2048,
    'private_key_type' => OPENSSL_KEYTYPE_RSA,
];

// 生成新的密钥对
$res = openssl_pkey_new($config);

if ($res === false) {
    die("Failed to generate RSA key pair\n");
}

// 导出私钥
openssl_pkey_export($res, $privKey);
file_put_contents(__DIR__ . '/private_key.pem', $privKey);

// 导出公钥
$pubKey = openssl_pkey_get_details($res)['key'];
file_put_contents(__DIR__ . '/public_key.pem', $pubKey);

echo "RSA key pair generated successfully\n";
