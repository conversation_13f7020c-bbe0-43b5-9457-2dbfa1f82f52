<?php
declare(strict_types = 1);

namespace app\tenant\service\login;

use app\tenant\model\User as UserModel;
use app\tenant\repository\UserRepository;

/**
 * 手机验证码登录策略类
 * 实现了基于手机号和验证码的登录验证
 * 包含以下主要功能：
 * - 验证用户提供的手机号和验证码
 * - 支持租户隔离的用户验证
 * - 集成短信验证码验证服务
 * <AUTHOR> <<EMAIL>>
 * @package app\tenant\service\login
 */
class MobileLoginStrategy implements LoginStrategyInterface
{
    /**
     * 执行手机验证码登录验证
     * @param array $credentials 登录凭证，需要包含tenant_id、mobile和code字段
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return UserModel|null
     */
    public function login(array $credentials): ?UserModel
    {
        if (!isset($credentials['tenant_id']) || !isset($credentials['mobile']) || !isset($credentials['code'])) {
            return null;
        }

        // 验证手机验证码
        if (!$this->verifyCode($credentials['mobile'], $credentials['code'])) {
            return null;
        }

        // 获取用户信息
        $user = (new UserRepository())->findByMobile($credentials['mobile'], $credentials['tenant_id']);

        if (!$user) {
            return null;
        }

        return $user;
    }

    /**
     * 验证手机验证码
     * @param string $mobile 手机号
     * @param string $code 验证码
     * @return bool
     */
    protected function verifyCode(string $mobile, string $code): bool
    {
        // TODO: 实现验证码验证逻辑
        // 这里需要根据实际的验证码存储和验证方式来实现
        // 可以使用缓存或数据库来存储和验证验证码
        return true;
    }

    /**
     * 获取登录类型
     * @return string
     */
    public function getType(): string
    {
        return 'mobile';
    }
}
