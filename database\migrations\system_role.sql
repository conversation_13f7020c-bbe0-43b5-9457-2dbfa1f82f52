-- 表结构: system_role
CREATE TABLE `system_role` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(50) NOT NULL DEFAULT '' COMMENT '角色名称',
  `alias` varchar(100) NOT NULL DEFAULT '' COMMENT '角色组别名',
  `is_system` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否系统自带（不可删除）',
  `visible_access` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '数据权限（0:全部可见,1:本人可见,2:所在部门可见,3:所在部门及子部门可见,4:选择的部门可见,5:自定义）',
  `visible_scope` varchar(500) NOT NULL DEFAULT '' COMMENT '数据可见范围（在visible_access为4和5时有效）',
  `visible_rule` varchar(255) NOT NULL DEFAULT '' COMMENT '数据可见范围（自定义规则）',
  `dashboard` enum('default','work','stats') NOT NULL DEFAULT 'default' COMMENT '控制台视图（default :默认,work:工作台,stats:数据统计）',
  `create_at` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_at` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  `description` varchar(255) NOT NULL DEFAULT '' COMMENT '备注信息',
  `sort` smallint(4) NOT NULL DEFAULT '0' COMMENT '排序号',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '角色状态 1为有效 0为失效',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='系统角色表';
