<?php
declare(strict_types = 1);

namespace app\system\controller;

use app\system\constant\AccountConstant;
use app\system\model\Permission as PermissionModel;
use app\system\model\PermissionRole as PermissionRoleModel;
use app\system\repository\OperationLogRepository;
use app\system\repository\RoleUserRepository;
use app\system\repository\UserRepository;
use app\system\service\AccountService;
use app\system\service\AuthService;
use app\system\service\MenuService;
use app\system\service\OnlineUserService;
use app\system\service\TokenService;
use app\system\validate\User as UserValidate;
use think\App;
use think\exception\ValidateException;
use think\Response;

/**
 * 用户账号控制器
 * <AUTHOR> <<EMAIL>>
 * @package app\system\controller
 */
class Account extends BaseController
{
    protected UserRepository $repository;

    protected RoleUserRepository $roleUserRepository;

    public function __construct(App $app)
    {
        parent::__construct($app);

        // 指定对应仓储类
        $this->repository         = new UserRepository();
        $this->roleUserRepository = new RoleUserRepository();
    }

    // 建议将登录类型定义为常量
    private const LOGIN_TYPES = [
        'phone'   => 'phone',
        'account' => 'account',
        'mobile'  => 'mobile',
        'email'   => 'email',
    ];

    /**
     * 验证用户是否登录（也可用作获取用户基本信息）
     * @return \think\Response
     */
    public function info(): Response
    {
        $userInfo = $this->getUserInfo();

        if ($userInfo) {
            return $this->success($userInfo);
        }

        return $this->error('failed');
    }

    /**
     * 登录接口
     * @throws \Psr\SimpleCache\InvalidArgumentException
     * @throws \Random\RandomException
     * @throws \UAParser\Exception\FileNotFoundException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return \think\Response
     */
    public function login(): Response
    {
        $login_type = $this->request->param('type');

        if (!isset(self::LOGIN_TYPES[$login_type])) {
            return $this->error('无效的登录方式');
        }

        switch ($login_type) {
            // 手机号验证登录
            case 'phone':
                $account = $this->request->param('phone'); // 手机号
                $code    = $this->request->param('code');  // 验证码

                break;
                // 账号密码验证登录
            case 'account':
                // 手机号密码验证登录
            case 'mobile':
                // 手机号密码验证登录
            case 'email':
                $account = $this->request->param('account'); // 用户名
                $code    = $this->request->param('password'); // 密码

                break;
            default:
                $account = null;
                $code    = null;

                return $this->error('登录方式[type]错误');
        }

        // 登录统一验证方法
        $result = AccountService::doLogin($login_type, $account, $code);

        if ($result['code'] !== AccountConstant::ACCOUNT_LOGIN_SUCCESS['code']) {
            return $this->error($result['message'], $result['code']);
        }

        return $this->success($result['data']);
    }

    /**
     * 退出登录接口
     * @return \think\Response
     */
    public function logout(): Response
    {
        $accessToken = TokenService::getBearerToken();
        $result      = AuthService::getUserInfoByAccessTokenSimple($accessToken);
        $userInfo    = !empty($result['userInfo']) ? $result['userInfo'] : $this->getUserInfo();

        if (!empty($result['userId'])) {
            // 退出登录统一方法
            AccountService::logout($result['userId']);
            // 写入事件日志
            $this->event_log('退出登录', [$result['userId']], $userInfo);
        }

        return $this->success('退出成功');
    }

    /**
     * 锁屏
     * @return \think\Response
     */
    public function lockscreen(): Response
    {
        // 锁屏方式：【0:自动超时锁屏，1:手动锁屏】
        $lock_type = $this->request->param('type', 0);

        $lock_type_arr = [
            0 => '超时锁屏',
            1 => '手动锁屏',
        ];

        if (!isset($lock_type_arr[$lock_type])) {
            return $this->error('type只能为0或1');
        }

        if (empty($this->getUserId())) {
            return $this->error('用户参数错误');
        }

        // 已经是锁定状态，无需重新操作
        if (!empty($this->getUserInfo()['lockscreen'])) {
            return $this->success();
        }

        // 更新缓存里的用户状态为锁定
        $res = AuthService::updateCacheUserInfo($this->getUserId(), ['lockscreen' => 1]);

        if ($res) {
            // 写入事件日志
            $this->event_log('用户' . $lock_type_arr[$lock_type], $this->request->param(), []);

            return $this->success();
        }

        return $this->error('锁屏失败');
    }

    /**
     * 解锁屏幕
     * @return \think\Response
     */
    public function unLockscreen(): Response
    {
        $params = $this->request->param();

        if (empty($params['password'])) {
            return $this->error('解锁密码不能为空');
        }

        if (empty($this->getUserId())) {
            return $this->error('用户参数错误');
        }

        // 查询用户表中的原始信息
        $userinfo = $this->repository->getInfo($this->getUserId());

        // 验证密码是否正确
        if (AuthService::generatePassword($params['password'], $userinfo['salt']) != $userinfo['password']) {
            return $this->error('密码验证失败');
        }

        // 更新缓存里的用户状态为解除锁定
        $res = AuthService::updateCacheUserInfo($this->getUserId(), ['lockscreen' => 0]);

        if ($res) {
            // 写入事件日志
            $this->event_log('用户解锁屏幕', $params, []);

            return $this->success();
        }

        return $this->error('解锁失败');
    }

    /**
     * 接收心跳
     * @throws \Psr\SimpleCache\InvalidArgumentException
     * @return \think\Response
     */
    public function ping(): Response
    {
        if (OnlineUserService::updateHeartbeat($this->getUserId())) {
            return $this->success();
        }

        return $this->error();
    }

    /**
     * 更新用户信息
     * @return \think\Response
     */
    public function edit(): Response
    {
        if (empty($this->getUserId())) {
            return $this->error('用户参数错误');
        }

        $params = $this->request->param();

        try {
            validate(UserValidate::class)->scene('profile')->check($params);
        } catch (ValidateException $e) {
            // 验证失败 输出错误信息
            return $this->error($e->getError());
        }

        // 检测手机号是否存在
        if (!empty($params['phone'])) {
            $phoneExist = $this->repository->checkExist('phone', $params['phone'], $this->getUserId());

            if ($phoneExist) {
                return $this->error('手机号已存在');
            }
        }

        // 检测邮箱是否存在
        if (!empty($params['email'])) {
            $emailExist = $this->repository->checkExist('email', $params['email'], $this->getUserId());

            if ($emailExist) {
                return $this->error('邮箱号已存在');
            }
        }

        $data = [
            'realname'  => trim($params['realname']),
            'phone'     => trim($params['phone']),
            'email'     => trim($params['email']),
            'gender'    => intval($params['gender']),
            'birthday'  => date('Y-m-d', strtotime($params['birthday'] . ' 00:01:01')),
            'signature' => trim($params['signature']),
            'update_at' => time(),
        ];

        // 更新用户信息
        $res = $this->repository->updateData($this->getUserId(), $data);

        if ($res) {
            // 写入事件日志
            $this->event_log('更新用户信息', $params, []);

            return $this->success('更新成功');
        }

        return $this->error('更新失败');
    }

    /**
     * 修改密码
     * @return \think\Response
     */
    public function changePassword(): Response
    {
        $params = $this->request->param();

        if (empty($params['password'])) {
            return $this->error('新密码不能为空');
        }

        if (empty($this->getUserId())) {
            return $this->error('用户参数错误');
        }

        if ($params['password'] != $params['confirm_password']) {
            return $this->error('两次密码不一致');
        }

        // force_change_password为1时是强制修改密码，否则为用户重置密码
        if ($this->getUserInfo()['force_change_password'] == 0) {
            if (empty($params['old_password'])) {
                return $this->error('原密码不能为空');
            }

            // 查询用户表中的原始信息
            $userinfo = $this->repository->getInfo($this->getUserId());

            // 验证原密码是否正确
            if (AuthService::generatePassword($params['old_password'], $userinfo['salt']) != $userinfo['password']) {
                return $this->error('原密码验证失败');
            }
        }

        // 更新缓存中的该字段
        $params['force_change_password'] = 0;

        // 更新用户密码
        $res = $this->repository->resetUserPassword($this->getUserId(), $params);

        if ($res) {
            // 写入事件日志
            $this->event_log('用户修改密码', $params, []);

            return $this->success('重置成功');
        }

        return $this->error('重置失败');
    }

    /**
     * 验证token合法性
     * @return \think\Response
     */
    public function verifyToken(): Response
    {
        $token = $this->request->param('access_token');

        if (empty($token)) {
            $token = $this->request->param('refresh_token');
        }

        if (!empty($token)) {
            // 验证用户token是否合法、过期并返回携带数据
            $token_info = TokenService::verifyToken($token);

            if (!empty($token_info['code'])) {
                return $this->error($token_info['message'], $token_info['code']);
            }

            return $this->success($token_info);
        }

        return $this->error('参数access_token或refresh_token不能为空');
    }

    /**
     * 全局刷新access_token方法（refresh_token过期后只能重新登录）
     * @throws \Psr\SimpleCache\InvalidArgumentException
     * @throws \Random\RandomException
     * @throws \UAParser\Exception\FileNotFoundException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return \think\Response
     */
    public function refreshToken(): Response
    {
        $refresh_token = $this->request->param('refresh_token');

        if (!empty($refresh_token)) {
            $result = AccountService::doLogin('refresh_token', null, $refresh_token);

            if ($result['code'] !== AccountConstant::ACCOUNT_LOGIN_SUCCESS['code']) {
                return $this->error($result['message'], $result['code']);
            }

            // 写入事件日志
            $this->event_log('刷新token', $this->request->param(), []);

            return $this->success($result['data']);
        }

        return $this->error('参数refresh_token不能为空');
    }

    /**
     * 获取用户个人菜单
     * @param array $userInfo
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return \think\Response
     */
    public function menu(array $userInfo = []): Response
    {
        $userInfo = $userInfo ?: $this->getUserInfo();

        if ($userInfo) {
            $list = [];

            // 超管时获取全部菜单
            if ($userInfo['is_super'] == 1) {
                // 获取所有状态正常的菜单列表
                $list = PermissionModel::getRulelist();
            } else {
                // 获取用户对应的角色组（联合一次查询用户组信息，禁用的不查询）
                $userRoles = $this->roleUserRepository->getUserRoles($userInfo['id']);

                $userRolesArr = $userRoles ? array_column($userRoles, 'id') : [];

                if ($userRolesArr) {
                    // 获取用户组对应的所有节点权限列表
                    $list = PermissionRoleModel::where('role_id', 'IN', $userRolesArr)->select()->toArray();
                }
            }

            // 菜单标准输出
            $data['menu_data']       = $list ? MenuService::format($list) : [];
            $data['permission_data'] = $list ? MenuService::permissions($list) : [];

            return $this->success($data);
        }

        return $this->error('请登录后获取');
    }

    /**
     * 个人操作日志列表
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return \think\Response
     */
    public function logs(): Response
    {
        $params = $this->request->param();

        $result = (new OperationLogRepository())->getList($this->getUserInfo(), $params);

        return $this->success($result);
    }
}
