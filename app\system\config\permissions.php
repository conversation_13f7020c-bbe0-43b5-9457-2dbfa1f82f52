<?php

/**
 * 公共权限节点配置管理
 * <AUTHOR> <<EMAIL>>
 * @package app\system\config
 */
return [
    // 完全不需要鉴权的节点
    'NO_AUTH_PERMISSION_LIST' => [
        '/system/common/info',         // 系统信息数据
        '/system/common/sendCode',     // 发送验证码
        '/system/account/login',       // 登录页
        '/system/account/logout',      // 退出登录
        '/system/account/verifyToken', // 验证token合法性
        '/system/account/refreshToken', // 刷新token
    ],

    // 登录后默认公共可访问（需登录验证token）的节点
    'PUBLIC_PERMISSION_LIST' => [
        'common' => [
            '/system/common/monitor', // 服务监控
        ],
        'account' => [
            '/system/account/menu', // 菜单节点路由
            '/system/account/info', // 用户信息获取
            '/system/account/edit', // 个人中心编辑资料
            '/system/account/logs', // 日志列表
        ],
        'notification' => [
            '/system/notification/*',  // 所有通知相关接口
        ],
    ],

    // 特殊权限节点（锁屏/修改密码等，同时也是公共节点，在AuthService中有处理）
    'SPECIAL_PERMISSION_LIST' => [
        'LOCKSCREEN'      => '/system/account/lockscreen',     // 锁屏
        'UNLOCKSCREEN'    => '/system/account/unLockscreen',   // 解锁屏幕
        'CHANGE_PASSWORD' => '/system/account/changePassword', // 修改密码
        'CHECK_HEARTBEAT' => '/system/account/ping',           // 心跳检测
    ],
];
