<?php
declare(strict_types = 1);

namespace app\tenant\validate;

use think\Validate;

/**
 * 租户角色验证器
 * <AUTHOR> <<EMAIL>>
 * @package app\tenant\validate
 */
class Role extends Validate
{
    /**
     * 验证规则
     * @var array
     */
    protected $rule = [
        'id'          => 'require|number',
        'name'        => 'require|max:50|unique:tenant_role,name^tenant_id',
        'code'        => 'require|max:50|alphaNum|unique:tenant_role,code^tenant_id',
        'description' => 'max:255',
        'sort'        => 'number',
        'status'      => 'require|in:0,1,-1',
    ];

    /**
     * 错误信息
     * @var array
     */
    protected $message = [
        'id.require'      => 'ID不能为空',
        'id.number'       => 'ID不能为空',
        'name.require'    => '角色名称不能为空',
        'name.max'        => '角色名称不能超过50个字符',
        'name.unique'     => '角色名称已经存在',
        'code.require'    => '角色标识不能为空',
        'code.max'        => '角色标识不能超过50个字符',
        'code.alphaNum'   => '角色标识只能是字母和数字',
        'code.unique'     => '角色标识已经存在',
        'description.max' => '角色描述不能超过255个字符',
        'status.require'  => '状态不能为空',
        'status.in'       => '状态值不正确',
        'sort.number'     => '排序必须为数字',
    ];

    /**
     * 验证场景
     * @var array
     */
    protected $scene = [
        'add'  => ['name', 'code', 'description', 'sort'],
        'edit' => ['id', 'name', 'code', 'description', 'sort'],
    ];
}
