<?php
declare(strict_types = 1);

/**
 * MQTT服务配置
 * 基于emqx的MQTT服务配置
 */
return [
    'debug'            => env('APP_DEBUG', false), // 是否调试模式
    'host'             => env('MQTT.MQTT_HOST', '127.0.0.1'), // MQTT服务器地址
    'port'             => env('MQTT.MQTT_PORT', 1883), // MQTT服务器端口
    'username'         => env('MQTT.MQTT_USERNAME', ''), // MQTT认证用户名
    'password'         => env('MQTT.MQTT_PASSWORD', ''), // MQTT认证密码
    'client_id_prefix' => env('MQTT.MQTT_CLIENT_ID_PREFIX', 'helio_'), // 客户端ID前缀
    'keepalive'        => env('MQTT.MQTT_KEEPALIVE', 0), // 保活时间（秒），0时为禁止
    'connect_timeout'  => env('MQTT.MQTT_CONNECT_TIMEOUT', 1.0), // 连接超时时间（秒）
    'write_timeout'    => env('MQTT.MQTT_WRITE_TIMEOUT', 3.0), // 写超时时间（秒）
    'read_timeout'     => env('MQTT.MQTT_READ_TIMEOUT', 0.5), // 读超时时间（秒）
    'reconnect_delay'  => env('MQTT.MQTT_RECONNECT_DELAY', 5), // 重连延迟时间（秒）
    'topic_prefix'     => env('MQTT.MQTT_TOPIC_PREFIX', 'helio/'), // 主题前缀
    'qos'              => env('MQTT.MQTT_QOS', 0), // 默认QoS级别
    'retain'           => env('MQTT.MQTT_RETAIN', false), // 默认是否保留消息
    'clean_session'    => env('MQTT.MQTT_CLEAN_SESSION', true), // 是否清理会话
    'protocolName'     => 'MQTT', // 协议名，默认为MQTT(3.1.1版本)，也可为MQIsdp(3.1版本)
    'protocol_version' => env('MQTT.MQTT_PROTOCOL_VERSION', 5), // MQTT协议版本，MQTT3.1.1版本为4，5.0版本为5，MQIsdp为3
    'topic_patterns'   => [ // 主题模式定义[C:客户端订阅主题，S:服务端订阅主题]
        'client_status'    => '{prefix}tenant/{tenant_code}/{user_id}/status', // 订阅的客户端状态主题（包括云端、客户端上下线、心跳）
        'device_trace'     => '{prefix}tenant/{tenant_code}/device/{device_id}/trace', // 订阅的设备状态探测主题
        'device_heartbeat' => '{prefix}tenant/{tenant_code}/device/{device_id}/heartbeat', // 设备心跳监控主题
        'device_control'   => '{prefix}tenant/{tenant_code}/device/{device_id}/control', // C:设备控制指令主题
        'device_data'      => '{prefix}tenant/{tenant_code}/device/{device_id}/data', // S:设备数据传输主题
    ],
];
