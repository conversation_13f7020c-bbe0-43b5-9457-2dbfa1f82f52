<?php
namespace app\tenant\validate;

/**
 * 租户验证器类
 * <AUTHOR> <<EMAIL>>
 * @package app\tenant\validate
 */
class Tenant extends BaseValidate
{
    protected $rule = [
        'id'             => 'require',
        'name'           => 'require|max:50|unique:tenant,name',
        'code'           => 'require|alphaNum|max:30|unique:tenant,code',
        'contact_person' => 'max:20',
        'contact_phone'  => 'mobile',
        'expire_time'    => 'number|min:0',
        'max_users'      => 'integer|egt:0',
        'description'    => 'max:255',
        'sort'           => 'between:-1000,1000',
        'status'         => 'in:-1,0,1',
    ];

    protected $message = [
        'id.require'         => 'ID不能为空',
        'name.require'       => '租户名称不能为空',
        'name.max'           => '租户名称最多不能超过50个字符',
        'name.unique'        => '租户名称已存在',
        'code.require'       => '租户编码不能为空',
        'code.alphaNum'      => '租户编码只能是字母、数字',
        'code.max'           => '租户编码最多不能超过30个字符',
        'code.unique'        => '租户编码已存在',
        'contact_person.max' => '联系人最多不能超过20个字符',
        'contact_phone'      => '手机号码格式不正确',
        'expire_time.number' => '到期时间必须为数字',
        'expire_time.min'    => '到期时间不能小于0',
        'max_users.integer'  => '最大用户数必须为整数',
        'max_users.egt'      => '最大用户数不能小于0',
        'description.max'    => '描述最多不能超过255个字符',
        'sort.between'       => '排序号只能是-1000到1000之间的整数值',
        'status.in'          => '状态值错误',
    ];

    protected $scene = [
        'add'    => ['name', 'code', 'contact_person', 'contact_phone', 'expire_time', 'max_users', 'description', 'sort'],
        'edit'   => ['id', 'name', 'code', 'contact_person', 'contact_phone', 'expire_time', 'max_users', 'description', 'sort'],
        'sort'   => ['id', 'sort'],
        'status' => ['id', 'status'],
    ];
}
