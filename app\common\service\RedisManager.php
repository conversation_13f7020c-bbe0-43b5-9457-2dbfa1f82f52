<?php
declare(strict_types = 1);

namespace app\common\service;

use Swoole\Coroutine;
use think\facade\Cache;
use think\facade\Log;

/**
 * Redis连接管理器
 * 提供简洁的Redis操作接口，自动管理连接池
 * <AUTHOR> <<EMAIL>>
 * @package app\common\service
 */
class RedisManager
{
    /**
     * 单例实例
     * @var ?RedisManager
     */
    private static ?RedisManager $instance = null;

    /**
     * Redis连接池
     * @var ?RedisPool
     */
    private ?RedisPool $pool = null;

    /**
     * 构造函数
     */
    private function __construct()
    {
        // 在协程环境中初始化连接池
        if (class_exists('\Swoole\Coroutine') && Coroutine::getCid() !== -1) {
            $poolSize = min(50, max(10, swoole_cpu_num() * 5));
            $this->pool = RedisPool::getInstance($poolSize);
        }
    }

    /**
     * 获取单例实例
     * @return self
     */
    public static function getInstance(): self
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }

        return self::$instance;
    }

    /**
     * 执行Redis操作
     * 自动管理连接的获取和归还
     * @param callable $callback Redis操作回调函数
     * @param float $timeout 获取连接超时时间
     * @throws \Exception
     * @return mixed 操作结果
     */
    public function execute(callable $callback, float $timeout = 3.0): mixed
    {
        $redis = null;

        try {
            $redis = $this->getConnection($timeout);

            if (!$redis) {
                throw new \Exception('无法获取Redis连接');
            }

            return $callback($redis);
        } finally {
            if ($redis) {
                $this->releaseConnection($redis);
            }
        }
    }

    /**
     * 执行Redis事务操作
     * @param callable $callback 事务操作回调函数
     * @param float $timeout 获取连接超时时间
     * @throws \Exception
     * @return mixed 事务执行结果
     * @return mixed
     */
    public function transaction(callable $callback, float $timeout = 3.0): mixed
    {
        return $this->execute(function($redis) use ($callback) {
            $redis->multi();

            try {
                $callback($redis);
                return $redis->exec();
            } catch (\Throwable $e) {
                $redis->discard();
                throw $e;
            }
        }, $timeout);
    }

    /**
     * 执行Redis管道操作
     * @param callable $callback 管道操作回调函数
     * @param float $timeout  获取连接超时时间
     * @return mixed 管道执行结果
     * @throws \Exception
     * @return mixed
     */
    public function pipeline(callable $callback, float $timeout = 3.0): mixed
    {
        return $this->execute(function($redis) use ($callback) {
            $pipeline = $redis->pipeline();
            $callback($pipeline);
            return $pipeline->exec();
        }, $timeout);
    }

    /**
     * 获取Redis连接
     * @param float $timeout 超时时间
     * @return ?Object
     */
    private function getConnection(float $timeout = 3.0): ?Object
    {
        // 在协程环境中使用连接池
        if ($this->pool && class_exists('\Swoole\Coroutine') && Coroutine::getCid() !== -1) {
            return $this->pool->get($timeout);
        }

        // 非协程环境，使用ThinkPHP的连接
        return Cache::store('redis')->handler();
    }

    /**
     * 释放Redis连接
     * @param \Redis $redis
     */
    private function releaseConnection(\Redis $redis): void
    {
        // 只有在协程环境中且使用连接池时才需要归还连接
        if ($this->pool && class_exists('\Swoole\Coroutine') && Coroutine::getCid() !== -1) {
            try {
                $this->pool->put($redis);
            } catch (\Throwable $e) {
                Log::debug('归还Redis连接异常: ' . $e->getMessage());
            }
        }
        // 非协程环境的连接由ThinkPHP自动管理，无需手动释放
    }

    /**
     * 关闭连接池
     */
    public function close(): void
    {
        if ($this->pool) {
            $this->pool->close();
            $this->pool = null;
        }
    }

    /**
     * 获取连接池状态
     * @return array
     */
    public function getStats(): array
    {
        if ($this->pool) {
            return $this->pool->stats();
        }

        return [
            'pool_size' => 0,
            'current_count' => 0,
            'available' => 0,
            'waiting' => 0,
            'connection_times' => 0,
            'active_connections' => 0,
        ];
    }

    /**
     * 便捷方法：设置键值
     * @param string $key
     * @param mixed $value
     * @param int $expire
     * @throws \Exception
     * @return bool
     */
    public function set(string $key, mixed $value, int $expire = 0): bool
    {
        return $this->execute(function($redis) use ($key, $value, $expire) {
            if ($expire > 0) {
                return $redis->setex($key, $expire, $value);
            }
            return $redis->set($key, $value);
        });
    }

    /**
     * 便捷方法：获取键值
     * @param string $key
     * @throws \Exception
     * @return mixed
     */
    public function get(string $key): mixed
    {
        return $this->execute(function($redis) use ($key) {
            return $redis->get($key);
        });
    }

    /**
     * 便捷方法：删除键
     * @param  array|string $key
     * @throws \Exception
     * @return int
     */
    public function del(array|string $key): int
    {
        return $this->execute(function($redis) use ($key) {
            return $redis->del($key);
        });
    }

    /**
     * 便捷方法：检查键是否存在
     * @param string $key
     * @throws \Exception
     * @return bool
     */
    public function exists(string $key): bool
    {
        return $this->execute(function($redis) use ($key) {
            return $redis->exists($key) > 0;
        });
    }

    /**
     * 便捷方法：设置过期时间
     * @param string $key
     * @param int $seconds
     * @throws \Exception
     * @return bool
     */
    public function expire(string $key, int $seconds): bool
    {
        return $this->execute(function($redis) use ($key, $seconds) {
            return $redis->expire($key, $seconds);
        });
    }

    /**
     * 便捷方法：哈希表操作
     * @param string $key
     * @param string $field
     * @param mixed $value
     * @throws \Exception
     * @return bool
     */
    public function hSet(string $key, string $field, mixed $value): bool
    {
        return $this->execute(function($redis) use ($key, $field, $value) {
            return $redis->hSet($key, $field, $value);
        });
    }

    /**
     * 便捷方法：获取哈希表字段值
     * @param string $key
     * @param string $field
     * @throws \Exception
     * @return mixed
     */
    public function hGet(string $key, string $field)
    {
        return $this->execute(function($redis) use ($key, $field) {
            return $redis->hGet($key, $field);
        });
    }

    /**
     * 便捷方法：获取哈希表所有字段
     * @param string $key
     * @throws \Exception
     * @return array
     */
    public function hGetAll(string $key): array
    {
        return $this->execute(function($redis) use ($key) {
            return $redis->hGetAll($key) ?: [];
        });
    }

    /**
     * 便捷方法：集合添加成员
     * @param string $key
     * @param mixed ...$values
     * @throws \Exception
     * @return int
     */
    public function sAdd(string $key, ...$values): int
    {
        return $this->execute(function($redis) use ($key, $values) {
            return $redis->sAdd($key, ...$values);
        });
    }

    /**
     * 便捷方法：获取集合所有成员
     * @param string $key
     * @throws \Exception
     * @return array
     */
    public function sMembers(string $key): array
    {
        return $this->execute(function($redis) use ($key) {
            return $redis->sMembers($key) ?: [];
        });
    }

    /**
     * 便捷方法：从集合中移除成员
     * @param string $key
     * @param mixed ...$values
     * @throws \Exception
     * @return int
     */
    public function sRem(string $key, ...$values): int
    {
        return $this->execute(function($redis) use ($key, $values) {
            return $redis->sRem($key, ...$values);
        });
    }
}
