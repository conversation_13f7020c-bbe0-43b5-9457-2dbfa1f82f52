<?php
declare(strict_types = 1);

namespace app\common\service;

use Swoole\Coroutine;
use think\facade\Cache;
use think\facade\Log;

/**
 * Redis连接管理器
 * 提供简洁的Redis操作接口，自动管理连接池
 * <AUTHOR> <<EMAIL>>
 * @package app\common\service
 */
class RedisManager
{
    /**
     * 单例实例
     * @var ?RedisManager
     */
    private static ?RedisManager $instance = null;

    /**
     * Redis连接池
     * @var ?RedisPool
     */
    private ?RedisPool $pool = null;

    /**
     * 是否启用监控
     * @var bool
     */
    private bool $enableMonitoring = true;

    /**
     * 操作统计
     * @var array
     */
    private array $stats = [
        'total_operations' => 0,
        'successful_operations' => 0,
        'failed_operations' => 0,
        'total_execution_time' => 0.0,
        'avg_execution_time' => 0.0,
        'connection_gets' => 0,
        'connection_puts' => 0,
        'connection_failures' => 0,
    ];

    /**
     * 构造函数
     */
    private function __construct()
    {
        // 在协程环境中初始化连接池
        if (class_exists('\Swoole\Coroutine') && Coroutine::getCid() !== -1) {
            $poolSize = min(50, max(10, swoole_cpu_num() * 5));
            $this->pool = RedisPool::getInstance($poolSize);
        }
    }

    /**
     * 获取单例实例
     * @return self
     */
    public static function getInstance(): self
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }

        return self::$instance;
    }

    /**
     * 执行Redis操作
     * 自动管理连接的获取和归还
     * @param callable $callback Redis操作回调函数
     * @param float $timeout 获取连接超时时间
     * @throws \Exception
     * @return mixed 操作结果
     */
    public function execute(callable $callback, float $timeout = 3.0): mixed
    {
        $startTime = microtime(true);
        $redis = null;
        $success = false;

        try {
            if ($this->enableMonitoring) {
                $this->stats['total_operations']++;
            }

            $redis = $this->getConnection($timeout);

            if (!$redis) {
                throw new \Exception('无法获取Redis连接');
            }

            $result = $callback($redis);
            $success = true;

            return $result;
        } catch (\Exception $e) {
            if ($this->enableMonitoring) {
                $this->stats['failed_operations']++;
                Log::warning('Redis操作失败: ' . $e->getMessage());
            }
            throw $e;
        } finally {
            if ($redis) {
                $this->releaseConnection($redis);
            }

            if ($this->enableMonitoring) {
                $executionTime = microtime(true) - $startTime;
                $this->updateExecutionStats($executionTime, $success);
            }
        }
    }

    /**
     * 执行Redis事务操作
     * @param callable $callback 事务操作回调函数
     * @param float $timeout 获取连接超时时间
     * @throws \Exception
     * @return mixed 事务执行结果
     * @return mixed
     */
    public function transaction(callable $callback, float $timeout = 3.0): mixed
    {
        return $this->execute(function($redis) use ($callback) {
            $redis->multi();

            try {
                $callback($redis);
                return $redis->exec();
            } catch (\Throwable $e) {
                $redis->discard();
                throw $e;
            }
        }, $timeout);
    }

    /**
     * 执行Redis管道操作
     * @param callable $callback 管道操作回调函数
     * @param float $timeout  获取连接超时时间
     * @return mixed 管道执行结果
     * @throws \Exception
     * @return mixed
     */
    public function pipeline(callable $callback, float $timeout = 3.0): mixed
    {
        return $this->execute(function($redis) use ($callback) {
            $pipeline = $redis->pipeline();
            $callback($pipeline);
            return $pipeline->exec();
        }, $timeout);
    }

    /**
     * 获取Redis连接
     * @param float $timeout 超时时间
     * @return ?Object
     */
    private function getConnection(float $timeout = 3.0): ?Object
    {
        try {
            if ($this->enableMonitoring) {
                $this->stats['connection_gets']++;
            }

            // 在协程环境中使用连接池
            if ($this->pool && class_exists('\Swoole\Coroutine') && Coroutine::getCid() !== -1) {
                $redis = $this->pool->get($timeout);

                if ($this->enableMonitoring && !$redis) {
                    $this->stats['connection_failures']++;
                    Log::warning('从连接池获取Redis连接失败');
                }

                return $redis;
            }

            // 非协程环境，使用ThinkPHP的连接
            return Cache::store('redis')->handler();
        } catch (\Exception $e) {
            if ($this->enableMonitoring) {
                $this->stats['connection_failures']++;
                Log::error('获取Redis连接异常: ' . $e->getMessage());
            }
            throw $e;
        }
    }

    /**
     * 释放Redis连接
     * @param \Redis $redis
     */
    private function releaseConnection(\Redis $redis): void
    {
        // 只有在协程环境中且使用连接池时才需要归还连接
        if ($this->pool && class_exists('\Swoole\Coroutine') && Coroutine::getCid() !== -1) {
            try {
                $this->pool->put($redis);

                if ($this->enableMonitoring) {
                    $this->stats['connection_puts']++;
                }
            } catch (\Throwable $e) {
                if ($this->enableMonitoring) {
                    $this->stats['connection_failures']++;
                }
                Log::debug('归还Redis连接异常: ' . $e->getMessage());
            }
        }
        // 非协程环境的连接由ThinkPHP自动管理，无需手动释放
    }

    /**
     * 关闭连接池
     */
    public function close(): void
    {
        if ($this->pool) {
            $this->pool->close();
            $this->pool = null;
        }
    }

    /**
     * 获取连接池状态
     * @return array
     */
    public function getStats(): array
    {
        $poolStats = [];

        if ($this->pool) {
            $poolStats = $this->pool->stats();
        } else {
            $poolStats = [
                'pool_size' => 0,
                'current_count' => 0,
                'available' => 0,
                'waiting' => 0,
                'connection_times' => 0,
                'active_connections' => 0,
            ];
        }

        // 合并连接池统计和操作统计
        return array_merge($poolStats, [
            'monitoring_enabled' => $this->enableMonitoring,
            'operation_stats' => $this->stats,
            'success_rate' => $this->calculateSuccessRate(),
            'avg_execution_time_ms' => round($this->stats['avg_execution_time'] * 1000, 2),
        ]);
    }

    /**
     * 更新执行统计
     * @param float $executionTime
     * @param bool $success
     */
    private function updateExecutionStats(float $executionTime, bool $success): void
    {
        if ($success) {
            $this->stats['successful_operations']++;
        }

        $this->stats['total_execution_time'] += $executionTime;

        // 计算平均执行时间
        if ($this->stats['total_operations'] > 0) {
            $this->stats['avg_execution_time'] = $this->stats['total_execution_time'] / $this->stats['total_operations'];
        }
    }

    /**
     * 计算成功率
     * @return float
     */
    private function calculateSuccessRate(): float
    {
        if ($this->stats['total_operations'] === 0) {
            return 0.0;
        }

        return round(($this->stats['successful_operations'] / $this->stats['total_operations']) * 100, 2);
    }

    /**
     * 启用/禁用监控
     * @param bool $enable
     */
    public function setMonitoring(bool $enable): void
    {
        $this->enableMonitoring = $enable;
    }

    /**
     * 重置统计数据
     */
    public function resetStats(): void
    {
        $this->stats = [
            'total_operations' => 0,
            'successful_operations' => 0,
            'failed_operations' => 0,
            'total_execution_time' => 0.0,
            'avg_execution_time' => 0.0,
            'connection_gets' => 0,
            'connection_puts' => 0,
            'connection_failures' => 0,
        ];
    }

    /**
     * 获取监控报告
     * @return string
     */
    public function getMonitoringReport(): string
    {
        $stats = $this->getStats();

        $report = "=== Redis连接池监控报告 ===\n";
        $report .= "监控状态: " . ($stats['monitoring_enabled'] ? '启用' : '禁用') . "\n";
        $report .= "\n--- 连接池状态 ---\n";
        $report .= "连接池大小: {$stats['pool_size']}\n";
        $report .= "当前连接数: {$stats['current_count']}\n";
        $report .= "可用连接数: {$stats['available']}\n";
        $report .= "等待连接数: {$stats['waiting']}\n";
        $report .= "活跃连接数: {$stats['active_connections']}\n";
        $report .= "\n--- 操作统计 ---\n";
        $report .= "总操作数: {$stats['operation_stats']['total_operations']}\n";
        $report .= "成功操作数: {$stats['operation_stats']['successful_operations']}\n";
        $report .= "失败操作数: {$stats['operation_stats']['failed_operations']}\n";
        $report .= "成功率: {$stats['success_rate']}%\n";
        $report .= "平均执行时间: {$stats['avg_execution_time_ms']}ms\n";
        $report .= "\n--- 连接统计 ---\n";
        $report .= "连接获取次数: {$stats['operation_stats']['connection_gets']}\n";
        $report .= "连接归还次数: {$stats['operation_stats']['connection_puts']}\n";
        $report .= "连接失败次数: {$stats['operation_stats']['connection_failures']}\n";

        return $report;
    }

    /**
     * 便捷方法：设置键值
     * @param string $key
     * @param mixed $value
     * @param int $expire
     * @throws \Exception
     * @return bool
     */
    public function set(string $key, mixed $value, int $expire = 0): bool
    {
        return $this->execute(function($redis) use ($key, $value, $expire) {
            if ($expire > 0) {
                return $redis->setex($key, $expire, $value);
            }
            return $redis->set($key, $value);
        });
    }

    /**
     * 便捷方法：获取键值
     * @param string $key
     * @throws \Exception
     * @return mixed
     */
    public function get(string $key): mixed
    {
        return $this->execute(function($redis) use ($key) {
            return $redis->get($key);
        });
    }

    /**
     * 便捷方法：删除键
     * @param  array|string $key
     * @throws \Exception
     * @return int
     */
    public function del(array|string $key): int
    {
        return $this->execute(function($redis) use ($key) {
            return $redis->del($key);
        });
    }

    /**
     * 便捷方法：检查键是否存在
     * @param string $key
     * @throws \Exception
     * @return bool
     */
    public function exists(string $key): bool
    {
        return $this->execute(function($redis) use ($key) {
            return $redis->exists($key) > 0;
        });
    }

    /**
     * 便捷方法：设置过期时间
     * @param string $key
     * @param int $seconds
     * @throws \Exception
     * @return bool
     */
    public function expire(string $key, int $seconds): bool
    {
        return $this->execute(function($redis) use ($key, $seconds) {
            return $redis->expire($key, $seconds);
        });
    }

    /**
     * 便捷方法：哈希表操作
     * @param string $key
     * @param string $field
     * @param mixed $value
     * @throws \Exception
     * @return bool
     */
    public function hSet(string $key, string $field, mixed $value): bool
    {
        return $this->execute(function($redis) use ($key, $field, $value) {
            return $redis->hSet($key, $field, $value);
        });
    }

    /**
     * 便捷方法：获取哈希表字段值
     * @param string $key
     * @param string $field
     * @throws \Exception
     * @return mixed
     */
    public function hGet(string $key, string $field)
    {
        return $this->execute(function($redis) use ($key, $field) {
            return $redis->hGet($key, $field);
        });
    }

    /**
     * 便捷方法：获取哈希表所有字段
     * @param string $key
     * @throws \Exception
     * @return array
     */
    public function hGetAll(string $key): array
    {
        return $this->execute(function($redis) use ($key) {
            return $redis->hGetAll($key) ?: [];
        });
    }

    /**
     * 便捷方法：集合添加成员
     * @param string $key
     * @param mixed ...$values
     * @throws \Exception
     * @return int
     */
    public function sAdd(string $key, ...$values): int
    {
        return $this->execute(function($redis) use ($key, $values) {
            return $redis->sAdd($key, ...$values);
        });
    }

    /**
     * 便捷方法：获取集合所有成员
     * @param string $key
     * @throws \Exception
     * @return array
     */
    public function sMembers(string $key): array
    {
        return $this->execute(function($redis) use ($key) {
            return $redis->sMembers($key) ?: [];
        });
    }

    /**
     * 便捷方法：从集合中移除成员
     * @param string $key
     * @param mixed ...$values
     * @throws \Exception
     * @return int
     */
    public function sRem(string $key, ...$values): int
    {
        return $this->execute(function($redis) use ($key, $values) {
            return $redis->sRem($key, ...$values);
        });
    }
}
