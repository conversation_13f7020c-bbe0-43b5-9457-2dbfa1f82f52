-- 表结构: tenant_operation_log
CREATE TABLE `tenant_operation_log` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `tenant_id` int(11) NOT NULL DEFAULT '0' COMMENT '租户ID',
  `user_id` int(11) NOT NULL DEFAULT '0' COMMENT '用户ID',
  `username` varchar(100) NOT NULL DEFAULT '' COMMENT '用户名',
  `title` varchar(500) NOT NULL DEFAULT '' COMMENT '操作消息',
  `path` varchar(255) NOT NULL DEFAULT '' COMMENT '路径',
  `method` varchar(50) NOT NULL DEFAULT '' COMMENT '请求方法',
  `payload` text COMMENT '操作内容',
  `ip` varchar(50) NOT NULL DEFAULT '' COMMENT 'IP地址',
  `request_id` varchar(100) NOT NULL DEFAULT '' COMMENT '请求Id',
  `client_id` varchar(100) NOT NULL DEFAULT '' COMMENT '客户端ID',
  `device_id` varchar(100) NOT NULL DEFAULT '' COMMENT '设备ID',
  `data` json DEFAULT NULL COMMENT '相关数据',
  `operate_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '操作状态（1：成功，0：失败）',
  PRIMARY KEY (`id`),
  KEY `idx_tenant_user` (`tenant_id`,`user_id`),
  KEY `idx_operate_time` (`operate_time`),
  KEY `idx_module_type` (`method`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='租户操作日志表';
