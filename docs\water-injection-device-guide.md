# 注水设备通信协议指南

## 1. 通信架构

### 1.1 主题设计

注水设备通信采用以下MQTT主题结构：

1. 设备状态主题：`helio/tenant/{tenant_id}/device/{device_id}/status`
   - 用途：监控设备在线状态、运行状态等
   - 消息格式：
   ```json
   {
     "type": "status",
     "timestamp": "2024-01-20T10:00:00Z",
     "payload": {
       "status": "online",
       "device_id": "device_001",
       "device_id": "device_001",
       "is_controlling": true,
       "signal_strength": -65
     }
   }
   ```

2. 设备控制主题：`helio/tenant/{tenant_id}/device/{device_id}/control`
   - 用途：发送注水控制指令
   - 消息格式：
   ```json
   {
     "type": "control",
     "timestamp": "2024-01-20T10:00:00Z",
     "payload": {
       "command": "set_injection",
       "params": {
         "flow_rate": 100.5,    // 注水流量设定值(m³/d)
         "pressure": 15.2,      // 注水压力设定值(MPa)
         "duration": 3600      // 持续时间(秒)
       }
     }
   }
   ```

3. 设备数据主题：`helio/tenant/{tenant_id}/device/{device_id}/data`
   - 用途：上报注水实时数据
   - 消息格式：
   ```json
   {
     "type": "data",
     "timestamp": "2024-01-20T10:00:00Z",
     "payload": {
       "flow_rate": 98.5,      // 实际注水流量(m³/d)
       "pressure": 14.8,       // 实际注水压力(MPa)
       "total_volume": 1256.8, // 累计注水量(m³)
       "temperature": 35.6,    // 水温(℃)
       "valve_status": 1       // 阀门状态(0:关闭,1:开启)
     }
   }
   ```

### 1.2 QoS设置

- 控制指令：使用QoS 2，确保指令只执行一次
- 状态上报：使用QoS 1，确保状态可靠传输
- 数据采集：使用QoS 0，允许部分数据丢失

## 2. 设备控制

### 2.1 控制权限

1. 控制条件
   - 设备在线且状态正常
   - 当前无其他客户端正在控制
   - 具有设备控制权限

2. 控制流程
   - 发送控制指令前先检查设备状态
   - 等待设备执行结果反馈
   - 超时未收到反馈则认为指令执行失败

### 2.2 控制指令

1. 开启注水
```json
{
  "command": "start_injection",
  "params": {
    "flow_rate": 100.5,
    "pressure": 15.2
  }
}
```

2. 停止注水
```json
{
  "command": "stop_injection",
  "params": {}
}
```

3. 调整参数
```json
{
  "command": "adjust_params",
  "params": {
    "flow_rate": 95.0,
    "pressure": 14.5
  }
}
```

## 3. 数据采集

### 3.1 采集周期

- 实时数据：5秒/次
- 状态数据：30秒/次
- 心跳数据：30秒/次

### 3.2 数据存储

- 本地缓存：保存最近24小时数据
- 断网续传：网络恢复后自动上传缓存数据
- 数据压缩：采用增量压缩算法

## 4. 异常处理

### 4.1 网络异常

1. 断网处理
   - 保持本地控制功能
   - 缓存采集数据
   - 定期尝试重连

2. 弱网处理
   - 降低数据上报频率
   - 合并多条数据后发送
   - 保证控制指令优先传输

### 4.2 设备异常

1. 通信超时
   - 自动重试3次
   - 超过重试次数后报错

2. 指令执行失败
   - 返回错误码和描述
   - 记录错误日志
   - 通知操作人员

## 5. 安全机制

### 5.1 通信加密

- 使用TLS加密传输
- 支持证书双向认证
- 定期更新密钥

### 5.2 访问控制

- 基于Token的身份认证
- 基于角色的权限控制
- 操作日志审计
