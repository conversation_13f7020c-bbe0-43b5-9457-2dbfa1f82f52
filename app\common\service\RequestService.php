<?php
declare(strict_types = 1);

namespace app\common\service;

/**
 * Request请求服务类
 * <AUTHOR> <<EMAIL>>
 * @package app\common\service
 */
class RequestService extends CommonService
{
    /**
     * 是否开启请求头中请求ID（用于用户请求数据追踪）
     */
    public const ENABLE_REQUEST_ID = true;

    /**
     * 请求头中请求ID名称
     */
    public const HEADER_REQUEST_ID_KEY = 'X-Request-Id';

    /**
     * 是否开启请求头中链路序列号（如同一个requestId时第几次，需要配合前端开启）
     */
    public const ENABLE_SEQUENCE_NUM = false;

    /**
     * 请求头中链路序列号key
     */
    public const HEADER_SEQUENCE_NUM_KEY = 'X-Sequence-Num';
}
