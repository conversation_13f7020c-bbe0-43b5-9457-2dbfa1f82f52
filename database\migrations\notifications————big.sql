-- 通知系统数据库表结构
--
-- 包含以下表：
-- 1. helio_system_notification: 主通知表
-- 2. helio_system_notification_counter: 计数器表
-- 3. helio_system_notification_interaction: 互动记录表(分表)
-- 4. helio_system_notification_queue: 消息队列表
-- 5. helio_system_notification_reads: 阅读状态表(分表)
--
-- 特点：
-- 1. 采用读扩散策略提高读取性能
-- 2. 使用分表策略处理大规模数据
-- 3. 完善的索引设计
-- 4. JSON类型支持灵活的目标人群

CREATE TABLE `helio_system_notification`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标题',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '内容',
  `type` tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '消息类型：1系统消息 2公告通知 3审批通知 4私信',
  `receive_range` tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '接收范围：1全体 2部门 3个人',
  `target_ids` json NULL COMMENT '目标ID(部门id或用户id列表)',
  `sender_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '发送者ID',
  `create_at` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_at` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `status` tinyint(2) UNSIGNED NOT NULL DEFAULT 1 COMMENT '消息状态(1:启用,0:禁用)',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_type`(`type`) USING BTREE,
  INDEX `idx_sender`(`sender_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '消息通知表';


CREATE TABLE `helio_system_notification_counter`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `notification_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '消息ID',
  `like_count` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '点赞数',
  `dislike_count` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '踩数',
  `read_count` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '已读数',
  `create_at` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_at` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_notification`(`notification_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '消息计数器表';


CREATE TABLE `helio_system_notification_interaction`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `notification_id` bigint(20) NOT NULL COMMENT '消息ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `type` tinyint(1) NOT NULL COMMENT '类型：1赞 2踩',
  `create_at` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_at` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_noti_user`(`notification_id`, `user_id`) USING BTREE,
  INDEX `idx_user`(`user_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '消息互动表(分表策略，按消息id分表)';

CREATE TABLE `helio_system_notification_queue` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `notification_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '消息ID',
  `user_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '接收用户ID',
  `is_read` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否已读',
  `interact_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '互动状态: 0=未操作 1=点赞 -1=踩',
  `is_deleted` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否已删除',
  `read_at` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '已读时间',
  `interact_at` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '互动时间',
  `delete_at` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '删除时间',
  `create_at` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_at` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_read` (`user_id`,`is_read`),
  KEY `idx_user_deleted` (`user_id`,`is_deleted`)
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '消息队列表(采用读扩散策略)';

CREATE TABLE `helio_system_notification_reads`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `notification_id` bigint(20) NOT NULL COMMENT '消息ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `create_at` int(11) NOT NULL DEFAULT 0 COMMENT '阅读时间',
  `update_at` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_noti_user`(`notification_id`, `user_id`) USING BTREE,
  INDEX `idx_user`(`user_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '消息阅读状态表(分表策略，按用户id分表)';
