<?php
declare(strict_types = 1);

namespace app\common\traits;

use app\common\constant\ResultConstant;
use think\Response;

/**
 * 响应处理复用类
 * <AUTHOR> <<EMAIL>>
 * @package app\common\traits
 */
trait ResultTrait
{
    /**
     * 默认成功跳转等待时间
     */
    protected int $successWait = 1;

    /**
     * 默认错误跳转等待时间
     */
    protected int $errorWait = 3;

    /**
     * 操作成功跳转的快捷方法
     * @param string|array $message 提示信息、完整数据数组或直接返回的结果数组
     * @param mixed|null $data 返回的数据
     * @param array $extra 附加数据，会作为顶层字段返回
     * @return \think\Response
     */
    public function success(string|array $message = 'success', mixed $data = null, array $extra = []): Response
    {
        if (is_array($message)) {
            // 如果数组中包含data或message键，按照标准格式处理
            if (isset($message['data']) || isset($message['message'])) {
                $extra   = $message;
                $message = $extra['message'] ?? 'success';
                $data    = $extra['data']    ?? null;
                unset($extra['message'], $extra['data']);
            } else {
                // 否则直接将数组作为data返回
                $data    = $message;
                $message = 'success';
            }
        }

        return $this->result(
            $message,
            $data,
            ResultConstant::RESULT_SUCCESS_STATUS,
            ResultConstant::RESULT_SUCCESS_CODE,
            $extra
        );
    }

    /**
     * 操作错误跳转的快捷方法
     * @param string|array $message 提示信息、完整数据数组或直接返回的结果数组
     * @param int $code 错误代码
     * @param array $extra 附加数据，会作为顶层字段返回
     * @return \think\Response
     */
    public function error(string|array $message = 'error', int $code = ResultConstant::RESULT_FAILED_CODE, array $extra = []): Response
    {
        if (is_array($message)) {
            // 如果数组中包含message或code键，按照标准格式处理
            if (isset($message['message']) || isset($message['code'])) {
                $extra   = $message;
                $message = $extra['message'] ?? 'error';
                $code    = $extra['code']    ?? ResultConstant::RESULT_FAILED_CODE;
                unset($extra['message'], $extra['code']);
            } else {
                // 否则将数组作为data放入extra
                $extra['data'] = $message;
                $message       = 'error';
            }
        }

        return $this->result($message, $extra['data'] ?? null, ResultConstant::RESULT_FAILED_STATUS, $code, $extra);
    }

    /**
     * 返回封装后的API数据到客户端
     * @param string $message 提示信息
     * @param mixed|null $data 返回的数据
     * @param int $status 返回的状态
     * @param int $code 返回的代码
     * @param array $extra 附加数据，会作为顶层字段返回
     * @return \think\Response
     */
    protected function result(string $message, mixed $data, int $status, int $code = ResultConstant::RESULT_FAILED_CODE, array $extra = []): Response
    {
        $type = $this->getResponseType();

        $result = [
            'message' => $message,
            'code'    => $code,
            'status'  => $status,
        ];

        // 只有在错误输出的时候，带上request_id
        if ($status === ResultConstant::RESULT_FAILED_STATUS) {
            $result['requestId'] = request()->requestId ?? '';
        }

        // 只在有数据时添加data字段
        if ($data !== null) {
            $result['data'] = $data;
        }

        // 合并额外的字段
        if (!empty($extra)) {
            $result = array_merge($result, $extra);
        }

        return Response::create($result, $type);
    }

    /**
     * URL重定向
     */
    public function redirect(string|array|null $url = null, int $code = 302, array $with = []): Response
    {
        if (empty($url)) {
            return Response::create('/', 'redirect')->code($code)->with($with);
        }

        $url = $this->formatUrl($url);

        return Response::create($url, 'redirect')->code($code)->with($with);
    }

    /**
     * 获取当前的response输出类型
     */
    protected function getResponseType(string $type = 'json'): string
    {
        if ($type !== 'json' && $type !== 'html') {
            return 'json';
        }

        return (request()->isJson() || request()->isAjax() || request()->isPost()) ? 'json' : $type;
    }

    /**
     * 格式化URL
     */
    protected function formatUrl(mixed $url): string
    {
        if (empty($url)) {
            return '';
        }

        if (is_string($url)) {
            return (strpos($url, '://') || str_starts_with($url, '/'))
                ? $url
                : app('route')->buildUrl($url)->__toString();
        }

        return '';
    }

    /**
     * 格式化返回结果
     */
    protected function formatResult(
        mixed $message,
        mixed $data,
        string $url,
        int $wait,
        int $code,
        int $status,
        string $type
    ): array {
        $result = [
            'message' => $message,
            'data'    => $data,
            'url'     => $url,
            'wait'    => $wait,
            'code'    => $code,
            'status'  => $status,
        ];

        if (is_array($message)) {
            unset($result['message']);
            $result = array_merge($result, $message);
        }

        if ($type === 'json') {
            unset($result['wait']);

            if (is_null($data)) {
                unset($result['data']);
            }

            if (empty($url)) {
                unset($result['url']);
            }
        }

        return $result;
    }

    /**
     * 创建响应对象
     */
    protected function createResponse(array $result, string $type, int $status, array $header = []): Response
    {
        if ($type === 'html') {
            $template = $status === 1
                ? config('jump.dispatch_success_tmpl')
                : config('jump.dispatch_error_tmpl');

            return Response::create($template, 'view')->assign($result)->header($header);
        }

        return Response::create($result, $type)->header($header);
    }
}
