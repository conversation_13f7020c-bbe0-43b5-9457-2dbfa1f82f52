<?php
declare(strict_types = 1);

namespace app\common\service;

/**
 * Client服务类
 * <AUTHOR> <<EMAIL>>
 * @package app\common\service
 */
class ClientService extends CommonService
{
    /**
     * 请求头中客户端ID名称（用于区分是哪个终端应用请求过来的）
     */
    public const HEADER_CLIENT_ID_KEY = 'X-Client-Id';

    /**
     * 获取客户端唯一标识
     * @return string
     */
    public static function getClientId(): string
    {
        // 获取客户端传过来的客户端ID（用于标识来自哪个端）
        return request()->header(self::HEADER_CLIENT_ID_KEY) ?: '';
    }

    /**
     * 获取客户端信息
     * @param $clientId
     * @return string
     */
    public static function getClientInfo($clientId): string
    {
        switch ($clientId) {
            case 'heli:df':
                return 'Heli Devops Frontend';
            case 'heli:cf':
                return 'Heli Client Frontend';
            default:
                return 'Unknown';
        }
    }
}
