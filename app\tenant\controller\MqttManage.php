<?php
declare(strict_types = 1);

namespace app\tenant\controller;

use app\system\controller\BaseController;
use app\tenant\model\MqttClientAuth as MqttClientAuthModel;
use app\tenant\model\MqttTopicAcl as MqttTopicAclModel;
use think\App;
use think\facade\Db;
use think\Response;

/**
 * MQTT客户端认证和授权管理控制器
 * 用于管理MQTT客户端认证和主题授权规则
 * <AUTHOR> <<EMAIL>>
 * @package app\tenant\controller
 */
class MqttManage extends BaseController
{
    public function __construct(App $app)
    {
        parent::__construct($app);
    }

    /**
     * 获取MQTT客户端认证列表
     * @return Response
     */
    public function clientAuthList(): Response
    {
        $params   = $this->request->param();
        $pageNo   = $this->request->param('page', 1, 'intval');
        $pageSize = $this->request->param('pageSize', 20, 'intval');
        $tenantId = $this->request->param('tenant_id', 0, 'intval');

        $query = MqttClientAuthModel::where([]);

        // 如果指定了租户ID，则只查询该租户的认证信息
        if ($tenantId > 0) {
            $query->where('tenant_id', $tenantId);
        }

        // 关键词搜索
        if (!empty($params['keyword'])) {
            $query->where(function($q) use ($params) {
                $q->whereOr([
                    ['client_id', 'like', "%{$params['keyword']}%"],
                    ['username', 'like', "%{$params['keyword']}%"],
                    ['description', 'like', "%{$params['keyword']}%"],
                ]);
            });
        }

        // 分页查询
        $total = $query->count();
        $list  = $query->page($pageNo, $pageSize)
            ->order('id', 'desc')
            ->select()
            ->toArray();

        return $this->success([
            'list'     => $list,
            'total'    => $total,
            'pageNo'   => $pageNo,
            'pageSize' => $pageSize,
        ]);
    }

    /**
     * 添加MQTT客户端认证
     * @return Response
     */
    public function addClientAuth(): Response
    {
        $params = $this->request->post();

        // 验证必填字段
        if (empty($params['client_id']) || empty($params['username']) || empty($params['password'])) {
            return $this->error('客户端ID、用户名和密码不能为空');
        }

        // 检查是否已存在相同的客户端ID
        $exists = MqttClientAuthModel::where([
            ['client_id', '=', $params['client_id']],
            ['tenant_id', '=', $params['tenant_id'] ?? 0],
        ])->find();

        if ($exists) {
            return $this->error('该租户下已存在相同的客户端ID');
        }

        // 创建认证记录
        $auth = new MqttClientAuthModel();

        $auth->tenant_id    = $params['tenant_id'] ?? 0;
        $auth->client_id    = $params['client_id'];
        $auth->username     = $params['username'];
        $auth->password     = $params['password'];
        $auth->is_superuser = $params['is_superuser'] ?? 0;
        $auth->status       = $params['status']       ?? 1;
        $auth->description  = $params['description']  ?? '';

        if ($auth->save()) {
            // 写入事件日志
            $this->event_log('添加MQTT客户端认证', $params, []);

            return $this->success('添加成功');
        }

        return $this->error('添加失败');
    }

    /**
     * 编辑MQTT客户端认证
     * @return Response
     */
    public function editClientAuth(): Response
    {
        $params = $this->request->post();

        if (empty($params['id'])) {
            return $this->error('参数错误');
        }

        $auth = MqttClientAuthModel::find($params['id']);

        if (!$auth) {
            return $this->error('认证信息不存在');
        }

        // 更新字段
        if (isset($params['client_id'])) {
            $auth->client_id = $params['client_id'];
        }

        if (isset($params['username'])) {
            $auth->username = $params['username'];
        }

        if (isset($params['password'])) {
            $auth->password = $params['password'];
        }

        if (isset($params['is_superuser'])) {
            $auth->is_superuser = $params['is_superuser'];
        }

        if (isset($params['status'])) {
            $auth->status = $params['status'];
        }

        if (isset($params['description'])) {
            $auth->description = $params['description'];
        }

        if ($auth->save()) {
            // 写入事件日志
            $this->event_log('编辑MQTT客户端认证', $params, []);

            return $this->success('更新成功');
        }

        return $this->error('更新失败');
    }

    /**
     * 删除MQTT客户端认证
     * @return Response
     */
    public function deleteClientAuth(): Response
    {
        $id = $this->request->param('id');

        if (empty($id)) {
            return $this->error('参数错误');
        }

        if (MqttClientAuthModel::destroy($id)) {
            // 写入事件日志
            $this->event_log('删除MQTT客户端认证', ['id' => $id], []);

            return $this->success('删除成功');
        }

        return $this->error('删除失败');
    }

    /**
     * 获取MQTT主题授权列表
     * @return Response
     */
    public function topicAclList(): Response
    {
        $params   = $this->request->param();
        $pageNo   = $this->request->param('page', 1, 'intval');
        $pageSize = $this->request->param('pageSize', 20, 'intval');
        $tenantId = $this->request->param('tenant_id', 0, 'intval');

        $query = MqttTopicAclModel::where([]);

        // 如果指定了租户ID，则只查询该租户的授权规则
        if ($tenantId > 0) {
            $query->where('tenant_id', $tenantId);
        }

        // 关键词搜索
        if (!empty($params['keyword'])) {
            $query->where(function($q) use ($params) {
                $q->whereOr([
                    ['client_id', 'like', "%{$params['keyword']}%"],
                    ['username', 'like', "%{$params['keyword']}%"],
                    ['topic', 'like', "%{$params['keyword']}%"],
                    ['description', 'like', "%{$params['keyword']}%"],
                ]);
            });
        }

        // 分页查询
        $total = $query->count();
        $list  = $query->page($pageNo, $pageSize)
            ->order('priority', 'desc')
            ->select()
            ->toArray();

        return $this->success([
            'list'     => $list,
            'total'    => $total,
            'pageNo'   => $pageNo,
            'pageSize' => $pageSize,
        ]);
    }

    /**
     * 添加MQTT主题授权规则
     * @return Response
     */
    public function addTopicAcl(): Response
    {
        $params = $this->request->post();

        // 验证必填字段
        if (empty($params['topic']) || empty($params['action']) || empty($params['permission'])) {
            return $this->error('主题、操作类型和权限不能为空');
        }

        // 验证操作类型
        if (!in_array($params['action'], ['publish', 'subscribe', 'all'])) {
            return $this->error('操作类型必须是publish、subscribe或all');
        }

        // 验证权限
        if (!in_array($params['permission'], ['allow', 'deny'])) {
            return $this->error('权限必须是allow或deny');
        }

        // 创建授权规则
        $acl              = new MqttTopicAclModel();
        $acl->tenant_id   = $params['tenant_id'] ?? 0;
        $acl->client_id   = $params['client_id'] ?? null;
        $acl->username    = $params['username']  ?? null;
        $acl->topic       = $params['topic'];
        $acl->action      = $params['action'];
        $acl->permission  = $params['permission'];
        $acl->qos         = $params['qos']         ?? -1;
        $acl->retain      = $params['retain']      ?? false;
        $acl->priority    = $params['priority']    ?? 0;
        $acl->status      = $params['status']      ?? 1;
        $acl->description = $params['description'] ?? '';

        if ($acl->save()) {
            // 写入事件日志
            $this->event_log('添加MQTT主题授权规则', $params, []);

            return $this->success('添加成功');
        }

        return $this->error('添加失败');
    }

    /**
     * 编辑MQTT主题授权规则
     * @return Response
     */
    public function editTopicAcl(): Response
    {
        $params = $this->request->post();

        if (empty($params['id'])) {
            return $this->error('参数错误');
        }

        $acl = MqttTopicAclModel::find($params['id']);

        if (!$acl) {
            return $this->error('授权规则不存在');
        }

        // 更新字段
        if (isset($params['client_id'])) {
            $acl->client_id = $params['client_id'];
        }

        if (isset($params['username'])) {
            $acl->username = $params['username'];
        }

        if (isset($params['topic'])) {
            $acl->topic = $params['topic'];
        }

        if (isset($params['action'])) {
            if (in_array($params['action'], ['publish', 'subscribe', 'all'])) {
                $acl->action = $params['action'];
            } else {
                return $this->error('操作类型必须是publish、subscribe或all');
            }
        }

        if (isset($params['permission'])) {
            if (in_array($params['permission'], ['allow', 'deny'])) {
                $acl->permission = $params['permission'];
            } else {
                return $this->error('权限必须是allow或deny');
            }
        }

        if (isset($params['qos'])) {
            $acl->qos = (int)$params['qos'];
        }

        if (isset($params['retain'])) {
            $acl->retain = (bool)$params['retain'];
        }

        if (isset($params['priority'])) {
            $acl->priority = (int)$params['priority'];
        }

        if (isset($params['status'])) {
            $acl->status = $params['status'];
        }

        if (isset($params['description'])) {
            $acl->description = $params['description'];
        }

        if ($acl->save()) {
            // 写入事件日志
            $this->event_log('编辑MQTT主题授权规则', $params, []);

            return $this->success('更新成功');
        }

        return $this->error('更新失败');
    }

    /**
     * 删除MQTT主题授权规则
     * @return Response
     */
    public function deleteTopicAcl(): Response
    {
        $id = $this->request->param('id');

        if (empty($id)) {
            return $this->error('参数错误');
        }

        if (MqttTopicAclModel::destroy($id)) {
            // 写入事件日志
            $this->event_log('删除MQTT主题授权规则', ['id' => $id], []);

            return $this->success('删除成功');
        }

        return $this->error('删除失败');
    }

    /**
     * 批量设置租户的MQTT主题授权规则
     * @return Response
     */
    public function batchSetTenantAcl(): Response
    {
        $params = $this->request->post();

        if (empty($params['tenant_id']) || empty($params['rules'])) {
            return $this->error('参数错误');
        }

        $tenantId = (int)$params['tenant_id'];
        $rules    = $params['rules'];

        if (!is_array($rules)) {
            return $this->error('规则格式错误');
        }

        Db::startTrans();

        try {
            // 删除该租户的所有现有规则
            MqttTopicAclModel::where('tenant_id', $tenantId)->delete();

            // 添加新规则
            $newRules = [];

            foreach ($rules as $rule) {
                if (empty($rule['topic']) || empty($rule['action']) || empty($rule['permission'])) {
                    continue;
                }

                $newRules[] = [
                    'tenant_id'   => $tenantId,
                    'client_id'   => $rule['client_id'] ?? null,
                    'topic'       => $rule['topic'],
                    'action'      => $rule['action'],
                    'permission'  => $rule['permission'],
                    'qos'         => $rule['qos']         ?? -1,
                    'retain'      => $rule['retain']      ?? false,
                    'priority'    => $rule['priority']    ?? 0,
                    'status'      => $rule['status']      ?? 1,
                    'description' => $rule['description'] ?? '',
                    'create_time' => time(),
                    'update_time' => time(),
                ];
            }

            if (!empty($newRules)) {
                (new MqttTopicAclModel())->saveAll($newRules);
            }

            Db::commit();

            // 写入事件日志
            $this->event_log('批量设置租户MQTT主题授权规则', ['tenant_id' => $tenantId], []);

            return $this->success('设置成功');
        } catch (\Exception $e) {
            Db::rollback();

            return $this->error('设置失败：' . $e->getMessage());
        }
    }
}
