<?php
declare(strict_types = 1);

namespace app\demo;

use helio\App;
use think\facade\Db;

/**
 * 演示应用管理类
 */
class DemoApp extends App
{
    /**
     * 安装应用
     * @return void
     */
    public function install()
    {
        // 执行数据库安装脚本
        $sql = file_get_contents(__DIR__ . '/database/install.sql');

        if ($sql) {
            Db::execute($sql);
        }

        // 初始化默认数据
        $this->initData();
    }

    /**
     * 卸载应用
     * @return void
     */
    public function uninstall()
    {
        // 执行数据库卸载脚本
        $sql = file_get_contents(__DIR__ . '/database/uninstall.sql');

        if ($sql) {
            Db::execute($sql);
        }
    }

    /**
     * 启用应用
     * @throws \think\db\exception\DbException
     * @return void
     */
    public function enable()
    {
        // 更新应用状态
        Db::name('demo_config')->where('1=1')->update([
            'status'    => 1,
            'update_at' => time(),
        ]);
    }

    /**
     * 禁用应用
     * @throws \think\db\exception\DbException
     * @return void
     */
    public function disable()
    {
        // 更新应用状态
        Db::name('demo_config')->where('1=1')->update([
            'status'    => 0,
            'update_at' => time(),
        ]);
    }

    /**
     * 升级应用
     * @param $version
     * @return void
     */
    public function upgrade($version)
    {
        // 执行升级脚本
        $file = __DIR__ . '/database/upgrade/' . $version . '.sql';

        if (file_exists($file)) {
            $sql = file_get_contents($file);

            if ($sql) {
                Db::execute($sql);
            }
        }
    }

    /**
     * 应用数据初始化
     * @return void
     */
    protected function initData()
    {
        // 初始化配置
        Db::name('demo_config')->insert([
            'name'      => '示例配置',
            'value'     => '默认值',
            'create_at' => time(),
            'update_at' => time(),
        ]);
    }
}
