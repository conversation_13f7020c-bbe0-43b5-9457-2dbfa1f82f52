<?php
declare(strict_types = 1);

namespace app\tenant\model;

use app\tenant\model\PermissionRole as PermissionRoleModel;
use think\db\Query;
use think\model\relation\BelongsToMany;
use think\model\relation\HasMany;

/**
 * 权限模型类
 * <AUTHOR> <<EMAIL>>
 * @package app\tenant\model
 */
class Permission extends BaseModel
{
    protected $name = 'tenant_permission';

    protected $field = [
        'id',
        'pid',
        'name',
        'title',
        'type',
        'icon',
        'path',
        'component',
        'redirect',
        'permission',
        'is_show',
        'sort',
        'status',
        'create_at',
        'update_at',
    ];

    // 自动完成
    protected array $auto = ['update_at'];

    protected $insert = ['create_at'];

    /**
     * 关联角色
     * @return \think\model\relation\BelongsToMany
     */
    public function roles(): BelongsToMany
    {
        return $this->belongsToMany(Role::class, PermissionRoleModel::class, 'role_id', 'permission_id');
    }

    /**
     * 获取子菜单
     * @return \think\model\relation\HasMany
     */
    public function children(): HasMany
    {
        return $this->hasMany(self::class, 'pid');
    }

    /**
     * 查询作用域：启用状态
     * @param \think\db\Query $query
     * @return \think\db\Query
     */
    public function scopeEnabled(Query $query): Query
    {
        return $query->where('status', 1);
    }

    /**
     * 查询作用域：菜单类型
     * @param \think\db\Query $query
     * @return \think\db\Query
     */
    public function scopeMenu(Query $query): Query
    {
        return $query->where('type', 1);
    }

    /**
     * 查询作用域：按排序
     * @param \think\db\Query $query
     * @return \think\db\Query
     */
    public function scopeSorted(Query $query): Query
    {
        return $query->order('sort', 'asc')->order('id', 'asc');
    }

    /**
     * 添加权限节点
     * @param $data
     * @return int
     */
    public static function addPermission($data): int
    {
        if ($res = self::create($data)) {
            return $res->id;
        }

        return 0;
    }

    /**
     * 获取正常权限组列表
     * @param array $where
     * @param string $desc
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return array
     */
    public static function getRulelist(array $where = ['status', '=', 1], string $desc = 'asc'): array
    {
        $map[] = $where;

        $data = self::where($map)->order('sort', $desc)->order('id', $desc)->select();

        return $data->toArray();
    }
}
