-- 表结构: system_app_install
CREATE TABLE `system_app_install` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `app_id` int(11) NOT NULL DEFAULT '0' COMMENT '应用ID',
  `tenant_id` int(11) NOT NULL DEFAULT '0' COMMENT '租户ID',
  `config` text COMMENT '应用配置JSON',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态(1:启用,0:禁用)',
  `expire_time` int(11) NOT NULL DEFAULT '0' COMMENT '过期时间',
  `create_at` int(11) NOT NULL DEFAULT '0' COMMENT '安装时间',
  `update_at` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_app_tenant` (`app_id`,`tenant_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='应用安装表';
