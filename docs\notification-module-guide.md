# 消息通知队列使用说明

## 消息通知队列

### 配置说明

1. Redis 队列配置 (`config/queue.php`):
```php
return [
    'default' => 'redis',
    'connections' => [
        'redis' => [
            'type' => 'redis',
            'queue' => 'notification',
            'host' => env('REDIS.HOST', '127.0.0.1'),
            'port' => env('REDIS.PORT', 6379),
            'password' => env('REDIS.PASSWORD', ''),
            'select' => env('REDIS.SELECT', 0),
            'timeout' => 0,
            'persistent' => false,
        ],
    ],
];
```

### 运行队列消费者

#### 开发环境
```bash
# 启动队列处理进程
php think queue:work --queue notification --daemon

# 指定队列并设置内存限制
php think queue:work --queue notification --daemon --memory 1024

# 设置尝试次数和超时时间
php think queue:work --queue notification --daemon --tries 3 --timeout 60
```

#### 生产环境
使用 Supervisor 管理队列进程:

1. 复制配置文件：
```bash
cp ./deploy/supervisor/notification-queue.conf /etc/supervisor/conf.d/
```

2. Supervisor 配置示例：
```ini
[program:notification-queue]
process_name=%(program_name)s_%(process_num)02d
command=php think queue:work --queue notification --daemon
directory=/path/to/project
autostart=true
autorestart=true
user=www-data
numprocs=2
redirect_stderr=true
stdout_logfile=/path/to/project/runtime/logs/queue.log
```

3. 重启 Supervisor：
```bash
supervisorctl reread
supervisorctl update
supervisorctl start notification-queue:*
```

### 常用命令

```bash
# 查看所有队列状态
supervisorctl status

# 启动指定队列
supervisorctl start notification-queue:*

# 停止指定队列
supervisorctl stop notification-queue:*

# 重启指定队列
supervisorctl restart notification-queue:*
```

### 查看队列日志

```bash
# 实时查看队列日志
tail -f runtime/logs/queue.log

# 查看最近100行日志
tail -n 100 runtime/logs/queue.log
```

### 队列监控

可以通过 Redis 命令查看队列状态：

```bash
# 查看队列长度
redis-cli llen notification

# 查看队列内容
redis-cli lrange notification 0 -1
```

### 注意事项

1. 内存管理
   - 设置合理的内存限制
   - 定期重启队列进程
   - 监控内存使用情况

2. 错误处理
   - 失败重试次数限制
   - 记录错误日志
   - 设置超时时间

3. 性能优化
   - 合理设置进程数
   - 批量处理数据
   - 避免长时间运行

4. 监控告警
   - 监控队列长度
   - 监控处理延迟
   - 设置错误告警
