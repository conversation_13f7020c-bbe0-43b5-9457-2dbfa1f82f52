<?php
declare(strict_types = 1);

namespace app\system\model;

use think\model\relation\BelongsTo;

/**
 * 操作日志模型
 * <AUTHOR> <<EMAIL>>
 * @package app\system\model
 */
class OperationLog extends BaseModel
{
    protected $name = 'system_operation_log';

    protected $autoWriteTimestamp = false;

    // 关闭自动写入update_time字段
    protected $updateTime = false;

    /**
     * 允许的字段
     * @var array
     */
    protected $field = [
        'id',
        'user_id',
        'event',
        'path',
        'params',
        'description',
        'user_agent',
        'finger_id',
        'request_id',
        'client_ip',
        'mem',
        'reqs',
        'runtime',
        'create_at',
    ];

    /**
     * 用户关联
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    /**
     * 搜索器：事件名称
     * @param $query
     * @param $value
     */
    public function searchEventAttr($query, $value)
    {
        if (!empty($value)) {
            $query->where('event', 'LIKE', "%{$value}%");
        }
    }

    /**
     * 搜索器：请求路径
     * @param $query
     * @param $value
     */
    public function searchPathAttr($query, $value)
    {
        if (!empty($value)) {
            $query->where('path', 'LIKE', "%{$value}%");
        }
    }

    /**
     * 搜索器：请求参数
     * @param $query
     * @param $value
     */
    public function searchParamsAttr($query, $value)
    {
        if (!empty($value)) {
            $query->where('params', 'LIKE', "%{$value}%");
        }
    }

    /**
     * 搜索器：描述
     * @param $query
     * @param $value
     */
    public function searchDescriptionAttr($query, $value)
    {
        if (!empty($value)) {
            $query->where('description', 'LIKE', "%{$value}%");
        }
    }

    /**
     * 搜索器：用户代理
     * @param $query
     * @param $value
     */
    public function searchUserAgentAttr($query, $value)
    {
        if (!empty($value)) {
            $query->where('user_agent', 'LIKE', "%{$value}%");
        }
    }

    /**
     * 获取器：客户端IP
     * @param $value
     * @return string
     */
    public function getClientIpAttr($value): string
    {
        return $value ? long2ip((int)$value) : '';
    }

    /**
     * 获取器：内存使用量
     * @param $value
     * @return string
     */
    public function getMemAttr($value): string
    {
        return $value ? format_bytes((float)$value) : '';
    }

    /**
     * 获取器：请求数
     * @param $value
     * @return string
     */
    public function getReqsAttr($value): string
    {
        return $value ? number_format((float)$value, 2) . 'req/s' : '';
    }

    /**
     * 获取器：运行时间
     * @param $value
     * @return string
     */
    public function getRuntimeAttr($value): string
    {
        return $value ? number_format((float)$value, 6) . 's' : '';
    }

    /**
     * 获取客户端IP
     * @param $userId
     * @param $fingerId
     * @return string
     */
    public static function getClientIpByUserFinger($userId, $fingerId): string
    {
        return self::where(['user_id' => $userId, 'finger_id' => $fingerId])->value('client_ip');
    }

    /**
     * 添加日志记录
     * @param array $data
     * @return bool
     */
    public static function addLog(array $data): bool
    {
        return self::create($data) != false;
    }
}
