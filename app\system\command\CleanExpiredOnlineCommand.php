<?php
namespace app\system\command;

use app\system\service\OnlineUserService;
use think\console\Input;
use think\console\Output;

/**
 * 清理过期在线用户命令
 * <AUTHOR> <<EMAIL>>
 * @package app\system\command
 */
class CleanExpiredOnlineCommand extends BaseCommand
{
    /**
     * 配置指令
     * @return void
     */
    protected function configure(): void
    {
        $this->setName('clean:online')
             ->setDescription('清理过期在线用户');
    }

    /**
     * 执行指令
     * @param Input $input
     * @param Output $output
     * @return void
     */
    protected function execute(Input $input, Output $output): void
    {
        $output->writeln('开始清理过期在线用户...');

        // 清理超过心跳时间的用户
        $expire       = config('auth.heartbeat_expire', 30); // 默认30秒
        $offlineCount = OnlineUserService::cleanOfflineUsers($expire);

        // 处理过期的黑名单
        $blacklistCount = OnlineUserService::processExpiredBlacklist();

        // 计算总清理数量
        $count = $offlineCount + $blacklistCount;

        $output->writeln("清理完成，共处理 {$count} 条记录");
    }
}
