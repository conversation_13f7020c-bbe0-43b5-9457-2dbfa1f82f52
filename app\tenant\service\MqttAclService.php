<?php
declare(strict_types = 1);
namespace app\tenant\service;

use app\tenant\model\MqttTopicAcl as MqttTopicAclModel;
use app\tenant\repository\TenantRepository;
use think\facade\Log;

/**
 * MQTT授权服务类
 * 提供EMQX HTTP授权插件所需的授权逻辑
 * 负责控制客户端对主题的访问权限
 * <AUTHOR> <<EMAIL>>
 * @package app\tenant\service
 */
class MqttAclService extends BaseService
{
    /**
     * 单例实例
     */
    private static ?MqttAclService $instance = null;

    /**
     * 私有构造函数，防止外部直接实例化
     */
    private function __construct()
    {
        // 私有构造函数，防止外部直接实例化
    }

    /**
     * 私有克隆方法，防止对象被克隆
     */
    private function __clone()
    {
        // 私有克隆方法，防止对象被克隆
    }

    /**
     * 获取单例实例
     * @return MqttAclService
     */
    public static function getInstance(): self
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }

        return self::$instance;
    }

    /**
     * 授权客户端操作
     * @param array $params 授权参数
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return array 授权结果
     */
    public function authorize(array $params): array
    {
        // 提取请求参数
        $action   = $params['action']   ?? ''; // 操作：publish, subscribe
        $clientId = $params['clientid'] ?? '';
        $topic    = $params['topic']    ?? '';
        $qos      = isset($params['qos']) ? (int)$params['qos'] : 0;
        $retain   = isset($params['retain']) && $params['retain'];
        $username = $params['username'] ?? '';

        // 记录详细日志用于调试
        Log::debug('MQTT authorize params: ' . json_encode($params, JSON_UNESCAPED_UNICODE));

        // 验证主题是否为空
        if (empty($topic)) {
            return $this->deny('Topic is empty', [
                'action'   => $action,
                'username' => $username,
            ]);
        }

        // 验证QoS级别不能大于1
        if ($qos > 1) {
            return $this->deny('QoS level not allowed', [
                'action'   => $action,
                'username' => $username,
                'qos'      => $qos,
            ]);
        }

        // 验证操作类型为publish时才可以设置retain标志
        if ($retain && $action !== 'publish') {
            return $this->deny('Retain flag only allowed for publish operation', [
                'action'   => $action,
                'username' => $username,
            ]);
        }

        // 验证用户名格式并提取租户信息
        if (!preg_match('/^[a-zA-Z0-9]+@[a-zA-Z0-9]+$/', $username)) {
            return $this->deny('Invalid username format', ['username' => $username]);
        }

        // 分割username以获取租户编码和用户ID
        list($tenantCode, $userId) = explode('@', $username);

        if (empty($tenantCode) || empty($userId)) {
            return $this->deny('Invalid tenant code or user id', ['username' => $username]);
        }

        // 验证租户状态
        $tenant = (new TenantRepository())->findByCode($tenantCode);

        if ($tenant->isEmpty() || $tenant['status'] !== 1) {
            return $this->deny('Tenant not active or not found', ['tenant_code' => $tenantCode]);
        }

        $tenantId = intval($tenant['id']);

        // 准备变量并检查权限
        $data = [
            'tenantCode' => $tenantCode,
            'userId'     => $userId,
            'tenantId'   => $tenantId,
            'clientId'   => $clientId,
        ];

        // 检查数据库中的ACL规则
        $permission = MqttTopicAclModel::checkPermission(
            $action,
            $topic,
            $qos,
            $retain,
            $data
        );

        // 匹配成功时返回允许结果
        if ($permission) {
            return $this->allow([
                'action'   => $action,
                'username' => $username,
                'topic'    => $topic,
            ]);
        }

        // 默认拒绝访问
        return $this->deny('No matching ACL rule found', [
            'action'   => $action,
            'username' => $username,
            'topic'    => $topic,
            'qos'      => $qos,
            'retain'   => $retain ? 'true' : 'false',
        ]);
    }

    /**
     * 记录日志并返回允许结果
     * @param array $logData 日志数据
     * @return array 允许结果
     */
    private function allow(array $logData = []): array
    {
        // 构建日志消息，包含所有日志数据
        Log::debug('MQTT authorize allowed: ' . $this->formatLogData($logData));

        return ['result' => 'allow'];
    }

    /**
     * 记录日志并返回拒绝结果
     * @param string $message 拒绝原因
     * @param array $logData 日志数据
     * @return array 拒绝结果
     */
    private function deny(string $message, array $logData = []): array
    {
        // 构建日志消息，包含所有日志数据
        Log::warning('MQTT authorize denied: ' . $message . ' ' . $this->formatLogData($logData));

        return ['result' => 'deny'];
    }

    /**
     * 格式化日志数据为字符串
     * @param array $logData 日志数据
     * @return string 格式化后的字符串
     */
    private function formatLogData(array $logData): string
    {
        if (empty($logData)) {
            return '';
        }

        $parts = [];

        foreach ($logData as $key => $value) {
            $parts[] = $key . '=' . (is_scalar($value) ? $value : json_encode($value, JSON_UNESCAPED_UNICODE));
        }

        return '[' . implode(', ', $parts) . ']';
    }

    /**
     * 检查主题是否匹配模式
     * 支持MQTT通配符: # 和 +
     * # 表示匹配任意多个层级
     * + 表示匹配单个层级中的任意内容
     *
     * @param string $topic 待检查的主题
     * @param string $pattern 主题模式
     * @return bool 是否匹配
     */
    private function isTopicMatch(string $topic, string $pattern): bool
    {
        // 将主题和模式按/分割
        $topicParts   = explode('/', trim($topic, '/'));
        $patternParts = explode('/', trim($pattern, '/'));

        // 如果模式中包含#通配符
        if (in_array('#', $patternParts)) {
            $hashPos = array_search('#', $patternParts);

            // # 必须是最后一个部分，否则不是有效的MQTT主题过滤器
            if ($hashPos !== count($patternParts) - 1) {
                return false;
            }

            // 检查 # 之前的部分是否匹配
            for ($i = 0; $i < $hashPos; $i++) {
                if ($i >= count($topicParts)) {
                    return false;
                }

                if ($patternParts[$i] !== $topicParts[$i] && $patternParts[$i] !== '+') {
                    return false;
                }
            }

            // # 匹配剩余的所有层级
            return true;
        }

        // 如果主题层级数与模式不匹配，则返回false（当没有#通配符时）
        if (count($topicParts) !== count($patternParts)) {
            return false;
        }

        // 逐层比较
        for ($i = 0; $i < count($topicParts); $i++) {
            // + 通配符匹配单个层级的任意内容
            if ($patternParts[$i] === '+') {
                continue;
            }

            // 精确匹配
            if ($patternParts[$i] !== $topicParts[$i]) {
                return false;
            }
        }

        return true;
    }
}
