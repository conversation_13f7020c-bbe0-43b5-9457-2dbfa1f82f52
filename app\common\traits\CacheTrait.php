<?php
namespace app\common\traits;

use think\facade\Cache;
use think\facade\Log;

/**
 * 缓存特性类
 * 为Repository提供缓存机制，提高查询效率
 * <AUTHOR> <<EMAIL>>
 * @package app\common\traits
 */
trait CacheTrait
{
    /**
     * 缓存前缀
     * @var string
     */
    protected string $cachePrefix = 'repo:';

    /**
     * 默认缓存时间（秒）
     * @var int
     */
    protected int $cacheExpire = 3600; // 默认1小时

    /**
     * 是否启用缓存
     * @var bool
     */
    protected bool $enableCache = true;

    /**
     * 获取缓存键名
     * @param string $key 缓存键
     * @return string 完整的缓存键名
     */
    protected function getCacheKey(string $key): string
    {
        return $this->cachePrefix . $key;
    }

    /**
     * 获取缓存标签
     * @return string 缓存标签
     */
    protected function getCacheTag(): string
    {
        // 默认使用当前类名作为标签
        return str_replace('\\', ':', get_class($this));
    }

    /**
     * 从缓存中获取数据，如果缓存不存在则执行回调函数获取数据并缓存
     * @param string $key 缓存键
     * @param callable $callback 回调函数，用于获取数据
     * @param int|null $expire 缓存过期时间（秒），null表示使用默认时间
     * @param bool $forceRefresh 是否强制刷新缓存
     * @return mixed 缓存数据或回调函数返回的数据
     */
    protected function remember(string $key, callable $callback, ?int $expire = null, bool $forceRefresh = false): mixed
    {
        // 如果未启用缓存或强制刷新，直接执行回调
        if (!$this->enableCache || $forceRefresh) {
            return $callback();
        }

        $cacheKey = $this->getCacheKey($key);
        $cacheTag = $this->getCacheTag();
        $expire ??= $this->cacheExpire;

        try {
            // 尝试从缓存获取数据
            $data = Cache::get($cacheKey);

            if ($data !== null) {
                return $data;
            }
        } catch (\Exception $e) {
            Log::error('Cache get error: ' . $e->getMessage());
        }

        // 缓存不存在，执行回调获取数据
        $data = $callback();

        try {
            // 将数据存入缓存
            Cache::tag($cacheTag)->set($cacheKey, $data, $expire);
        } catch (\Exception $e) {
            Log::error('Cache set error: ' . $e->getMessage());
        }

        return $data;
    }

    /**
     * 清除指定键的缓存
     * @param string $key 缓存键
     * @return bool 是否成功
     */
    protected function forgetCache(string $key): bool
    {
        try {
            $cacheKey = $this->getCacheKey($key);

            return Cache::delete($cacheKey);
        } catch (\Exception $e) {
            Log::error('Cache delete error: ' . $e->getMessage());

            return false;
        }
    }

    /**
     * 清除当前仓储的所有缓存
     * @return bool 是否成功
     */
    protected function clearCache(): bool
    {
        try {
            $cacheTag = $this->getCacheTag();

            return Cache::tag($cacheTag)->clear();
        } catch (\Exception $e) {
            Log::error('Cache clear error: ' . $e->getMessage());

            return false;
        }
    }
}
