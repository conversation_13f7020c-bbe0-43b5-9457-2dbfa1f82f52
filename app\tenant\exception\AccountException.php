<?php
declare(strict_types = 1);

namespace app\tenant\exception;

use app\common\constant\ResultConstant;
use app\common\exception\CommonException as BaseException;

/**
 * 租户模块业务异常类
 * <AUTHOR> <<EMAIL>>
 * @package app\tenant\exception
 */
class AccountException extends BaseException
{
    /**
     * data数据
     */
    public mixed $data = [];

    /**
     * 额外数据
     */
    public mixed $extra = [];

    /**
     * 无效参数
     */
    public const INVALID_PARAM = 10000;

    /**
     * 租户不存在
     */
    public const TENANT_NOT_FOUND = 10001;

    /**
     * 租户已被禁用
     */
    public const TENANT_DISABLED = 10002;

    /**
     * 租户已过期
     */
    public const TENANT_EXPIRED = 10003;

    /**
     * 用户不存在
     */
    public const USER_NOT_FOUND = 10004;

    /**
     * 用户已被禁用
     */
    public const USER_DISABLED = 10005;

    /**
     * 用户认证失败
     */
    public const AUTH_FAILED = 10006;

    /**
     * Token无效或已过期
     */
    public const INVALID_TOKEN = 10007;

    /**
     * 无权访问
     */
    public const ACCESS_DENIED = 10008;

    /**
     * 超过最大用户数限制
     */
    public const MAX_USERS_LIMIT = 10009;

    /**
     * 错误信息映射
     */
    protected array $messages = [
        self::TENANT_NOT_FOUND => '租户不存在',
        self::TENANT_DISABLED  => '租户已被禁用',
        self::TENANT_EXPIRED   => '租户已过期',
        self::USER_NOT_FOUND   => '用户不存在',
        self::USER_DISABLED    => '用户已被禁用',
        self::AUTH_FAILED      => '认证失败',
        self::INVALID_TOKEN    => 'Token无效或已过期',
        self::ACCESS_DENIED    => '无权访问',
        self::MAX_USERS_LIMIT  => '已超过最大用户数限制',
    ];

    /**
     * 获取错误信息
     * @param int $code 错误码
     * @return string
     */
    public function getMessageByCode(int $code): string
    {
        return $this->messages[$code] ?? '未知错误';
    }

    /**
     * 设置data数据
     * @param array $data
     * @return $this
     */
    public function setData(array $data): self
    {
        $this->data = $data;

        return $this;
    }

    /**
     * 获取data数据
     * @return array
     */
    public function getData(): array
    {
        return $this->data;
    }

    /**
     * 设置额外数据
     * @param array $extra
     * @return $this
     */
    public function setExtra(array $extra): self
    {
        $this->extra = $extra;

        return $this;
    }

    /**
     * 获取额外数据
     * @return array
     */
    public function getExtra(): array
    {
        return $this->extra;
    }

    /**
     * 抛出异常
     * @param int $code
     * @param string|null $customMessage
     * @throws AccountException
     */
    public static function throwException(int $code, ?string $customMessage = null): void
    {
        $instance = new self();
        $message  = $customMessage ?? $instance->getMessageByCode($code);

        throw new self($message, $code);
    }

    /**
     * 获取响应数据
     */
    public function getResponseData(): array
    {
        return [
            'status'  => 0,
            'code'    => $this->getCode() ?: ResultConstant::RESULT_FAILED_CODE,
            'message' => $this->getMessage(),
            'data'    => $this->getData() ?: null,
            'extra'   => $this->getExtra() ?: [],
        ];
    }
}
