<?php
declare(strict_types = 1);

namespace app\system\model;

/**
 * 系统配置模型
 * <AUTHOR> <<EMAIL>>
 * @package app\system\model
 */
class Configure extends BaseModel
{
    protected $name = 'system_configure';

    protected $field = [];

    /**
     * 控件类型定义，必须和configure表中的type字段中定义
     * @var string[]
     */
    public static array $widgetType = [
        'input'    => ['name' => 'input', 'label' => '单行文本', 'enum' => false, 'placeholder' => true],
        'textarea' => ['name' => 'textarea', 'label' => '多文本框', 'enum' => false, 'placeholder' => true],
        'password' => ['name' => 'password', 'label' => '密码控件', 'enum' => false, 'placeholder' => true],
        'select'   => ['name' => 'select', 'label' => '下拉框', 'enum' => true, 'placeholder' => true],
        'checkbox' => ['name' => 'checkbox', 'label' => '复选框', 'enum' => true, 'placeholder' => false],
        'radio'    => ['name' => 'radio', 'label' => '单选框', 'enum' => true, 'placeholder' => false],
        'switch'   => ['name' => 'switch', 'label' => '开关按钮', 'enum' => true, 'placeholder' => false],
        'number'   => ['name' => 'number', 'label' => '数值控件', 'enum' => false, 'placeholder' => true],
        'date'     => ['name' => 'date', 'label' => '日期控件', 'enum' => false, 'placeholder' => true],
        'hidden'   => ['name' => 'hidden', 'label' => '隐藏控件', 'enum' => false, 'placeholder' => false],
    ];
}
