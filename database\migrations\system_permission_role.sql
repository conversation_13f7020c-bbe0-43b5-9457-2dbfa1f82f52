-- 表结构: system_permission_role
CREATE TABLE `system_permission_role` (
  `role_id` int(11) NOT NULL DEFAULT '0' COMMENT '角色id',
  `permission_id` int(11) NOT NULL DEFAULT '0' COMMENT '权限id',
  `default` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否默认菜单（菜单时有效，且只有一个） 1为默认、0为否',
  `create_at` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='系统权限角色关联表';
