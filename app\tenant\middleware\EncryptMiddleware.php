<?php
declare(strict_types = 1);

namespace app\tenant\middleware;

use app\tenant\exception\AccountException;
use app\tenant\service\EncryptionService;
use think\facade\Config;
use think\facade\Log;
use think\Request;

/**
 * API数据加密中间件
 * 实现请求数据的解密和响应数据的加密
 * <AUTHOR> <<EMAIL>>
 * @package app\tenant\middleware
 */
class EncryptMiddleware
{
    /**
     * 加密服务实例
     */
    protected EncryptionService $encryptionService;

    /**
     * 不需要加密的控制器
     */
    protected array $excludeControllers = [];

    /**
     * 不需要加密的路由
     */
    protected array $excludeRoutes = [];

    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->encryptionService  = EncryptionService::getInstance();
        $this->excludeControllers = Config::get('encryption.exclude_controllers', []);
        $this->excludeRoutes      = Config::get('encryption.exclude_routes', []);
    }

    /**
     * @throws \app\tenant\exception\AccountException
     */
    public function handle(Request $request, \Closure $next)
    {
        // 获取当前路由
        $route = $request->pathinfo();

        if (count(explode('/', $route)) === 2) {
            $route = strtolower(app()->http->getName()) . '/' . $route;
        }

        // 从路由中解析控制器名称
        $pathParts  = explode('/', $route);
        $controller = $pathParts[1] ?? '';

        // 如果无法获取控制器名称，则从请求对象中获取
        if (empty($controller)) {
            $controller = $request->controller();
        }

        // 检查是否需要加密处理
        if ($this->shouldEncrypt($controller, $route)) {
            try {
                // 检查是否需要解密请求数据
                if (Config::get('encryption.request_decrypt', true)) {
                    // 解密请求数据
                    $this->decryptRequest($request);
                }

                // 获取响应
                $response = $next($request);

                // 加密响应数据
                return $this->encryptResponse($response);
            } catch (\Exception $e) {
                Log::error('加密中间件异常: ' . $e->getMessage());

                throw new AccountException('数据加密处理失败', AccountException::INVALID_PARAM);
            }
        }

        return $next($request);
    }

    /**
     * 检查是否需要加密处理
     * @param string $controller
     * @param string $route
     * @return bool
     */
    protected function shouldEncrypt(string $controller, string $route): bool
    {
        // 排除指定控制器
        if (in_array($controller, $this->excludeControllers)) {
            return false;
        }

        // 排除指定路由
        if (in_array($route, $this->excludeRoutes)) {
            return false;
        }

        return true;
    }

    /**
     * 解密请求数据
     * @param Request $request
     * @throws AccountException
     */
    protected function decryptRequest(Request $request): void
    {
        $input = $request->getInput();

        if (empty($input)) {
            return;
        }

        $data = json_decode($input, true);

        if (!isset($data['encrypted_data']) || !isset($data['encrypted_key'])) {
            throw new AccountException('加密数据格式错误', AccountException::INVALID_PARAM);
        }

        try {
            // 使用RSA私钥解密AES密钥
            $aesKey = $this->encryptionService->rsaDecrypt($data['encrypted_key']);

            // 使用AES密钥解密业务数据
            $decryptedData = $this->encryptionService->aesDecrypt($data['encrypted_data'], $aesKey);

            // 更新请求数据
            $request->setInput($decryptedData);
        } catch (\Exception $e) {
            Log::error('请求解密失败: ' . $e->getMessage());

            throw new AccountException('数据解密失败', AccountException::INVALID_PARAM);
        }
    }

    /**
     * 加密响应数据
     * @param mixed $response
     * @throws AccountException
     */
    protected function encryptResponse($response)
    {
        if (!method_exists($response, 'getContent')) {
            return $response;
        }

        $content = $response->getContent();

        if (empty($content)) {
            return $response;
        }

        try {
            // 生成随机AES密钥
            $aesKey = $this->encryptionService->generateAesKey();

            // 使用AES加密响应数据
            $encryptedData = $this->encryptionService->aesEncrypt($content, $aesKey);

            // 使用RSA公钥加密AES密钥
            $encryptedKey = $this->encryptionService->rsaEncrypt($aesKey);

            // 构造加密响应
            $encryptedResponse = json_encode([
                'encrypted_data' => $encryptedData,
                'encrypted_key'  => $encryptedKey,
            ]);

            $response->content($encryptedResponse);
        } catch (\Exception $e) {
            Log::error('响应加密失败: ' . $e->getMessage());

            throw new AccountException('响应加密失败: ' . $e->getMessage(), AccountException::INVALID_PARAM);
        }

        return $response;
    }
}
