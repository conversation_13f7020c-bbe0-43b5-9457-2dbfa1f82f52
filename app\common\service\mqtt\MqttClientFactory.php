<?php
declare(strict_types = 1);

namespace app\common\service\mqtt;

use think\facade\Config;

/**
 * MQTT客户端工厂类
 * 用于创建独立的MQTT客户端实例
 * <AUTHOR> <<EMAIL>>
 * @package app\common\service
 */
class MqttClientFactory
{
    /**
     * MQTT配置
     * @var array
     */
    private array $config = [];

    /**
     * 协议版本，默认使用v5
     * @var int
     */
    private int $protocolVersion;

    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->config          = Config::get('mqtt');
        $this->protocolVersion = intval($this->config['protocol_version'] ?? 5);
    }

    /**
     * 创建MQTT客户端
     * @param string $clientIdSuffix 客户端ID后缀，用于区分不同的客户端
     * @param array $willMessage 遗嘱消息，可根据需要配置
     * @return MqttClient 新的MQTT客户端实例
     */
    public function create(string $clientIdSuffix = '', array $willMessage = []): MqttClient
    {
        // 创建新的MQTT客户端实例
        $client = new MqttClient($this->config, $clientIdSuffix);

        // 连接MQTT服务器
        $client->connect($willMessage);

        return $client;
    }
}
