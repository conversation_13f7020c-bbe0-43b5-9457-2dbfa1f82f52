<?php
declare(strict_types = 1);
namespace app\system\controller;

use app\system\repository\ConfigureGroupRepository;
use app\system\validate\ConfigureGroup as ConfigureGroupValidate;
use think\App;
use think\exception\ValidateException;
use think\Response;

/**
 * 配置分类控制器
 * <AUTHOR> <<EMAIL>>
 * @package app\system\controller
 */
class ConfigureGroup extends BaseController
{
    protected ConfigureGroupRepository $repository;

    public function __construct(App $app, ConfigureGroupRepository $repository)
    {
        parent::__construct($app);

        // 指定对应仓储类
        $this->repository = $repository;
    }

    /**
     * 分类列表方法
     * @return \think\Response
     */
    public function list(): Response
    {
        $params = $this->request->param();

        $result = $this->repository->pageList($params, 1, 100);

        $data = $result['list']->isEmpty() === false ? $result['list']->toArray() : [];

        return $this->success($data);
    }

    /**
     * 添加方法
     * @return \think\Response
     */
    public function add(): Response
    {
        $params = $this->request->param();

        try {
            validate(ConfigureGroupValidate::class)->scene('add')->check($params);
        } catch (ValidateException $e) {
            return $this->error($e->getError());
        }

        $data = [
            'name'        => isset($params['name']) ? trim($params['name']) : '',
            'label'       => isset($params['label']) ? trim($params['label']) : '',
            'icon'        => isset($params['icon']) ? trim($params['icon']) : '',
            'description' => isset($params['description']) ? trim($params['description']) : '',
            'sort'        => isset($params['sort']) ? intval($params['sort']) : 0,
            'status'      => isset($params['status']) ? intval($params['status']) : 1,
            'hidden'      => isset($params['hidden']) ? intval($params['hidden']) : 0,
            'is_system'   => 0,
            'create_at'   => time(),
        ];

        if ($this->repository->addGroup($data)) {
            // 写入事件日志
            $this->event_log('添加分组', $params, []);

            return $this->success('添加成功');
        }

        return $this->error('添加失败');
    }

    /**
     * 编辑方法
     * @return \think\Response
     */
    public function edit(): Response
    {
        $params = $this->request->param();

        try {
            validate(ConfigureGroupValidate::class)->scene('edit')->check($params);
        } catch (ValidateException $e) {
            return $this->error($e->getError());
        }

        $info = $this->repository->getInfo($params['id']);

        if (empty($info)) {
            return $this->error('分组不存在');
        }

        if ($info['status'] == -1) {
            return $this->error('该分组已删除，无法操作');
        }

        $data = [
            'name'        => isset($params['name']) ? trim($params['name']) : '',
            'label'       => isset($params['label']) ? trim($params['label']) : '',
            'icon'        => isset($params['icon']) ? trim($params['icon']) : '',
            'description' => isset($params['description']) ? trim($params['description']) : '',
            'sort'        => isset($params['sort']) ? intval($params['sort']) : 0,
            'status'      => isset($params['status']) ? intval($params['status']) : 1,
            'hidden'      => isset($params['hidden']) ? intval($params['hidden']) : 0,
            'update_at'   => time(),
        ];

        if ($this->repository->editGroup($params['id'], $data)) {
            // 写入事件日志
            $this->event_log('更新分组', $params, []);

            return $this->success('更新成功');
        }

        return $this->error('更新失败');
    }

    /**
     * 设置状态（禁用启用）
     * @return \think\Response
     */
    public function status(): Response
    {
        $params = $this->request->param();

        try {
            validate(ConfigureGroupValidate::class)->scene('status')->check($params);
        } catch (ValidateException $e) {
            return $this->error($e->getError());
        }

        if ($this->repository->setStatus($params['id'], $params['status'])) {
            // 写入事件日志
            $this->event_log('设置状态', $params, []);

            return $this->success($params['status'] == 0 ? '禁用成功' : '启用成功');
        }

        return $this->error('操作失败');
    }

    /**
     * 删除方法（软删除）
     * @return \think\Response
     */
    public function delete(): Response
    {
        $params = $this->request->param();

        try {
            validate(ConfigureGroupValidate::class)->scene('delete')->check($params);
        } catch (ValidateException $e) {
            return $this->error($e->getError());
        }

        if ($this->repository->recycle($params['id'])) {
            // 写入事件日志
            $this->event_log('删除分组', $params, []);

            return $this->success('删除成功');
        }

        return $this->error('删除失败');
    }

    /**
     * 设置隐藏状态
     * @return \think\Response
     */
    public function hidden(): Response
    {
        $params = $this->request->param();

        try {
            validate(ConfigureGroupValidate::class)->scene('hidden')->check($params);
        } catch (ValidateException $e) {
            return $this->error($e->getError());
        }

        if ($this->repository->setHidden($params['id'], $params['hidden'])) {
            // 写入事件日志
            $this->event_log('设置隐藏状态', $params, []);

            return $this->success($params['hidden'] == 0 ? '显示成功' : '隐藏成功');
        }

        return $this->error('操作失败');
    }
}
