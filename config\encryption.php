<?php
declare(strict_types = 1);

/**
 * 接口数据加密配置
 */
return [
    // RSA密钥对配置
    'rsa_public_key'  => '../config/cert/public_key.pem',  // 从环境变量获取公钥
    'rsa_private_key' => '../config/cert/private_key.pem', // 从环境变量获取私钥

    // AES配置
    'aes_key_size' => 128, // AES密钥长度（位）
    'aes_mode'     => 'CBC',   // AES加密模式

    // 请求时间窗口配置（毫秒）
    'request_timeout' => 300000, // 5分钟

    // 是否开启请求解密
    'request_decrypt' => false,

    // 随机数（nonce）配置
    'nonce_length' => 32,   // nonce长度
    'nonce_ttl'    => 300,     // nonce有效期（秒）

    // 不需要加密的路由配置
    'exclude_routes' => [
        'tenant/auth/login',
        'tenant/auth/logout',
        'tenant/auth/refreshToken',
    ],

    // 不需要加密的控制器
    'exclude_controllers' => [
        'manage',
    ],
];
