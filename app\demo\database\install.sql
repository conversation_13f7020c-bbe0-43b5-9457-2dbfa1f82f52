-- ----------------------------
-- Table structure for demo_config
-- ----------------------------
DROP TABLE IF EXISTS `demo_config`;
CREATE TABLE `demo_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `name` varchar(50) NOT NULL DEFAULT '' COMMENT '配置名称',
  `value` text COMMENT '配置值',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态(1:正常,0:禁用)',
  `create_at` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_at` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='示例配置表';

-- ----------------------------
-- Table structure for demo_data
-- ----------------------------
DROP TABLE IF EXISTS `demo_data`;
CREATE TABLE `demo_data` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `title` varchar(100) NOT NULL DEFAULT '' COMMENT '标题',
  `content` text COMMENT '内容',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态(1:正常,0:禁用)',
  `create_at` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_at` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='示例数据表'; 