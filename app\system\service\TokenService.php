<?php
declare(strict_types = 1);

namespace app\system\service;

use app\system\constant\TokenConstant;
use Firebase\JWT\BeforeValidException;
use Firebase\JWT\ExpiredException;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Firebase\JWT\SignatureInvalidException;
use InvalidArgumentException;
use TypeError;

/**
 * Token服务类
 * <AUTHOR> <<EMAIL>>
 * @package app\system\service
 */
class TokenService extends BaseService
{
    // 签名算法
    public const JWT_ALG = 'HS256'; // JWT签名算法

    /**
     * 从.env读取jwt密钥配置
     * @return string
     */
    protected static function getJwtKey(): string
    {
        return env('JWT.JWT_KEY', '550e8400-e29b-41d4-a716-446655440000'); // 默认值作为fallback
    }

    /**
     * 获取token过期时间配置
     * @param string $type
     * @return int
     */
    public static function getTokenExpire(string $type = 'access_token'): int
    {
        $expires = [
            'access_token'  => (int)env('JWT.ACCESS_TOKEN_EXPIRE', 2 * 24 * 60 * 60),  // 默认48小时，单位秒
            'refresh_token' => (int)env('JWT.REFRESH_TOKEN_EXPIRE', 7 * 24 * 60 * 60), // 默认7天，单位秒
        ];

        return $expires[$type] ?? $expires['access_token'];
    }

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 签发生成token
     * @param string $type 是access_token还是refresh_token
     * @param array $data 携带数据
     * @return string
     */
    public static function issueToken(string $type = '', array $data = []): string
    {
        // 当前登录时间
        $time = time();

        $payload = [
            'iss' => 'system', // JWT的签发者
            'sub' => 'user', // 面向用户
            'iat' => $time, // 签发时间
            'exp' => $time + self::getTokenExpire($type), // 过期时间
            'nbf' => $time, // 定义在什么时间之前，该jwt都是不可用的
            'jti' => md5(uniqid($type) . $time), // token唯一标识
            'uid' => $data['uid'],  // 把用户id传过去即可
            //'cip' => FingerService::getClientIp(1), // 使用自定义方法获取IP
            //'device' => request()->header('user-agent'), // 建议加入设备信息
            //'typ'  => $type, // 接收的一方
            //'data' => $data,  // 携带数据
        ];

        // 生成用户token
        return JWT::encode($payload, self::getJwtKey(), self::JWT_ALG);
    }

    /**
     * 验证token合法性并返回包含信息
     * @param string|null $token 要验证的token
     * @param string $type
     * @return array
     */
    public static function verifyToken(?string $token = null, string $type = 'access_token'): array
    {
        try {
            $info = JWT::decode($token, new Key(self::getJwtKey(), self::JWT_ALG));

            return std_class_object_to_array($info);
        } catch (ExpiredException $e) {
            // 已过期的时候应该重新签发token
            if ($type === 'refresh_token') {
                return TokenConstant::REFRESH_TOKEN_EXPIRED;
            }

            return TokenConstant::ACCESS_TOKEN_EXPIRED;
        } catch (InvalidArgumentException $e) {
            return TokenConstant::TOKEN_INVALID_ARGUMENT;
        } catch (SignatureInvalidException $e) {
            return TokenConstant::TOKEN_SIGNATURE_INVALID;
        } catch (BeforeValidException $e) {
            return TokenConstant::TOKEN_BEFORE_VALID;
        } catch (TypeError $e) {
            return TokenConstant::TOKEN_TYPE_ERROR;
        } catch (\Exception $e) {
            return TokenConstant::TOKEN_VERIFY_INVALID;
        }
    }

    /**
     * 解析JWT token，可选是否验证过期时间
     * @param string|null $token JWT token字符串
     * @param bool $validateExpiration 是否验证过期时间，默认false
     * @return array|null 解析后的数据，解析失败返回null
     */
    public static function parseToken(?string $token = null, bool $validateExpiration = false): ?array
    {
        if (empty($token)) {
            $token = self::getBearerToken();
        }

        if (empty($token)) {
            return null;
        }

        try {
            $tks = explode('.', $token);

            if (count($tks) != 3) {
                return null;
            }

            // 先解析不验证签名，只获取payload部分
            $payload = JWT::jsonDecode(JWT::urlsafeB64Decode($tks[1]));

            // 如果不需要验证过期时间，直接返回解析结果
            if (!$validateExpiration) {
                return std_class_object_to_array($payload);
            }

            // 需要验证过期时间时，使用完整的decode
            $decoded = JWT::decode($token, new Key(self::getJwtKey(), self::JWT_ALG));

            return std_class_object_to_array($decoded);
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * 统一管理用户token中的payload数据
     * @param int $userId
     * @return array
     */
    public static function setTokenPayloadData(int $userId): array
    {
        return [
            'uid' => $userId,
        ];
    }

    /**
     * 获取客户端header中传过来的Authorization参数值
     * @return null|string
     */
    public static function getBearerToken(): ?string
    {
        $authorization = request()->header(AuthService::HEADER_AUTHORIZATION_KEY) ?: null;

        if (!empty($authorization)) {
            if (preg_match('/Bearer\s(\S+)/', $authorization, $matches)) {
                return $matches[1];
            }
        }

        return null;
    }
}
