# API安全机制指南

## 概述

本文档详细说明了API接口的安全机制，包括签名验证、数据加密等核心安全特性，参考了小桔加油等成熟API的安全实践。

## 核心安全特性

### 1. 防重放攻击

每个API请求都需要包含以下防重放参数：

- `timestamp`: 请求时间戳（毫秒）
- `nonce`: 随机字符串，建议32位

服务端会：
- 校验timestamp是否在允许的时间窗口内（如5分钟）
- 校验nonce是否重复使用

### 2. 数据加密

采用AES+RSA混合加密机制：

1. AES加密流程
   - 生成随机AES密钥
   - 使用AES-256-CBC模式加密请求数据
   - 使用PKCS7进行填充

2. RSA密钥交换
   - 使用RSA公钥加密AES密钥
   - 将加密后的AES密钥随请求一起发送

### 3. 签名验证

使用MD5进行签名验证：

1. 签名参数列表：
   - 请求参数（按字母升序）
   - timestamp
   - nonce
   - app_key

2. 签名步骤：
   - 将参数按字母升序排序
   - 拼接成key=value形式
   - 添加应用密钥（app_secret）
   - 计算MD5值

## 请求示例

```http
POST /api/endpoint
Content-Type: application/json

{
  "data": "AES加密后的业务数据",
  "key": "RSA加密后的AES密钥",
  "timestamp": 1672502400000,
  "nonce": "8f7d56a1c3b2e4f9a8d7c6b5e4f3a2d1",
  "sign": "md5签名结果"
}
```

## 安全最佳实践

1. 密钥管理
   - 定期轮换RSA密钥对
   - 安全存储app_secret
   - 避免密钥硬编码

2. 传输安全
   - 强制使用HTTPS
   - 启用HSTS
   - 配置适当的TLS版本

3. 异常处理
   - 记录签名验证失败的请求
   - 对频繁失败的IP进行限制
   - 返回合适的错误信息

## 与现有认证体系集成

本安全机制与现有的租户认证体系配合使用：

1. 接口分级
   - 普通接口：仅需Token认证
   - 高敏感接口：需要额外的签名验证
   - 特殊接口：需要完整的加密+签名机制

2. Token集成
   - 在签名参数中包含access_token
   - 同时验证Token有效性和请求签名

3. 权限控制
   - 继承现有的RBAC权限体系
   - 可对加密接口单独配置权限
