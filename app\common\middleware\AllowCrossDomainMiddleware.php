<?php
namespace app\common\middleware;

use Closure;
use think\Request;
use think\Response;

/**
 * 自定义跨域中间件
 * <AUTHOR> <<EMAIL>>
 * @package app\common\middleware
 */
class AllowCrossDomainMiddleware
{
    /**
     * 跨域处理
     * @param Request $request
     * @param \Closure $next
     * @param array|null $header
     * @return Response
     */
    public function handle(Request $request, Closure $next, ?array $header = []): Response
    {
        if ($request->isOptions()) {
            return resp_header();
        }

        return resp_header($next($request));
    }
}
