<?php
declare(strict_types = 1);

namespace app\common\middleware;

use app\common\service\RequestService;
use think\facade\Log;

/**
 * 生成唯一的requestId加入到响应头中（用于用户请求追踪）
 * <AUTHOR> <<EMAIL>>
 * @package app\common\middleware
 */
class RequestIdMiddleware
{
    protected ?string $requestId;

    public function handle($request, \Closure $next)
    {
        $responseData = [];

        // 获取前端传来的请求ID
        if (RequestService::ENABLE_REQUEST_ID) {
            $this->requestId    = $request->header(strtolower(RequestService::HEADER_REQUEST_ID_KEY)) ?? $this->generateRequestId();
            $request->requestId = $this->requestId;

            $responseData[RequestService::HEADER_REQUEST_ID_KEY] = $this->requestId;

            if (RequestService::ENABLE_SEQUENCE_NUM) {
                $sequenceNum          = (int)($request->header(strtolower(RequestService::HEADER_SEQUENCE_NUM_KEY), '0')) + 1;
                $request->sequenceNum = $sequenceNum;

                $responseData[RequestService::HEADER_REQUEST_ID_KEY] = $sequenceNum;
            }
        }

        $response = $next($request);

        if (!empty($responseData)) {
            $response->header($responseData);

            // 增强日志记录
            Log::info('Request', [
                'request_id'   => $requestId   ?? null,
                'sequence_num' => $sequenceNum ?? 0,  // 请求序号
                'method'       => $request->method(),
                'url'          => $request->url(true),
                'ip'           => $request->ip(),
                'params'       => $request->param(),
                'referer'      => $request->header('referer'),  // 来源页面
                'user_agent'   => $request->header('user-agent'),
            ]);
        }

        return $response;
    }

    /**
     * 生成请求ID（uuid）
     * @return string
     */
    protected function generateRequestId(): string
    {
        return sprintf(
            '%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0xffff)
        );
    }
}
