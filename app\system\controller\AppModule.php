<?php
declare(strict_types = 1);

namespace app\system\controller;

use app\system\service\AppModuleService;
use think\App;
use think\Response;

/**
 * 应用模块控制器
 * <AUTHOR> <<EMAIL>>
 * @package app\system\controller
 */
class AppModule extends BaseController
{
    protected AppModuleService $appModuleService;

    public function __construct(App $app, AppModuleService $appModuleService)
    {
        parent::__construct($app);
        $this->appModuleService = $appModuleService;
    }

    /**
     * 应用市场
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return Response
     */
    public function store(): Response
    {
        $params   = $this->request->param();
        $pageNo   = $this->request->param('page', 1, 'intval');
        $pageSize = $this->request->param('pageSize', 20, 'intval');

        // 获取应用市场列表
        $result = (new AppRepository())->getStoreList($params, $pageNo, $pageSize);

        return $this->success($result);
    }

    /**
     * 应用列表
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return \think\Response
     */
    public function list(): Response
    {
        $page     = input('page/d', 1);
        $pageSize = input('pageSize/d', 15);

        $list = $this->appModuleService->getList([], $page, $pageSize);

        return $this->success($list);
    }

    /**
     * 安装应用
     * @return \think\Response
     */
    public function install(): Response
    {
        $appCode = input('code/s', '');

        if (!$appCode) {
            return $this->error('应用标识不能为空');
        }

        try {
            $this->appModuleService->install($appCode);

            return $this->success('安装成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 卸载应用
     * @return \think\Response
     */
    public function uninstall(): Response
    {
        $appCode = input('code/s', '');

        if (!$appCode) {
            return $this->error('应用标识不能为空');
        }

        try {
            $this->appModuleService->uninstall($appCode);

            return $this->success('卸载成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 启用应用
     * @return \think\Response
     */
    public function enable(): Response
    {
        $appCode = input('code/s', '');

        if (!$appCode) {
            return $this->error('应用标识不能为空');
        }

        try {
            $this->appModuleService->enable($appCode);

            return $this->success('启用成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 禁用应用
     * @return \think\Response
     */
    public function disable(): Response
    {
        $appCode = input('code/s', '');

        if (!$appCode) {
            return $this->error('应用标识不能为空');
        }

        try {
            $this->appModuleService->disable($appCode);

            return $this->success('禁用成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 升级应用
     * @return \think\Response
     */
    public function upgrade(): Response
    {
        $appCode = input('code/s', '');
        $version = input('version/s', '');

        if (!$appCode) {
            return $this->error('应用标识不能为空');
        }

        if (!$version) {
            return $this->error('版本号不能为空');
        }

        try {
            $this->appModuleService->upgrade($appCode, $version);

            return $this->success('升级成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}
