<?php
declare(strict_types = 1);

namespace app\tenant\service\login;

use app\tenant\model\User as UserModel;

/**
 * 登录策略接口
 * 定义了租户系统中所有登录策略的标准接口
 * 包含以下主要功能：
 * - 提供统一的登录验证方法
 * - 支持多种登录方式的扩展
 * - 定义获取登录类型的方法
 * <AUTHOR> <<EMAIL>>
 * @package app\tenant\service\login
 */
interface LoginStrategyInterface
{
    /**
     * 执行登录验证
     * @param array $credentials 登录凭证
     * @return UserModel|null 返回用户对象或null
     */
    public function login(array $credentials): ?UserModel;

    /**
     * 获取登录类型
     * @return string
     */
    public function getType(): string;
}
