<?php
// +----------------------------------------------------------------------
// | 控制台配置
// +----------------------------------------------------------------------
use app\system\command\CleanExpiredOnlineCommand;
use app\tenant\command\ClientStatusMonitorCommand;
use app\tenant\command\DeviceStatusMonitorCommand;
use app\tenant\command\test\ClientStatusSimulatorCommand;
use app\tenant\command\test\DeviceSimulatorCommand;
use app\tenant\command\test\RealDeviceSimulatorCommand;
use app\tenant\command\test\RedisMonitorTestCommand;

return [
    // 指令定义
    'commands' => [
        // 消息队列消费者计划任务脚本
        'system:cleanExpiredOnline' => CleanExpiredOnlineCommand::class,
        /**
         * 客户端状态监控命令
         * 监听所有租户的客户端状态
         * php think tenant:clientStatusMonitor
         * 监听指定租户的客户端状态
         * php think tenant:clientStatusMonitor -t shengli
         */
        'tenant:clientStatusMonitor' => ClientStatusMonitorCommand::class,

        /**
         * 客户端状态模拟器命令
         * 使用默认参数（租户：shengli，客户端数量：5）
         * php think tenant:clientStatusSimulator
         * 指定租户和客户端数量
         * php think tenant:clientStatusSimulator -t shengli -c 10
         */
        'tenant:clientStatusSimulator' => ClientStatusSimulatorCommand::class,

        // 设备模拟器命令
        'tenant:deviceSimulator' => DeviceSimulatorCommand::class,

        /**
         * 真实设备模拟器命令
         * 使用默认参数（租户：shengli，设备：10001,10003,10005,10007,10009）
         * php think tenant:realDeviceSimulator
         * 指定租户
         * php think tenant:realDeviceSimulator -t shengli
         * 指定设备ID列表
         * php think tenant:realDeviceSimulator -d 10001,10003,10005
         */
        'tenant:realDeviceSimulator' => RealDeviceSimulatorCommand::class,

        /**
         * 设备状态监控命令
         * 监听设备心跳，跟踪设备在线状态
         * php think tenant:deviceStatusMonitor
         * 指定租户
         * php think tenant:deviceStatusMonitor -t shengli
         */
        'tenant:deviceStatusMonitor' => DeviceStatusMonitorCommand::class,
        /**
         * Redis监控测试命令
         * php think tenant:redisMonitorTest
         */
        'tenant:redisMonitorTest' => RedisMonitorTestCommand::class,
    ],
];
