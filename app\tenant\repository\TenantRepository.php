<?php
declare(strict_types = 1);

namespace app\tenant\repository;

use app\tenant\model\Tenant as TenantModel;
use think\facade\Cache;
use think\facade\Db;

/**
 * 租户数据仓库类
 * 负责租户相关的数据访问和操作，包括租户状态的缓存管理
 * <AUTHOR> <<EMAIL>>
 * @package app\tenant\repository
 */
class TenantRepository extends BaseRepository
{
    /**
     * 缓存前缀
     */
    public const TENANT_STATUS_CACHE_PREFIX = 'tenant_status_';

    /**
     * 缓存时间（秒）
     */
    public const TENANT_STATUS_CACHE_TTL = 1800; // 30分钟

    /**
     * 构造方法
     */
    public function __construct()
    {
        // 指定当前模型类名而非实例，避免条件累积问题
        parent::__construct(TenantModel::class);
    }

    /**
     * 获取租户列表（支持高级筛选）
     * @param array $params 查询参数
     * @param int $pageNo 页码
     * @param int $pageSize 每页数量
     * @return array
     */
    public function getList(array $params = [], int $pageNo = 1, int $pageSize = 20): array
    {
        // 设置基础查询条件 - 非删除数据
        $this->setCondition([
            ['status', '>=', 0],
        ]);

        // 设置搜索条件
        if (!empty($params['keyword'])) {
            $this->setCondition([
                function($query) use ($params) {
                    $query->whereOr([
                        ['name', 'LIKE', "%{$params['keyword']}%"],
                        ['code', 'LIKE', "%{$params['keyword']}%"],
                        ['domain', 'LIKE', "%{$params['keyword']}%"],
                        ['contact_person', 'LIKE', "%{$params['keyword']}%"],
                        ['contact_phone', 'LIKE', "%{$params['keyword']}%"],
                        ['description', 'LIKE', "%{$params['keyword']}%"],
                    ]);
                },
            ]);
        }

        // 复用通用分页方法
        return $this->pageList($params, $pageNo, $pageSize);
    }

    /**
     * 添加租户
     * @param array $data 租户数据
     * @throws \Exception
     * @return array
     */
    public function addTenant(array $data): array
    {
        Db::startTrans();

        try {
            $tenantId = $this->model->addTenant($data);

            if ($tenantId) {
                // 默认管理员信息
                $defaultAdmin = [
                    'username' => 'root',
                    'password' => generateRandomPassword(8), // 生成随机8位密码
                ];

                // 创建默认租户管理员
                $result = (new UserRepository())->createTenantAdmin($tenantId, md5($defaultAdmin['password']), $defaultAdmin['username']);

                if ($result) {
                    Db::commit();

                    return [
                        'status'     => 1,
                        'tenantId'   => $tenantId,
                        'tenantCode' => $data['code'],
                        'username'   => $defaultAdmin['username'],
                        'password'   => $defaultAdmin['password'],
                    ];
                }
            }

            Db::rollback();

            return [
                'status' => 0,
            ];
        } catch (\Exception $e) {
            Db::rollback();

            throw $e;
        }
    }

    /**
     * 更新租户信息
     * @param int $id 租户ID
     * @param array $data 更新数据
     * @return bool
     */
    public function editTenant(int $id, array $data): bool
    {
        return $this->model->editTenant($id, $data);
    }

    /**
     * 获取正常租户列表
     * @return array
     */
    public function getActiveTenantList(): array
    {
        return $this->model->getActiveList();
    }

    /**
     * 根据租户编码查询租户信息
     * @param string $code 租户编码
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return TenantModel|null
     */
    public function findByCode(string $code): ?TenantModel
    {
        return $this->model->where([
            ['code', '=', $code],
            ['status', '=', 1],
        ])->find();
    }

    /**
     * 检查租户状态
     * @param string $tenant_code 租户编码
     * @param bool $useCache 是否使用缓存
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return array
     */
    public function checkTenantStatus(string $tenant_code, bool $useCache = true): array
    {
        $cacheKey = self::TENANT_STATUS_CACHE_PREFIX . $tenant_code;

        if ($useCache) {
            $tenantId = Cache::get($cacheKey);

            if ($tenantId !== null) {
                return [
                    'status'    => 1,
                    'tenant_id' => (int)$tenantId,
                ];
            }
        }

        // 查询租户信息
        $tenant = $this->model->where([
            ['code', '=', $tenant_code],
        ])->find();

        if (!$tenant) {
            return [
                'status'  => 0,
                'message' => '租户不存在',
            ];
        }

        if ($tenant->status === -1) {
            return [
                'status'  => 0,
                'message' => '租户已删除',
            ];
        }

        if ($tenant->status === 0) {
            return [
                'status'  => 0,
                'message' => '租户已禁用',
            ];
        }

        if ($tenant->expire_time && strtotime($tenant->expire_time) < time()) {
            return [
                'status'  => 0,
                'message' => '租户已过期',
            ];
        }

        // 验证成功返回租户ID
        $tenantId = $tenant->id;

        if ($useCache) {
            Cache::set($cacheKey, $tenantId, self::TENANT_STATUS_CACHE_TTL);
        }

        return [
            'status'    => 1,
            'tenant_id' => (int)$tenantId,
        ];
    }
}
