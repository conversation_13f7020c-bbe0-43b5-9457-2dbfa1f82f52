<?php
declare(strict_types = 1);

namespace app\system\middleware;

use app\system\constant\AppModuleConstant;
use app\system\service\AppModuleService;
use think\Request;
use think\Response;

/**
 * 应用模块检测中间件
 * <AUTHOR> <<EMAIL>>
 * @package app\system\middleware
 */
class AppModuleCheckMiddleware
{
    protected AppModuleService $appModuleService;

    public function __construct(AppModuleService $appModuleService)
    {
        $this->appModuleService = $appModuleService;
    }

    /**
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\DataNotFoundException
     */
    public function handle(Request $request, \Closure $next): Response
    {
        // 获取当前应用名称
        $app = explode('/', $request->pathinfo())[0];

        // 免检模块直接放行
        if (empty($request->pathinfo()) || in_array($app, AppModuleConstant::NO_CHECK_APPS)) {
            return $next($request);
        }

        // 检查应用模块状态
        $status = $this->appModuleService->checkAppModuleStatus($app);

        // 如果应用未安装，直接返回错误
        if (!$status['installed']) {
            return json([
                'code' => AppModuleConstant::APP_NOT_INSTALLED['code'],
                'msg'  => AppModuleConstant::APP_NOT_INSTALLED['message'],
                'data' => null,
            ]);
        }

        // 如果应用已安装但被禁用
        if (!$status['enabled']) {
            return json([
                'code' => AppModuleConstant::APP_DISABLED['code'],
                'msg'  => AppModuleConstant::APP_DISABLED['message'],
                'data' => null,
            ]);
        }

        return $next($request);
    }
}
