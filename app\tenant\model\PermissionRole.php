<?php
declare(strict_types = 1);

namespace app\tenant\model;

/**
 * 权限角色关联模型
 * <AUTHOR> <<EMAIL>>
 * @package app\tenant\model
 */
class PermissionRole extends BaseModel
{
    protected $name = 'tenant_permission_role';

    protected $field = [];

    /**
     * 获取指定角色的节点ids
     * @param mixed $role_ids
     * @return array
     */
    public static function getPermissions(mixed $role_ids): array
    {
        $role_ids_arr = is_array($role_ids) ? $role_ids : [$role_ids];

        if (empty($role_ids) || empty($role_ids_arr)) {
            return [];
        }

        $data = self::where('role_id', 'IN', $role_ids_arr)->field('permission_id')->distinct(true)->select()->toArray();

        if (empty($data)) {
            return [];
        }

        $permission = array_unique(array_column($data, 'permission_id'));

        sort($permission);

        return $permission;
    }

    /**
     * 设置角色组节点授权
     * @param int $role_id
     * @param mixed $permission_id
     * @return bool
     */
    public static function setPermission(int $role_id, mixed $permission_id): bool
    {
        if (is_array($permission_id)) {
            $permission_id_arr = $permission_id;
        } else {
            $permission_id_arr = explode(',', (string)$permission_id);
        }

        $permission_id_arr = $permission_id_arr ? array_unique($permission_id_arr) : [];

        sort($permission_id_arr);

        $data = [];

        foreach ($permission_id_arr as $key => $value) {
            if (empty($value)) {
                continue;
            }

            $data[$key]['role_id']       = intval($role_id);
            $data[$key]['permission_id'] = intval($value);
            $data[$key]['default']       = 0;
            $data[$key]['create_at']     = time();
        }

        $count = self::where('role_id', $role_id)->count();

        // 如果原来该角色有对应的节点，将其删除
        if ($count > 0) {
            $handle = self::where('role_id', $role_id)->delete();

            if ($handle == 0) {
                return false;
            }
        }

        if ($data) {
            return (bool)self::insertAll($data);
        }

        return true;
    }

    /**
     * 根据权限节点ids获取角色组ids
     * @param array $permission_ids
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return array
     */
    public static function getRolesByPermission(array $permission_ids = []): array
    {
        if ($permission_ids) {
            $list = self::where([['permission_id', 'IN', $permission_ids]])->field('role_id')->select()->toArray();

            return array_unique(array_column($list, 'role_id'));
        }

        return [];
    }
}
