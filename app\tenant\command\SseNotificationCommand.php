<?php
declare(strict_types = 1);

namespace app\tenant\command;

use app\common\service\mqtt\MqttService;
use app\tenant\service\SseService;
use Swoole\Coroutine;

use function Swoole\Coroutine\run;

use Swoole\Runtime;
use think\console\Input;
use think\console\input\Option;
use think\console\Output;

/**
 * SSE通知命令
 * 用于监听MQTT消息并通过SSE发送给客户端
 * <AUTHOR> <<EMAIL>>
 * @package app\tenant\command
 */
class SseNotificationCommand extends BaseCommand
{
    /**
     * MQTT配置
     * @var array
     */
    private array $config = [];

    /**
     * 进程锁文件路径
     * @var string
     */
    private string $lockFile = '';

    /**
     * MQTT服务实例
     * @var MqttService
     */
    protected MqttService $mqttService;

    /**
     * SSE服务实例
     * @var SseService
     */
    protected SseService $sseService;

    /**
     * 是否继续运行
     * @var bool
     */
    protected bool $running = true;

    public function __construct()
    {
        parent::__construct();

        // 获取tenant模块的mqtt配置
        $this->config = getAppConfig('mqtt', 'tenant');
        // 初始化进程锁文件路径
        $this->lockFile = runtime_path() . 'sse_notification.lock';
    }

    /**
     * 配置指令
     * php think tenant:sseNotification
     * php think tenant:sseNotification -t 10001
     * php think tenant:sseNotification --tenant 10001
     * @return void
     */
    protected function configure(): void
    {
        $this->setName('tenant:sseNotification')
            ->setDescription('启动SSE通知服务')
            ->addOption('tenant', 't', Option::VALUE_OPTIONAL, '指定租户代码，不指定则监听所有租户', null);
    }

    /**
     * 执行指令
     * @param Input $input
     * @param Output $output
     * @return void
     */
    protected function execute(Input $input, Output $output): void
    {
        // 检查是否已有实例在运行
        if (file_exists($this->lockFile)) {
            $pid = file_get_contents($this->lockFile);

            if ($pid && posix_kill((int)$pid, 0)) {
                $this->logError("已有一个SSE通知服务实例正在运行，进程ID：{$pid}");

                return;
            }

            // 如果进程不存在，删除锁文件
            unlink($this->lockFile);
        }

        // 创建进程锁
        file_put_contents($this->lockFile, getmypid());

        // 获取指定的租户代码
        $tenantCode = $input->getOption('tenant');

        Runtime::enableCoroutine();

        // 在协程环境中运行SSE通知服务
        run(function() use ($tenantCode) {
            // 初始化服务
            $this->mqttService = MqttService::getInstance();
            $this->sseService  = SseService::getInstance();

            if (!$this->mqttService->connect()) {
                $this->logError('MQTT服务连接失败');

                return;
            }

            // 注册信号处理
            pcntl_signal(SIGTERM, [$this, 'handleSignal']);
            pcntl_signal(SIGINT, [$this, 'handleSignal']);

            // 创建信号处理协程
            Coroutine::create(function() {
                while ($this->running) {
                    pcntl_signal_dispatch();
                    Coroutine::sleep(0.1); // 每100ms检查一次信号
                }
            });

            // 订阅SSE通知主题
            $this->subscribeToSseTopics($tenantCode);

            // 主循环
            while ($this->running) {
                Coroutine::sleep(1);
            }

            // 优雅退出
            $this->logWarning('SSE通知服务已停止');

            // 删除进程锁
            if (file_exists($this->lockFile)) {
                unlink($this->lockFile);
            }
        });
    }

    /**
     * 订阅SSE通知主题
     * @param string|null $tenantCode 指定的租户代码
     * @return void
     */
    protected function subscribeToSseTopics(?string $tenantCode): void
    {
        if ($tenantCode) {
            // 订阅指定租户的SSE通知主题
            $topic = "helio/tenant/{$tenantCode}/sse/notification/#";
            $this->mqttService->subscribe($topic, function($topic, $message) {
                $this->handleSseMessage($topic, $message);
            });
            $this->logInfo("已订阅租户 {$tenantCode} 的SSE通知主题");
        } else {
            // 订阅所有租户的SSE通知主题
            $topic = 'helio/tenant/+/sse/notification/#';
            $this->mqttService->subscribe($topic, function($topic, $message) {
                $this->handleSseMessage($topic, $message);
            });
            $this->logInfo('已订阅所有租户的SSE通知主题');
        }
    }

    /**
     * 处理SSE消息
     * @param string $topic 主题
     * @param string $message 消息内容
     * @return void
     */
    protected function handleSseMessage(string $topic, string $message): void
    {
        try {
            // 解析主题，提取租户代码
            // 主题格式: helio/tenant/{tenantCode}/sse/notification[/user/{userId}]
            $parts = explode('/', $topic);

            if (count($parts) < 5) {
                $this->logWarning("无效的SSE通知主题: {$topic}");

                return;
            }

            $tenantCode  = $parts[2];
            $messageData = json_decode($message, true);

            if (!$messageData) {
                $this->logWarning("无效的SSE消息格式: {$message}");

                return;
            }

            // 记录接收到的消息
            $this->logInfo("接收到SSE通知消息: 租户 {$tenantCode}, 主题 {$topic}");

            // 根据消息类型处理
            // 在实际应用中，这里会将消息转发到对应的SSE连接
            // 由于ThinkPHP默认不支持长连接保持，这里仅作为示例记录日志

            // 如果是用户特定消息
            if (strpos($topic, '/user/') !== false && isset($parts[6])) {
                $userId = (int)$parts[6];
                $this->logInfo("向用户 {$userId} 发送SSE消息: " . json_encode($messageData, JSON_UNESCAPED_UNICODE));
            } else {
                // 租户级别消息
                $this->logInfo("向租户 {$tenantCode} 发送SSE消息: " . json_encode($messageData, JSON_UNESCAPED_UNICODE));
            }
        } catch (\Exception $e) {
            $this->logError('处理SSE消息异常: ' . $e->getMessage());
        }
    }

    /**
     * 处理指定信号
     * @param int $signal
     */
    public function handleSignal(int $signal): void
    {
        switch ($signal) {
            case SIGTERM:
            case SIGINT:
                $this->running = false;
                break;
        }
    }
}
