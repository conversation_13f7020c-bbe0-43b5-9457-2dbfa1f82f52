<?php
declare(strict_types = 1);

namespace app\system\model;

use think\db\Query;
use think\model\relation\BelongsToMany;

/**
 * 用户组模型
 * <AUTHOR> <<EMAIL>>
 *@package app\system\model
 */
class UserGroup extends BaseModel
{
    protected $name = 'system_user_group';

    protected $field = [];

    /**
     * 关联用户
     * @return \think\model\relation\BelongsToMany
     */
    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, self::class, 'user_id', 'group_id');
    }

    /**
     * 查询作用域：启用状态的用户组
     * @param $query
     * @return \think\db\Query
     */
    public function scopeEnabled($query): Query
    {
        return $query->where('status', 1);
    }
}
