<?php
declare(strict_types = 1);
namespace app\system\controller;

use app\system\repository\UserRepository;
use app\system\service\AuthService;
use app\system\validate\User as UserValidate;
use think\App;
use think\exception\ValidateException;
use think\facade\Db;
use think\Response;

/**
 * 用户管理控制器
 * <AUTHOR> <<EMAIL>>
 * @package app\system\controller
 */
class User extends BaseController
{
    protected UserRepository $repository;

    public function __construct(App $app)
    {
        parent::__construct($app);

        // 指定对应仓储类
        $this->repository = new UserRepository();
    }

    /**
     * 管理员下拉列表
     * @return \think\Response
     */
    public function selects(): Response
    {
        $list = $this->repository->getSelects();

        return $this->success($list);
    }

    /**
     * 管理员列表
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return \think\Response
     */
    public function list(): Response
    {
        $params   = $this->request->param();
        $pageNo   = $this->request->param('page', 1, 'intval');
        $pageSize = $this->request->param('pageSize', 20, 'intval');
        $list     = $this->repository->getList($params, $pageNo, $pageSize);

        return $this->success($list);
    }

    /**
     * 添加用户
     * @return \think\Response
     */
    public function add(): Response
    {
        $params = $this->request->param();

        try {
            validate(UserValidate::class)->scene('add')->check($params);
        } catch (ValidateException $e) {
            // 验证失败 输出错误信息
            return $this->error($e->getError());
        }

        // 检测用户名是否存在
        if ($this->repository->checkExist('username', $params['username'])) {
            return $this->error('用户名已存在');
        }

        // 检测手机号是否存在
        if ($this->repository->checkExist('phone', $params['phone'])) {
            return $this->error('手机号已存在');
        }

        // 检测邮箱是否存在
        if ($this->repository->checkExist('email', $params['email'])) {
            return $this->error('邮箱号已存在');
        }

        $result = Db::transaction(function() use ($params) {
            // 生成随机盐值
            $password_salt = AuthService::generateSalt();

            // 创建用户
            $user = $this->repository->createData([
                'username' => isset($params['username']) ? trim($params['username']) : '',
                'realname' => isset($params['realname']) ? trim($params['realname']) : '',
                'password' => AuthService::generatePassword(md5(trim($params['password'])), $password_salt),
                // 生成密码（对用户输入的明文密码进行md5后再存入）
                'salt'                  => $password_salt,
                'phone'                 => isset($params['phone']) ? trim($params['phone']) : '',
                'email'                 => isset($params['email']) ? trim($params['email']) : '',
                'force_change_password' => isset($params['force_change_password']) ? intval($params['force_change_password']) : 0,
                // 是否强制修改密码
                'create_at' => time(),
                'is_super'  => 0,
                'status'    => 1,
            ]);

            if (!$user->id) {
                return false;
            }

            // 设置角色
            if (!empty($params['roles'])) {
                if (!$this->repository->setUserRoles($user->id, $params['roles'])) {
                    return false;
                }
            }

            // 设置部门
            if (!empty($params['depts'])) {
                if (!$this->repository->setUserDepartments($user->id, $params['depts'])) {
                    return false;
                }
            }

            return true;
        });

        if ($result === true) {
            // 写入事件日志
            $this->event_log('添加新职员', $params, []);

            return $this->success('添加成功');
        }

        return $this->error('添加失败');
    }

    /**
     * 编辑用户
     * @return \think\Response
     */
    public function edit(): Response
    {
        $params  = $this->request->param();
        $user_id = $params['id'] ?? 0;

        try {
            validate(UserValidate::class)->scene('edit')->check($params);
        } catch (ValidateException $e) {
            return $this->error($e->getError());
        }

        $userinfo = $this->repository->getInfo($user_id, 'id');

        if (empty($userinfo)) {
            return $this->error('用户不存在');
        }

        if ($userinfo['status'] == -1) {
            return $this->error('该用户已删除，无法操作');
        }

        // 检测用户名是否存在
        if (!empty($params['username'])) {
            if ($this->repository->checkExist('username', $params['username'], $user_id)) {
                return $this->error('用户名已存在');
            }
        }

        // 检测手机号是否存在
        if (!empty($params['phone'])) {
            if ($this->repository->checkExist('phone', $params['phone'], $user_id)) {
                return $this->error('手机号已存在');
            }
        }

        // 检测邮箱是否存在
        if (!empty($params['email'])) {
            if ($this->repository->checkExist('email', $params['email'], $user_id)) {
                return $this->error('邮箱号已存在');
            }
        }

        $data = [
            'realname'              => isset($params['realname']) ? trim($params['realname']) : $userinfo['realname'],
            'phone'                 => isset($params['phone']) ? trim($params['phone']) : $userinfo['phone'],
            'email'                 => isset($params['email']) ? trim($params['email']) : $userinfo['email'],
            'force_change_password' => isset($params['force_change_password']) ? intval($params['force_change_password']) : 0,
            'status'                => isset($params['status']) ? intval($params['status']) : $userinfo['status'],
            'update_at'             => time(),
        ];

        // 如果用户填写了密码，就修改密码
        if (!empty($params['password'])) {
            $data['salt']     = AuthService::generateSalt();
            $data['password'] = AuthService::generatePassword(md5(trim($params['password'])), $data['salt']);
        }

        $result = Db::transaction(function() use ($user_id, $params, $data) {
            // 更新用户基本信息
            if (!$this->repository->updateData($user_id, $data)) {
                return false;
            }

            // 设置用户角色
            if (!$this->repository->setUserRoles($user_id, $params['roles'] ?? [])) {
                return false;
            }

            // 设置用户部门
            if (!$this->repository->setUserDepartments($user_id, $params['depts'] ?? [])) {
                return false;
            }

            return true;
        });

        if ($result === true) {
            // 更新缓存里的用户信息
            AuthService::updateCacheUserInfo($user_id, $data);
            // 更新用户节点权限缓存
            AuthService::updateCacheUserNodesByUserIds($user_id);
            // 写入事件日志
            $this->event_log('编辑职员信息', $params, []);

            return $this->success('更新成功');
        }

        return $this->error('更新失败');
    }

    /**
     * 设置用户状态（禁用启用）
     * @return \think\Response
     */
    public function status(): Response
    {
        $params = $this->request->param();

        try {
            validate(UserValidate::class)->scene('status')->check($params);
        } catch (ValidateException $e) {
            return $this->error($e->getError());
        }

        $result = $this->repository->setStatusWithCache($params['id'], $params['status'], $this->getUserId());

        if ($result['code'] === 0) {
            $this->event_log('设置职员状态', $params, []);

            return $this->success($params['status'] == 0 ? '冻结成功' : '解冻成功');
        }

        return $this->error($result['message']);
    }

    /**
     * 删除用户（软删除）
     * @return \think\Response
     */
    public function delete(): Response
    {
        $params = $this->request->param();

        if (empty($params['id'])) {
            return $this->error('参数ID不能为空');
        }

        $result = $this->repository->setStatusWithCache($params['id'], -1, $this->getUserId());

        if ($result['code'] === 0) {
            $this->event_log('删除职员', $params, []);

            return $this->success('删除成功');
        }

        return $this->error($result['message']);
    }

    /**
     * 批量分配部门
     * @return \think\Response
     */
    public function setDepts(): Response
    {
        $params = $this->request->param();

        if (empty($params['id'])) {
            return $this->error('参数ID不能为空');
        }

        if (empty($params['depts'])) {
            return $this->error('部门ID不能为空');
        }
        $id_arr = is_array($params['id']) ? $params['id'] : explode(',', (string)$params['id']);

        foreach ($id_arr as $uid) {
            $this->repository->setUserDepartments($uid, $params['depts']);
        }
        // 写入事件日志
        $this->event_log('批量分配部门', $params, []);

        return $this->success('操作成功');
    }

    /**
     * 批量分配角色
     * @return \think\Response
     */
    public function setRoles(): Response
    {
        $params = $this->request->param();

        if (empty($params['id']) || empty($params['roles'])) {
            return $this->error('参数不完整');
        }

        $id_arr = is_array($params['id']) ? $params['id'] : explode(',', (string)$params['id']);

        foreach ($id_arr as $uid) {
            $this->repository->setRolesAndUpdateCache($uid, $params['roles']);
        }

        $this->event_log('批量分配角色', $params, []);

        return $this->success('操作成功');
    }

    /**
     * 批量重置密码
     * @return \think\Response
     */
    public function resetPassword(): Response
    {
        $params = $this->request->param();

        if (empty($params['id']) || empty($params['password'])) {
            return $this->error('参数不完整');
        }

        $id_arr = is_array($params['id']) ? $params['id'] : explode(',', (string)$params['id']);

        foreach ($id_arr as $uid) {
            $this->repository->resetUserPassword($uid, $params);
        }

        $this->event_log('批量重置密码', $params, []);

        return $this->success('重置成功');
    }
}
