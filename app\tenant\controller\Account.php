<?php
declare(strict_types = 1);

namespace app\tenant\controller;

use app\tenant\constant\LoginType as LoginTypeConstant;
use app\tenant\context\TenantContext;
use app\tenant\repository\LoginLogRepository;
use app\tenant\repository\OperationLogRepository;
use app\tenant\repository\UserRepository;
use app\tenant\service\CacheService;
use app\tenant\service\LoginService;
use app\tenant\service\TokenService;
use app\tenant\validate\User as UserValidate;
use think\App;
use think\Response;

/**
 * 租户用户账户控制器
 * 负责处理用户个人账户相关操作
 *
 * 接口安全机制：
 * 1. API加密：所有接口（除登录相关）都使用RSA+AES混合加密
 *    - 请求数据格式：{"encrypted_data":"AES加密的数据","encrypted_key":"RSA加密的AES密钥"}
 *    - 响应数据格式：同上
 *
 * 2. 接口认证：需要在请求头中携带access_token
 *    - Authorization: Bearer {access_token}
 *
 * 主要功能：
 * 1. 用户登录（支持多种登录方式）
 * 2. Token刷新
 * 3. 修改密码
 * 4. 更新个人信息
 * 5. 退出登录
 * 6. 获取可管理设备列表
 *
 * <AUTHOR> <<EMAIL>>
 * @package app\tenant\controller
 */
class Account extends AuthBase
{
    /**
     * token服务类
     * @var TokenService
     */
    protected TokenService $tokenService;

    /**
     * 租户缓存服务类
     * @var CacheService
     */
    protected CacheService $cacheService;

    /**
     * 租户登录服务类
     * @var LoginService
     */
    protected LoginService $loginService;

    /**
     * 排除不需要登录的方法
     * @var array|string[]
     */
    protected array $noNeedLogin = ['login', 'refreshToken'];

    public function __construct(
        App $app,
        TokenService $tokenService,
        CacheService $cacheService,
        LoginService $loginService
    ) {
        parent::__construct($app);

        $this->tokenService = $tokenService;
        $this->cacheService = $cacheService;
        $this->loginService = $loginService;

        // 处理不需要登录的方法
        if (!in_array($this->request->action(), $this->noNeedLogin)) {
            $this->checkAuth();
        }
    }

    /**
     * 用户登录
     * 支持多种登录方式，通过login_type参数指定：
     * - account: 账号密码登录
     * - mobile: 手机号验证码登录
     * - token: 令牌登录
     *
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \Random\RandomException
     * @return \think\Response
     */
    public function login(): Response
    {
        $params    = $this->request->post();
        $loginType = $params['login_type'] ?? null;

        // 验证登录类型是否合法
        if (!LoginTypeConstant::isValid($loginType)) {
            if ($loginType === null) {
                return $this->error('登录类型不能为空');
            }

            // 说明未注册对应的登录策略
            return $this->error('登录类型校验错误[' . ($loginType ?: 'null') . ']');
        }

        // 验证登录参数
        $validate      = validate(UserValidate::class);
        $validateScene = LoginTypeConstant::getScene($loginType);

        if (!$validate->scene($validateScene)->check($params)) {
            return $this->error($validate->getError());
        }

        // 验证完毕后，执行登录验证流程
        $result = $this->loginService->validateLogin($loginType, $params);

        // 处理登录验证结果
        if (isset($result['status'])) {
            if (!$result['status']) {
                return $this->error($result['message']);
            }
        }

        return $this->success($result);
    }

    /**
     * 刷新access_token（相当于拿refresh_token重新登录）
     *
     * @throws \Random\RandomException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return \think\Response
     */
    public function refreshToken(): Response
    {
        $params       = $this->request->param();
        $refreshToken = $this->request->param('refresh_token');

        if (empty($refreshToken)) {
            return $this->error('refresh_token不能为空');
        }

        try {
            // 验证refresh_token并获取缓存用户信息
            $tokenData = $this->tokenService->verifyToken($refreshToken, 'refresh_token');

            // 将获取到的租户编码附加到params
            $params['user_id']     = $tokenData['id']          ?? 0;
            $params['tenant_id']   = $tokenData['tenant_id']   ?? 0;
            $params['tenant_code'] = $tokenData['tenant_code'] ?? '';
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }

        // 使用LoginService的validateLogin方法处理token刷新
        $result = $this->loginService->validateLogin('refresh_token', $params);

        // 处理登录验证结果
        if (isset($result['status']) && !$result['status']) {
            return $this->error($result['message']);
        }

        $result['message'] = '刷新成功';

        return $this->success($result);
    }

    /**
     * 修改密码
     *
     * @return \think\Response
     */
    public function changePassword(): Response
    {
        $params = $this->request->post();

        // 验证参数
        if (empty($params['old_password']) || empty($params['password']) || empty($params['confirm_password'])) {
            return $this->error('请填写完整的密码信息');
        }

        if ($params['password'] !== $params['confirm_password']) {
            return $this->error('两次输入的新密码不一致');
        }

        $userRepository = new UserRepository();

        // 获取用户信息
        $user = $userRepository->getTenantUserById($this->getUserId());

        if (!$user) {
            return $this->error('用户不存在');
        }

        // 验证原密码
        if (!$user->verifyPassword($params['old_password'])) {
            return $this->error('原密码错误');
        }

        // 更新密码
        $result = $userRepository->updateUser($this->getUserId(), [
            'password' => $params['password'],
        ]);

        return $result ? $this->success('密码修改成功') : $this->error('密码修改失败');
    }

    /**
     * 修改个人信息
     *
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return \think\Response
     */
    public function updateProfile(): Response
    {
        $params = $this->request->post();

        // 验证参数
        $validate = validate(UserValidate::class);

        if (!$validate->scene('update_profile')->check($params)) {
            return $this->error($validate->getError());
        }

        $userRepository = new UserRepository();

        // 检查手机号或用户名是否已被使用
        $existUser = $userRepository->findByUsernameOrMobile(
            $params['username'] ?? '',
            $params['mobile']   ?? '',
            $this->getUserId()
        );

        if ($existUser) {
            return $this->error('用户名或手机号已被使用');
        }

        // 更新用户信息
        $result = $userRepository->updateUser($this->getUserId(), $params);

        return $result ? $this->success('个人信息修改成功') : $this->error('个人信息修改失败');
    }

    /**
     * 退出登录
     *
     * @return \think\Response
     */
    public function logout(): Response
    {
        // 获取accessToken
        $accessToken = $this->tokenService->getRequestToken();

        // 清除用户token
        $this->tokenService->removeToken($accessToken, $this->getTenantId(), $this->getUserId());

        // 清除上下文中的用户信息
        TenantContext::getInstance()->clearUserInfo();

        return $this->success('退出成功');
    }

    /**
     * 个人日志列表
     * @return \think\Response
     */
    public function logs(): Response
    {
        $params   = $this->request->param();
        $pageNo   = $this->request->param('page', 1, 'intval');
        $pageSize = $this->request->param('pageSize', 20, 'intval');
        $type     = $this->request->param('type', '');

        // 指定当前租户用户
        $params['tenant_id'] = $this->getTenantId();
        $params['user_id']   = $this->getUserId();

        // 根据type参数调用不同的仓库类查询日志
        switch ($type) {
            case 'login':
                $repository = new LoginLogRepository();
                $result     = $repository->getUserLoginLogs($params, $pageNo, $pageSize);
                break;
            case 'operation':
                $repository = new OperationLogRepository();
                $result     = $repository->getUserOperationLogs($params, $pageNo, $pageSize);
                break;
            default:
                return $this->error('类型错误');
        }

        return $this->success($result);
    }
}
