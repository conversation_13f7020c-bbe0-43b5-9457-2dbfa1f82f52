<?php
namespace app\common\controller;

use think\Response;

class Docs extends CommonController
{
    /**
     * 获取目录树结构
     * @return \think\Response
     */
    public function tree(): Response
    {
        $docsPath = root_path() . 'docs';

        if (!is_dir($docsPath)) {
            return $this->error('文档目录不存在');
        }

        $tree = $this->buildTree($docsPath);

        return $this->success($tree);
    }

    /**
     * 获取文件内容
     * @return \think\Response
     */
    public function content(): Response
    {
        $path = input('path');

        if (empty($path)) {
            return $this->error('文档路径不能为空');
        }

        // 过滤路径中的特殊字符
        $path = str_replace(['..', '\\', '//', '~', '`', '$', '&', '*', '|', ';', ' '], '', $path);

        $filePath = root_path() . 'docs' . DIRECTORY_SEPARATOR . $path;

        // 验证路径安全性
        if (!$this->isPathSafe($filePath)) {
            return $this->error('非法的文档路径');
        }

        if (!is_file($filePath)) {
            return $this->error('文档不存在');
        }

        if (pathinfo($filePath, PATHINFO_EXTENSION) !== 'md') {
            return $this->error('不支持的文档格式');
        }

        $content = file_get_contents($filePath);

        if ($content === false) {
            return $this->error('读取文档失败');
        }

        return $this->success(['data' => $content]);
    }

    /**
     * 验证路径安全性
     * @param string $path 需要验证的路径
     * @return bool
     */
    protected function isPathSafe(string $path): bool
    {
        // 获取规范化的真实路径
        $realPath = realpath($path);

        if ($realPath === false) {
            return false;
        }

        // 获取docs目录的真实路径
        $docsPath = realpath(root_path() . 'docs');

        if ($docsPath === false) {
            return false;
        }

        // 确保路径在docs目录下
        return str_starts_with($realPath, $docsPath);
    }

    /**
     * 递归构建目录树
     * @param string $path 目录路径
     * @return array
     */
    protected function buildTree(string $path): array
    {
        if (!$this->isPathSafe($path)) {
            return [];
        }

        $result = [];
        $files  = scandir($path);

        foreach ($files as $file) {
            if ($file === '.' || $file === '..' || $file[0] === '.') {
                continue;
            }

            $fullPath = $path . DIRECTORY_SEPARATOR . $file;

            // 验证文件路径安全性
            if (!$this->isPathSafe($fullPath)) {
                continue;
            }
            $relativePath = str_replace(root_path() . 'docs' . DIRECTORY_SEPARATOR, '', $fullPath);

            if (is_dir($fullPath)) {
                $children = $this->buildTree($fullPath);

                if (!empty($children)) {
                    $result[] = [
                        'label'    => $file,
                        'children' => $children,
                        'isFolder' => true,
                    ];
                }
            } elseif (pathinfo($file, PATHINFO_EXTENSION) === 'md') {
                $result[] = [
                    'label' => pathinfo($file, PATHINFO_FILENAME),
                    'path'  => $relativePath,
                ];
            }
        }

        return $result;
    }

    /**
     * 搜索文档
     * @return \think\Response
     */
    public function search(): Response
    {
        $keyword = input('keyword');

        if (empty($keyword)) {
            return $this->error('搜索关键词不能为空');
        }

        $docsPath = root_path() . 'docs';

        if (!is_dir($docsPath)) {
            return $this->error('文档目录不存在');
        }

        $result = [];
        $this->searchDocs($docsPath, $keyword, $result);

        return $this->success($result);
    }

    /**
     * 递归搜索文档
     * @param string $path 搜索路径
     * @param string $keyword 搜索关键词
     * @param array &$result 搜索结果
     */
    protected function searchDocs(string $path, string $keyword, array &$result): void
    {
        if (!$this->isPathSafe($path)) {
            return;
        }

        $files = scandir($path);

        foreach ($files as $file) {
            if ($file === '.' || $file === '..' || $file[0] === '.') {
                continue;
            }

            $fullPath = $path . DIRECTORY_SEPARATOR . $file;

            if (!$this->isPathSafe($fullPath)) {
                continue;
            }

            if (is_dir($fullPath)) {
                $this->searchDocs($fullPath, $keyword, $result);
            } elseif (pathinfo($file, PATHINFO_EXTENSION) === 'md') {
                $content = file_get_contents($fullPath);

                if (stripos(pathinfo($file, PATHINFO_FILENAME), $keyword) !== false || stripos($content, $keyword) !== false) {
                    $relativePath = str_replace(root_path() . 'docs' . DIRECTORY_SEPARATOR, '', $fullPath);
                    $result[]     = [
                        'label' => pathinfo($file, PATHINFO_FILENAME),
                        'path'  => $relativePath,
                    ];
                }
            }
        }
    }
}
