<?php
namespace app\system\repository;

use app\system\model\DepartmentUser as DepartmentUserModel;
use app\system\model\RoleUser as RoleUserModel;
use app\system\model\User as UserModel;
use app\system\service\AuthService;
use think\Collection;

/**
 * 用户数据仓储类
 * 负责系统用户相关的数据访问和操作，提供用户信息的CRUD功能和关联数据管理
 * 主要职责：
 * 1. 管理用户基础信息的增删改查
 * 2. 提供用户数据的搜索功能
 * 3. 维护用户状态管理
 * 4. 处理用户角色关联
 * 5. 处理用户部门关联
 * 6. 管理用户密码和认证信息
 * 7. 维护用户缓存数据
 * <AUTHOR> <<EMAIL>>
 * @package app\system\repository
 */
class UserRepository extends BaseRepository
{
    /**
     * 构造方法
     */
    public function __construct()
    {
        // 指定当前模型类
        parent::__construct(UserModel::class);
    }

    /**
     * 查询列表
     * @param array $params
     * @param int $pageNo
     * @param int $pageSize
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return array
     */
    public function getList(array $params = [], int $pageNo = 1, int $pageSize = 10): array
    {
        // 设置需要排除的敏感字段
        $this->setCondition(['password', 'salt'], 'withoutField');

        // 设置基础查询条件 - 非删除数据
        $this->setCondition([
            ['status', '>=', 0],
        ]);

        // 设置搜索条件
        if (!empty($params['keyword'])) {
            $this->setCondition([
                function($query) use ($params) {
                    $query->whereOr([
                        ['username', 'LIKE', "%{$params['keyword']}%"],
                        ['realname', 'LIKE', "%{$params['keyword']}%"],
                        ['phone', 'LIKE', "%{$params['keyword']}%"],
                        ['email', 'LIKE', "%{$params['keyword']}%"],
                    ]);
                },
            ]);
        }

        // 角色筛选
        if (!empty($params['role_id'])) {
            $this->setCondition([
                function($query) use ($params) {
                    $query->join('system_role_user ru', 'ru.user_id = id', 'LEFT')
                          ->where('ru.role_id', '=', intval($params['role_id']));
                },
            ]);
        }

        // 部门筛选
        if (!empty($params['dept_id'])) {
            $this->setCondition([
                function($query) use ($params) {
                    $query->join('system_department_user du', 'du.user_id = id', 'LEFT')
                          ->where('du.dept_id', '=', intval($params['dept_id']));
                },
            ]);
        }

        // 状态筛选
        if (isset($params['status']) && $params['status'] !== '') {
            $this->setCondition([
                ['status', '=', intval($params['status'])],
            ]);
        }

        // 获取分页数据
        $result = $this->pageList($params, $pageNo, $pageSize);

        // 加载关联数据
        if (!empty($result['list'])) {
            $this->loadRelations($result['list']);
        }

        return $result;
    }

    /**
     * 设置用户角色关联关系
     * 先清空已有角色绑定，再批量插入新角色
     * @param int $userId 用户ID
     * @param array $roleIds 角色ID数组
     * @return int 成功插入的记录数
     */
    public function setUserRoles(int $userId, array $roleIds): int
    {
        RoleUserModel::where('user_id', $userId)->delete();
        $data = array_map(function($roleId) use ($userId) {
            return ['user_id' => $userId, 'role_id' => $roleId];
        }, $roleIds);

        return RoleUserModel::insertAll($data);
    }

    /**
     * 设置用户部门关联关系
     * 先清空已有部门绑定，再批量插入新部门
     * @param int $userId 用户ID
     * @param array $deptIds 部门ID数组
     * @return int 成功插入的记录数
     */
    public function setUserDepartments(int $userId, array $deptIds): int
    {
        DepartmentUserModel::where('user_id', $userId)->delete();
        $data = array_map(function($deptId) use ($userId) {
            return ['user_id' => $userId, 'dept_id' => $deptId];
        }, $deptIds);

        return DepartmentUserModel::insertAll($data);
    }

    /**
     * 加载关联数据（角色和部门）
     * 为用户列表附加关联的角色名称和部门名称信息
     * @param \think\Collection $list 用户数据集合（引用传递）
     */
    protected function loadRelations(Collection &$list): void
    {
        $userIds = $list->column('id');

        // 获取角色信息
        $roles = RoleUserModel::alias('ru')
            ->join('system_role r', 'ru.role_id = r.id')
            ->where('ru.user_id', 'in', $userIds)
            ->field('r.id, r.name, r.status, ru.user_id')
            ->select();

        // 获取部门信息
        $department = DepartmentUserModel::alias('du')
            ->join('system_department d', 'du.dept_id = d.id')
            ->where('du.user_id', 'in', $userIds)
            ->field('d.id, d.name, d.status, du.user_id')
            ->select();

        // 整合数据
        $list->each(function($item) use ($roles, $department) {
            $item->roles      = $roles->where('user_id', $item->id)->column('id');
            $item->roles_name = implode('/', $roles->where('user_id', $item->id)->column('name'));
            $item->depts      = $department->where('user_id', $item->id)->column('id');
            $item->depts_name = implode('/', $department->where('user_id', $item->id)->column('name'));
        });
    }

    /**
     * 设置用户角色和更新相关缓存
     * @param int $userId
     * @param array $roleIds
     * @return bool
     */
    public function setRolesAndUpdateCache(int $userId, array $roleIds): bool
    {
        $ret = $this->setUserRoles($userId, $roleIds);

        if ($ret) {
            // 更新用户状态位，标记需要重新登录获取授权
            AuthService::updateCacheUserInfo($userId, ['relogin' => 1]);
            // 更新用户节点权限缓存
            AuthService::updateCacheUserNodesByUserIds([$userId]);

            return true;
        }

        return false;
    }

    /**
     * 重置用户密码
     * @param int $userId
     * @param array $params
     * @return bool
     */
    public function resetUserPassword(int $userId, array $params): bool
    {
        $salt = AuthService::generateSalt();
        $data = [
            'salt'                  => $salt,
            'password'              => AuthService::generatePassword($params['password'], $salt),
            'force_change_password' => $params['force_change_password'] ?? 0,
            'update_at'             => time(),
        ];

        $ret = $this->updateData($userId, $data);

        if ($ret) {
            AuthService::updateCacheUserInfo($userId, [
                'force_change_password' => $data['force_change_password'],
            ]);

            return true;
        }

        return false;
    }

    /**
     * 设置用户状态并更新缓存(覆盖父类方法)
     * @param mixed $ids
     * @param int $status
     * @param int $excludeUserId 需要排除的用户ID
     * @return array
     */
    public function setStatusWithCache(mixed $ids, int $status = 0, int $excludeUserId = 0): array
    {
        // 将传过来的ids解析成数组
        $idArr = is_array($ids) ? $ids : explode(',', (string)$ids);

        // 排除指定用户ID
        if ($excludeUserId > 0) {
            $idArr = array_merge(array_diff($idArr, [$excludeUserId]));
        }

        if (empty($idArr)) {
            return [
                'code'    => 1,
                'message' => '无法操作用户状态',
            ];
        }

        // 设置状态
        $ret = $this->setStatus($idArr, $status);

        if ($ret) {
            // 更新用户状态缓存
            foreach ($idArr as $uid) {
                AuthService::updateCacheUserInfo((int)$uid, ['status' => $status]);
            }
            // 更新节点权限缓存
            AuthService::updateCacheUserNodesByUserIds($idArr);

            return [
                'code'    => 0,
                'message' => '设置成功',
            ];
        }

        return [
            'code'    => 1,
            'message' => '设置失败',
        ];
    }

    /**
     * 更新用户信息和缓存
     * @param int $userId
     * @param array $data
     * @return bool
     */
    public function updateUserAndCache(int $userId, array $data): bool
    {
        $result = $this->updateData($userId, $data);

        if ($result) {
            // 选择性更新缓存 - 只更新需要缓存的字段
            $cacheData = array_intersect_key($data, array_flip([
                'force_change_password',
                'status',
                'phone',
                'email',
                'realname',
                'gender',
                'birthday',
                'signature',
                // 其他需要缓存的字段...
            ]));

            if (!empty($cacheData)) {
                AuthService::updateCacheUserInfo($userId, $cacheData);
            }

            return true;
        }

        return false;
    }

    /**
     * 根据用户ids，筛选出当前正常状态（非冻结、删除）的用户数据
     * @param mixed $ids
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return array
     */
    public function getActiveUserByIds(mixed $ids): array
    {
        if (is_string($ids)) {
            $ids = explode(',', $ids);
        }

        if (empty($ids)) {
            return [];
        }

        $user_id_arr = $this->model->where([['id', 'IN', $ids], ['status', '=', 1]])->field('id')->select()->toArray();

        return $user_id_arr ? array_column($user_id_arr, 'id') : [];
    }

    /**
     * 获取有效用户列表（用于发送消息）
     */
    public function getActiveUsers(): array
    {
        return $this->model->field('id, username, realname, avatar')
            ->where('status', 1)
            ->select()
            ->toArray();
    }
}
