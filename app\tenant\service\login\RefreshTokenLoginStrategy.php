<?php
declare(strict_types = 1);

namespace app\tenant\service\login;

use app\tenant\exception\AccountException;
use app\tenant\model\User as UserModel;
use app\tenant\repository\UserRepository;

/**
 * refresh_token登录策略类
 * <AUTHOR> <<EMAIL>>
 * @package app\tenant\service\login
 */
class RefreshTokenLoginStrategy implements LoginStrategyInterface
{
    /**
     * 执行登录
     * @param array $credentials 登录凭证，需要包含refresh_token字段
     * @throws \app\tenant\exception\AccountException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return \app\tenant\model\User|null
     */
    public function login(array $credentials): ?UserModel
    {
        if (!isset($credentials['user_id']) || !isset($credentials['tenant_id']) || !isset($credentials['refresh_token'])) {
            return null;
        }

        if (empty($credentials['refresh_token'])) {
            throw new AccountException('refresh_token不能为空');
        }

        // 获取用户信息
        $user = (new UserRepository())->findByUserId($credentials['user_id'], $credentials['tenant_id']);

        if (!$user) {
            return null;
        }

        return $user;
    }

    public function getType(): string
    {
        return 'refresh_token';
    }
}
