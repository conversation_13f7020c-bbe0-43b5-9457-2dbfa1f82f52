<?php
declare(strict_types = 1);

namespace app\common\service;

use U<PERSON>arser\Parser;
use UAParser\Result\Client;

/**
 * UserAgent服务类
 * <AUTHOR> <<EMAIL>>
 * @package app\common\service
 */
class UserAgentService extends CommonService
{
    private Parser $parser;

    private ?Client $result = null;

    private ?string $userAgent = null;

    /** @var self|null 服务实例 */
    private static ?self $instance = null;

    /**
     * 初始化Parser
     * @param string|null $userAgent 自定义UA字符串
     * @throws \UAParser\Exception\FileNotFoundException
     */
    public function __construct(?string $userAgent = null)
    {
        parent::__construct();

        $this->parser    = Parser::create();
        $this->userAgent = $userAgent;
    }

    /**
     * 获取实例（单例模式）
     * @param string|null $userAgent
     * @throws \UAParser\Exception\FileNotFoundException
     * @return self
     */
    public static function getInstance(?string $userAgent = null): self
    {
        if (!self::$instance instanceof self || $userAgent !== null) {
            self::$instance = new self($userAgent);
        }

        return self::$instance;
    }

    /**
     * 获取当前UA字符串
     * @return string
     */
    public function getUserAgent(): string
    {
        if ($this->userAgent === null) {
            $this->userAgent = request()->header('user-agent', '');
        }

        return $this->userAgent;
    }

    /**
     * 获取解析结果
     * @return Client
     */
    private function getResult(): Client
    {
        if ($this->result === null) {
            $this->result = $this->parser->parse($this->getUserAgent());
        }

        return $this->result;
    }

    /**
     * 获取操作系统信息
     * @return array
     */
    public function getOs(): array
    {
        $os = $this->getResult()->os;

        return [
            'info'       => $os->toString(),
            'family'     => $os->family,
            'version'    => $os->toVersion(),
            'major'      => $os->major,
            'minor'      => $os->minor,
            'patch'      => $os->patch,
            'patchMinor' => $os->patchMinor,
        ];
    }

    /**
     * 获取浏览器信息
     * @return array
     */
    public function getBrowser(): array
    {
        $ua = $this->getResult()->ua;

        return [
            'info'    => $ua->toString(),
            'family'  => $ua->family,
            'version' => $ua->toVersion(),
            'major'   => $ua->major,
            'minor'   => $ua->minor,
            'patch'   => $ua->patch,
        ];
    }

    /**
     * 获取设备信息
     * @return array
     */
    public function getDevice(): array
    {
        $device = $this->getResult()->device;

        return [
            'family' => $device->family,
            'brand'  => $device->brand,
            'model'  => $device->model,
        ];
    }
}
