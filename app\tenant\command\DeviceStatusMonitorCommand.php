<?php
declare(strict_types = 1);

namespace app\tenant\command;

use app\common\service\mqtt\MqttService;
use app\common\service\RedisPool;
use app\tenant\service\DeviceStatusService;
use Swoole\Coroutine;

use function Swoole\Coroutine\run;

use Swoole\Process;
use Swoole\Runtime;
use think\console\Input;
use think\console\input\Option;
use think\console\Output;
use think\facade\Cache;
use think\facade\Log;
use think\facade\Config;

/**
 * 设备状态监控命令
 * 用于监听设备心跳，跟踪设备在线状态
 * <AUTHOR> <<EMAIL>>
 * @package app\tenant\command
 */
class DeviceStatusMonitorCommand extends BaseCommand
{
    /**
     * MQTT配置
     * @var array
     */
    private array $config = [];

    /**
     * 进程锁文件路径
     * @var string
     */
    private string $lockFile = '';

    /**
     * MQTT服务实例
     * @var MqttService
     */
    protected MqttService $mqttService;

    /**
     * Redis连接池实例
     * @var RedisPool|null
     */
    protected ?RedisPool $redisPool = null;

    /**
     * 是否继续运行
     * @var bool
     */
    protected bool $running = true;

    /**
     * Redis键前缀
     * @var string
     */
    protected string $redisKeyPrefix;

    /**
     * Redis通用前缀
     * @var string
     */
    protected string $redisCommonPrefix;

    /**
     * 设备状态过期时间（秒）
     * 如果在这段时间内没有收到心跳，则认为设备离线
     * @var int
     */
    protected int $deviceStatusExpire = 120; // 2分钟

    /**
     * 设备状态服务
     * @var DeviceStatusService
     */
    protected DeviceStatusService $deviceStatusService;

    public function __construct()
    {
        parent::__construct();

        // 合并全局配置和tenant模块配置
        $this->config = getAppConfig('mqtt', 'tenant');
        // 初始化进程锁文件路径
        $this->lockFile = runtime_path() . 'device_status_monitor.lock';
        // 初始化设备状态服务
        $this->deviceStatusService = DeviceStatusService::getInstance();

        // 初始化Redis键前缀
        $this->initRedisPrefix();
    }

    /**
     * 初始化Redis键前缀
     */
    protected function initRedisPrefix(): void
    {
        // 从环境变量或配置中获取Redis通用前缀
        $this->redisCommonPrefix = env('REDIS.PREFIX', '');

        // 如果配置中有定义设备前缀，则使用配置中的值
        if (isset($this->config['redis_prefix']) && !empty($this->config['redis_prefix'])) {
            $this->redisKeyPrefix = $this->config['redis_prefix'];
        } else {
            // 否则使用默认值
            $this->redisKeyPrefix = 'tenant:device:';
        }

        // 确保前缀以冒号结尾
        if (!empty($this->redisKeyPrefix) && !str_ends_with($this->redisKeyPrefix, ':')) {
            $this->redisKeyPrefix .= ':';
        }

        // 添加通用前缀
        $this->redisKeyPrefix = $this->redisCommonPrefix . $this->redisKeyPrefix;
    }

    /**
     * 配置指令
     * php think tenant:deviceStatusMonitor
     * php think tenant:deviceStatusMonitor -t 10001
     * php think tenant:deviceStatusMonitor --tenant 租户编码
     * @return void
     */
    protected function configure(): void
    {
        $this->setName('tenant:deviceStatusMonitor')
            ->setDescription('启动设备状态监控服务')
            ->addOption('tenant', 't', Option::VALUE_OPTIONAL, '指定租户ID，不指定则监听所有租户', null);
    }

    /**
     * 执行指令
     * @param Input $input
     * @param Output $output
     * @return void
     */
    protected function execute(Input $input, Output $output): void
    {
        // 检查是否已有实例在运行
        if (file_exists($this->lockFile)) {
            $pid = file_get_contents($this->lockFile);

            if ($pid && posix_kill((int)$pid, 0)) {
                $this->logError("已有一个设备状态监控服务实例正在运行，进程ID：{$pid}");
                return;
            }

            // 如果进程不存在，删除锁文件
            unlink($this->lockFile);
        }

        // 创建进程锁
        file_put_contents($this->lockFile, getmypid());

        $config = $this->config;

        Runtime::enableCoroutine();

        // 在协程环境中运行MQTT客户端
        run(function() use ($input, $output, $config) {
            try {
                // 初始化MQTT服务
                $this->mqttService = MqttService::getInstance();

                if (!$this->mqttService->connect()) {
                    $this->logError('MQTT服务连接失败，失败原因请查看Log');
                    return;
                }

                // 获取租户编码参数
                $tenantCode = $input->getOption('tenant');

                // 主题名称，指定租户ID，不指定则监听所有租户
                $heartbeatTopic = $this->mqttService->buildTopic($config['topic_patterns']['device_heartbeat'], [
                    'tenant_code' => $tenantCode ?: '+',
                    'device_id'   => '+',
                ]);

                // 按协议版本组合成数组
                $topics = [
                    $heartbeatTopic => ['qos' => 0, 'no_local' => true, 'retain' => false],
                ];

                // 订阅主题
                if (!$this->mqttService->subscribe($topics)) {
                    $this->logError('订阅主题失败');
                    return;
                }

                if ($tenantCode) {
                    $this->logInfo("开始监听租户【{$tenantCode}】的设备心跳主题[{$heartbeatTopic}]");
                } else {
                    $this->logInfo("开始监听所有租户的设备心跳主题[{$heartbeatTopic}]");
                }

                // 使用 Swoole 的信号处理
                $signalHandler = function($signal) {
                    static $isShuttingDown = false; // 用于确保清理逻辑只执行一次

                    if ($isShuttingDown) {
                        $this->logInfo('服务正在关闭中，尝试强制停止事件循环...');
                        // 如果已经在关闭过程中，再次收到信号，尝试停止Swoole事件循环
                        if (class_exists('\Swoole\Event')) {
                            \Swoole\Event::exit();
                        }
                        return;
                    }
                    $isShuttingDown = true;

                    $this->running = false;
                    $this->logInfo('收到信号 ' . $signal . '，准备停止服务');

                    // 尝试中断MQTT接收操作
                    if (isset($this->mqttService) && $this->mqttService->isConnected()) {
                        $this->logInfo('尝试断开MQTT连接以中断接收...');
                        $this->mqttService->disconnect(); // 主动断开，使receive()不再阻塞
                    }

                    // 清理资源的逻辑将主要放在主循环退出后执行
                    // 这里可以做一些快速的标记或预处理
                };

                Process::signal(SIGTERM, $signalHandler);
                Process::signal(SIGINT, $signalHandler);

                // 监听循环
                while ($this->running) {
                    // 检查连接状态并自动重连
                    if (!$this->mqttService->isConnected()) {
                        $this->logWarning('检测到MQTT连接断开，尝试重新连接...');

                        if (!$this->mqttService->connect()) {
                            $this->logError('MQTT重连失败，5秒后重试');
                            Coroutine::sleep(5);
                            continue;
                        }
                        $this->logInfo('MQTT重连成功');

                        // 重新订阅主题
                        if (!$this->mqttService->subscribe($topics)) {
                            $this->logError('重新订阅主题失败');
                            continue;
                        }
                    }

                    // 接收消息
                    $result = $this->mqttService->receive();

                    if (!$result) {
                        // 发送心跳包保持连接
                        if (!$this->mqttService->ping()) {
                            $this->logError('发送心跳包失败，标记连接为断开状态');
                            $this->mqttService->disconnect();
                        }
                        continue;
                    }

                    // 说明是keepalive心跳
                    if ($result === true) {
                        continue;
                    }

                    // 调试模式下打印消息到屏幕
                    if ($config['debug']) {
                        $this->logDebug('收到设备心跳消息: ' . (!is_array($result) ? $result : ''));

                        if (is_array($result)) {
                            dump($result);
                        }
                    }

                    try {
                        // 解析消息
                        $message = json_decode($result['message'] ?? '', true);

                        if (!$message) {
                            continue;
                        }

                        // 处理设备心跳消息
                        $this->processDeviceHeartbeat($result['topic'], $message);
                    } catch (\Exception $e) {
                        $this->logError('处理设备心跳消息异常: ' . $e->getMessage());
                    }
                }

                // 优雅退出
                $this->logWarning('设备状态监控服务已停止');

                // 确保清理逻辑只执行一次
                static $finalCleanupDone = false;
                if (!$finalCleanupDone) {
                    $this->logInfo('开始最终资源清理...');
                    try {
                        // 关闭Redis连接池
                        if ($this->redisPool !== null) {
                            $this->redisPool->close();
                            $this->logInfo('Redis连接池已关闭');
                        }

                        // 断开MQTT连接 (如果信号处理器中没有断开)
                        if (isset($this->mqttService) && $this->mqttService->isConnected()) {
                            $this->mqttService->disconnect();
                            $this->logInfo('MQTT连接已断开 (最终清理)');
                        }
                    } catch (\Throwable $e) {
                        $this->logError('最终资源清理异常: ' . $e->getMessage());
                    }

                    // 删除进程锁
                    if (file_exists($this->lockFile)) {
                        unlink($this->lockFile);
                        $this->logInfo('进程锁文件已删除 (最终清理)');
                    }
                    $finalCleanupDone = true;
                    $this->logInfo('最终资源清理完成');
                } else {
                    $this->logInfo('最终资源清理已执行过，跳过。');
                }
            } catch (\Throwable $e) {
                $this->logError('设备状态监控服务异常: ' . $e->getMessage() . "\n" . $e->getTraceAsString());

                // 删除进程锁
                if (file_exists($this->lockFile)) {
                    unlink($this->lockFile);
                }
            }
        });
    }

    /**
     * 处理设备心跳消息
     * @param string $topic 主题
     * @param array $message 消息内容
     */
    protected function processDeviceHeartbeat(string $topic, array $message): void
    {
        try {
            // 优先从消息中获取租户编码和设备ID
            $tenantCode = $message['tenantCode'] ?? null;
            $deviceId   = $message['deviceId']     ?? 0;

            // 如果消息中没有租户编码和设备ID，则尝试从主题中提取
            if (!$tenantCode || !$deviceId) {
                // 从配置文件中获取设备心跳主题模式
                $topicPattern = $this->config['topic_patterns']['device_heartbeat'] ?? '';

                if (!empty($topicPattern)) {
                    // 使用静态缓存存储编译后的正则表达式，避免重复编译
                    static $regexPattern = null;

                    if ($regexPattern === null) {
                        // 将主题模式转换为正则表达式模式
                        $regexPattern = '#' . str_replace(
                            ['{prefix}', '{tenant_code}', '{device_id}', '/'],
                            ['.*?', '([^/]+)', '([^/]+)', '\\/'],
                            $topicPattern
                        ) . '#';
                    }

                    $matches = [];

                    if (preg_match($regexPattern, $topic, $matches)) {
                        $tenantCode = $matches[1] ?? null;
                        $deviceId   = $matches[2]   ?? null;
                    }
                }
            }

            // 如果仍然无法获取租户编码和设备ID，则记录警告并返回
            if (!$tenantCode || !$deviceId) {
                $this->logWarning("无法获取租户编码和设备ID，主题：{$topic}");

                return;
            }

            $status    = $message['status']       ?? 'offline';
            $timestamp = $message['timestamp'] ?? time();

            $this->logDebug("收到设备心跳消息: 租户[{$tenantCode}] 设备[{$deviceId}] 状态[{$status}]");

            // 根据状态处理
            if ($status === 'online' || $status === 'alive') {
                // 设备上线或心跳
                $this->updateDeviceStatus((string)$tenantCode, (int)$deviceId, $status, (int)$timestamp);
            } elseif ($status === 'offline') {
                // 设备离线
                $this->setDeviceOffline((string)$tenantCode, (int)$deviceId, (int)$timestamp);
            } else {
                // 未知状态
                $this->logWarning("收到未知状态的设备心跳消息: 租户[{$tenantCode}] 设备[{$deviceId}] 状态[{$status}]");
            }
        } catch (\Throwable $e) {
            $this->logError('处理设备心跳消息异常: ' . $e->getMessage());
        }
    }

    /**
     * 更新设备状态
     * @param string $tenantCode 租户编码
     * @param int $deviceId 设备ID
     * @param string $status 状态
     * @param int $timestamp 时间戳
     */
    protected function updateDeviceStatus(string $tenantCode, int $deviceId, string $status, int $timestamp): void
    {
        $redis = null;
        try {
            // 获取Redis实例
            $redis = $this->getRedis();

            // 构建设置状态键
            $statusKey = $this->redisKeyPrefix . "status:{$tenantCode}:{$deviceId}";

            // 构建设置在线集合键
            $onlineKey = $this->redisKeyPrefix . "online:{$tenantCode}";

            if ($status === 'online' || $status === 'alive') {
                // 设备上线或心跳
                $statusData = [
                    'status'        => $status,
                    'lastHeartbeat' => $timestamp,
                    'tenantCode'    => $tenantCode,
                    'deviceId'      => $deviceId,
                    'online'        => true,
                ];

                // 使用事务确保原子性
                $redis->multi();

                // 设置设备状态，并设置过期时间
                $redis->hMset($statusKey, $statusData);
                $redis->expire($statusKey, $this->deviceStatusExpire);

                // 将设备添加到在线集合
                $redis->sAdd($onlineKey, $deviceId);
                $redis->expire($onlineKey, $this->deviceStatusExpire);

                // 执行事务并等待结果
                $results = $redis->exec();

                // 验证事务执行结果
                if (!$results || in_array(false, $results, true)) {
                    throw new \Exception('Redis transaction failed');
                }

                if ($status === 'online') {
                    $this->logInfo("设备上线: 租户[{$tenantCode}] 设备[{$deviceId}]");
                } else {
                    $this->logDebug("设备心跳: 租户[{$tenantCode}] 设备[{$deviceId}]");
                }
            } elseif ($status === 'offline') {
                // 设备离线
                $redis->multi();

                // 从在线集合中移除设备
                $redis->sRem($onlineKey, $deviceId);

                // 更新设备状态为离线
                $redis->hSet($statusKey, 'status', 'offline');
                $redis->hSet($statusKey, 'lastHeartbeat', $timestamp);
                $redis->hSet($statusKey, 'online', false);

                // 设置较短的过期时间，让Redis自动清理
                $redis->expire($statusKey, 86400); // 24小时后自动清理

                // 执行事务并等待结果
                $results = $redis->exec();

                // 验证事务执行结果
                if (!$results || in_array(false, $results, true)) {
                    throw new \Exception('Redis transaction failed');
                }

                $this->logInfo("设备离线: 租户[{$tenantCode}] 设备[{$deviceId}]");
            }
        } catch (\Exception $e) {
            $this->logError('更新设备状态异常: ' . $e->getMessage());
            // 记录详细的错误信息
            $this->logError('Redis操作失败详情: ' . json_encode([
                'tenantCode' => $tenantCode,
                'deviceId' => $deviceId,
                'status' => $status,
                'timestamp' => $timestamp,
                'error' => $e->getMessage()
            ]));
        } finally {
            // 确保Redis连接被归还到连接池
            if ($redis && class_exists('\Swoole\Coroutine') && Coroutine::getCid() !== -1) {
                try {
                    // 使用类属性中的连接池实例
                    if ($this->redisPool !== null) {
                        $this->redisPool->put($redis);
                    }
                } catch (\Throwable $e) {
                    Log::debug('归还Redis连接异常: ' . $e->getMessage());
                }
            }
        }
    }

    /**
     * 获取租户的在线设备
     * @param string $tenantCode 租户编码
     * @return array 在线设备ID数组
     */
    protected function getTenantOnlineDevices(string $tenantCode): array
    {
        $redis = null;
        try {
            $redis = $this->getRedis();
            $onlineKey = $this->redisKeyPrefix . "online:{$tenantCode}";

            // 获取在线设备集合中的所有设备ID
            return $redis->sMembers($onlineKey) ?: [];
        } catch (\Exception $e) {
            $this->logError('获取在线设备异常: ' . $e->getMessage());

            return [];
        } finally {
            // 确保Redis连接被归还到连接池
            if ($redis && class_exists('\Swoole\Coroutine') && Coroutine::getCid() !== -1 && $this->redisPool !== null) {
                try {
                    $this->redisPool->put($redis);
                } catch (\Throwable $e) {
                    Log::debug('归还Redis连接异常: ' . $e->getMessage());
                }
            }
        }
    }

    /**
     * 获取设备状态
     * @param int $deviceId 设备ID
     * @param string $tenantCode 租户编码
     * @return array|null 设备状态信息
     */
    protected function getDeviceStatus(int $deviceId, string $tenantCode): ?array
    {
        $redis = null;
        try {
            $redis = $this->getRedis();
            $statusKey = $this->redisKeyPrefix . "status:{$tenantCode}:{$deviceId}";

            // 获取设备状态
            $status = $redis->hGetAll($statusKey);

            return !empty($status) ? $status : null;
        } catch (\Exception $e) {
            $this->logError('获取设备状态异常: ' . $e->getMessage());

            return null;
        } finally {
            // 确保Redis连接被归还到连接池
            if ($redis && class_exists('\Swoole\Coroutine') && Coroutine::getCid() !== -1 && $this->redisPool !== null) {
                try {
                    $this->redisPool->put($redis);
                } catch (\Throwable $e) {
                    Log::debug('归还Redis连接异常: ' . $e->getMessage());
                }
            }
        }
    }

    /**
     * 设置设备离线
     * @param string $tenantCode 租户编码
     * @param int $deviceId 设备ID
     * @param int $timestamp 时间戳
     */
    protected function setDeviceOffline(string $tenantCode, int $deviceId, int $timestamp): void
    {
        $redis = null;
        try {
            $redis = $this->getRedis();

            // 构建设备状态键
            $statusKey = $this->redisKeyPrefix . "status:{$tenantCode}:{$deviceId}";

            // 构建设备在线集合键
            $onlineKey = $this->redisKeyPrefix . "online:{$tenantCode}";

            // 使用管道批量操作
            $pipeline = $redis->pipeline();

            // 从在线集合中移除设备
            $pipeline->sRem($onlineKey, $deviceId);

            // 更新设备状态为离线
            $pipeline->hSet($statusKey, 'status', 'offline');
            $pipeline->hSet($statusKey, 'lastHeartbeat', $timestamp);

            // 设置较短的过期时间，让Redis自动清理
            $pipeline->expire($statusKey, 86400); // 24小时后自动清理

            // 执行管道操作
            $pipeline->exec();

            $this->logInfo("设备离线: 租户[{$tenantCode}] 设备[{$deviceId}]");
        } catch (\Exception $e) {
            $this->logError('设置设备离线异常: ' . $e->getMessage());
        } finally {
            // 确保Redis连接被归还到连接池
            if ($redis && class_exists('\Swoole\Coroutine') && Coroutine::getCid() !== -1 && $this->redisPool !== null) {
                try {
                    $this->redisPool->put($redis);
                } catch (\Throwable $e) {
                    Log::debug('归还Redis连接异常: ' . $e->getMessage());
                }
            }
        }
    }

    /**
     * 获取Redis实例
     * 使用Redis连接池获取连接，避免在协程环境中频繁创建连接
     * @return object
     */
    protected function getRedis(): object
    {
        // 在协程环境中，使用连接池获取Redis连接
        if (class_exists('\Swoole\Coroutine') && Coroutine::getCid() !== -1) {
            try {
                // 使用静态变量缓存连接池实例，避免重复创建
                static $redisPool = null;

                if ($redisPool === null) {
                    // 根据系统负载动态调整连接池大小
                    $poolSize = min(10, max(20, swoole_cpu_num() * 10));
                    $redisPool = RedisPool::getInstance($poolSize);

                    // 将连接池实例保存到类属性中，以便在信号处理函数中使用
                    $this->redisPool = $redisPool;
                }

                // 增加获取连接的超时时间，提高获取成功率
                $redis = $redisPool->get(5.0);

                if ($redis) {
                    // 不再使用Coroutine::defer自动归还，改为手动归还
                    // 避免与updateDeviceStatus等方法中的手动归还冲突
                    return $redis;
                }

                // 如果无法从连接池获取连接，记录警告并尝试使用默认连接
                Log::warning('无法从Redis连接池获取连接，使用默认连接');

                // 创建新的Redis连接
                $config = Config::get('cache.stores.redis');
                $redis = new \Redis();

                // 设置连接超时时间
                $timeout = floatval($config['timeout']) ?? 1.0;

                // 连接Redis服务器
                if (!$redis->connect($config['host'], (int)$config['port'], $timeout)) {
                    throw new \Exception("Redis连接失败: {$config['host']}:{$config['port']}");
                }

                // 如果有密码，进行认证
                if (!empty($config['password'])) {
                    if (!$redis->auth($config['password'])) {
                        throw new \Exception('Redis认证失败');
                    }
                }

                // 选择数据库
                if (isset($config['select'])) {
                    if (!$redis->select((int)$config['select'])) {
                        throw new \Exception("Redis选择数据库失败: {$config['select']}");
                    }
                }

                // 协程结束时自动关闭连接
                Coroutine::defer(function() use ($redis) {
                    try {
                        $redis->close();
                    } catch (\Throwable $e) {
                        Log::debug('关闭Redis连接异常: ' . $e->getMessage());
                    }
                });

                return $redis;
            } catch (\Throwable $e) {
                Log::error('获取Redis连接异常: ' . $e->getMessage());
                throw $e; // 抛出异常，让调用者处理
            }
        }

        // 非协程环境，使用ThinkPHP的连接
        return Cache::store('redis')->handler();
    }

    /**
     * 处理指定信号
     * @param int $signal
     */
    public function handleSignal(int $signal): void
    {
        switch ($signal) {
            case SIGTERM:
            case SIGINT:
                $this->running = false;
                break;
        }
    }
}
