<?php
namespace app\common\validate;

use think\Validate;

/**
 * 全局公共验证器类
 * <AUTHOR> <<EMAIL>>
 * @package app\common\validate
 */
class CommonValidate extends Validate
{
    /**
     * 检查字符串是否合法（只能为字母、数字、中间可以用空格、@-_）
     * @param string $string
     * @return bool
     */
    public function checkStringValid(string $string): bool
    {
        return (bool)preg_match('/^[a-zA-Z0-9@_]*(\s[a-zA-Z0-9@_]+)*$/u', $string);
    }

    /**
     * 检查用户名是否只包含允许的字符
     * @param mixed $value
     * @param mixed $rule
     * @param array $data
     * @return bool
     */
    public function alphaNumSymbol(mixed $value, mixed $rule, array $data = []): bool
    {
        return preg_match('/^[a-zA-Z0-9_\-@.]+$/', $value) === 1;
    }
}
