<?php
declare(strict_types = 1);

namespace app\system\controller;

use app\common\controller\CommonController;
use app\system\constant\AuthConstant;
use app\system\service\AuthService;
use app\system\service\LogService;
use think\App;
use think\exception\HttpResponseException;

/**
 * 系统模块控制器基类
 * <AUTHOR> <<EMAIL>>
 * @package app\system\controller
 */
class BaseController extends CommonController
{
    protected string $routePath; // 路由地址

    protected int $userId = 0; // 用户ID

    protected array $userInfo = []; // 用户信息

    /**
     * 构造方法
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException|\Throwable
     */
    public function __construct(App $app)
    {
        parent::__construct($app);

        // 控制器初始化
        $this->initialize();
    }

    /**
     * 初始化
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException|\Throwable
     * @return void
     */
    protected function initialize(): void
    {
        parent::initialize();

        // 路由信息初始化
        $this->routePath = AuthService::getRoutePath();

        // 校验权限
        $checkResult = AuthService::checkPermission($this->routePath, true);

        // 如果没有验证通过（无qualified字段或者qualified为false）标识，就返回错误信息
        if (!isset($checkResult['qualified'])) {
            $errorData = [
                'code'    => $checkResult['code']    ?? AuthConstant::AUTH_NO_PERMISSION['code'],
                'message' => $checkResult['message'] ?? AuthConstant::AUTH_NO_PERMISSION['message'],
                'label'   => $checkResult['label']   ?? AuthConstant::AUTH_NO_PERMISSION['label'],
            ];

            // 使用JumpTrait的error方法构建统一的响应格式
            $response = $this->error($errorData);

            throw new HttpResponseException($response);
        }

        $this->userId   = $checkResult['userId']   ?? 0;
        $this->userInfo = $checkResult['userInfo'] ?? [];
    }

    /**
     * 获取当前路由地址
     * @return string
     */
    protected function getRoutePath(): string
    {
        return $this->routePath;
    }

    /**
     * 获取当前用户ID
     * @return int
     */
    protected function getUserId(): int
    {
        return $this->userId;
    }

    /**
     * 获取当前用户信息
     * @return array
     */
    protected function getUserInfo(): array
    {
        return $this->userInfo;
    }

    /**
     * 写入事件日志方法
     * @param string $event 操作事件
     * @param array $params 事件参数
     * @param mixed $description 事件描述
     * @return void
     */
    protected function event_log(string $event, array $params, mixed $description = ''): void
    {
        LogService::write($this->getUserInfo(), $event, $this->getRoutePath(), $params, $description);
    }
}
