# Redis缓存使用指南

## 基本用法

### 1. 常规缓存操作

```php
use app\common\facade\PredisService;

// 设置缓存（支持过期时间）
PredisService::set('key', 'value', 3600);  // 最后参数为过期时间(秒)
PredisService::set('key', 'value', new DateTime('+1 hour')); // 支持DateTime对象

// 获取缓存（支持默认值）
$value = PredisService::get('key', 'default_value');

// 删除缓存
PredisService::delete('key');

// 检查缓存是否存在
if (PredisService::has('key')) {
    // ...
}

// 设置过期时间
PredisService::expire('key', 3600);

// 获取过期剩余时间
$ttl = PredisService::ttl('key');
```

### 2. 计数器操作

```php
// 自增操作
PredisService::inc('counter');      // +1
PredisService::inc('counter', 5);   // +5

// 自减操作
PredisService::dec('counter');      // -1
PredisService::dec('counter', 5);   // -5
```

### 3. 标签(Tag)功能使用

支持两种方式使用标签：

#### 方式一：链式调用（推荐，与ThinkPHP用法一致）

```php
// 设置带标签的缓存
PredisService::tag('user')->set('user:1', ['name' => 'john'], 3600);

// 支持多个标签
PredisService::tag(['user', 'vip'])->set('user:1', ['name' => 'john'], 3600);

// 获取标签下的缓存
$value = PredisService::tag('user')->get('user:1');

// 清除标签下所有缓存
PredisService::tag('user')->clear();
```

#### 方式二：直接方法调用

```php
// 设置带标签的缓存
PredisService::setWithTag('user:1', ['name' => 'john'], 'users', 3600);

// 获取标签下的缓存
$value = PredisService::getByTag('user:1', 'users');

// 清除标签下所有缓存
PredisService::clearTag('users');
```

### 4. 并发锁的使用

```php
use app\common\facade\PredisService;

// 方式一：手动加锁和解锁
if (PredisService::lock('order:pay:123', 5)) {
    try {
        // 执行业务逻辑
        processPay();
    } finally {
        // 释放锁
        PredisService::unlock('order:pay:123');
    }
}

// 方式二：使用回调函数（推荐）
PredisService::withLock('order:pay:123', function() {
    // 执行业务逻辑
    processPay();
}, 5);

// 等待锁示例（最多等待10秒）
PredisService::withLock('order:pay:123', function() {
    // 业务逻辑
}, 5, 10);
```

#### 锁的参数说明：
- `$key`: 锁的唯一标识
- `$expire`: 锁的过期时间（秒），默认5秒
- `$timeout`: 获取锁的等待超时时间（秒），0表示不等待
- `$sleep`: 重试间隔时间（秒），默认0.1秒

#### 使用建议：
1. 推荐使用`withLock`方法，可以自动处理锁的释放
2. 设置合适的过期时间，防止死锁
3. 重要操作建议设置等待超时时间
4. 锁的key建议使用业务相关的唯一标识

## 与ThinkPHP缓存集成

### 1. ThinkPHP的Cache用法

```php
use think\facade\Cache;
use app\common\facade\PredisService;

// 使用ThinkPHP的Cache设置缓存
Cache::tag('user')->set('user:1', 'value', 3600);

// 使用PredisService的tag方法设置缓存（完全兼容）
PredisService::tag('user')->set('user:1', 'value', 3600);

// 两种方式都可以互相读取
$value = Cache::get('user:1');
$value = PredisService::get('user:1');
```

### 2. 自动前缀支持

系统会根据`.env`配置的`REDIS.PREFIX`自动处理键名前缀：

```ini
[REDIS]
PREFIX = pms:
```

使用时无需关心前缀：
```php
// 如果配置了 PREFIX = pms:
PredisService::set('user:1', 'value');    // 实际存储为 pms:user:1
PredisService::get('user:1');             // 自动查找 pms:user:1
```

### 3. 获取原始Redis实例

需要使用更多Redis原生方法时：

```php
$redis = PredisService::getRedis();
$redis->hSet('hash:1', 'field', 'value');
```

## 最佳实践

1. 推荐使用tag方法的链式调用方式，与ThinkPHP保持一致
2. 键名推荐使用`:`分隔符，如：`module:entity:id`
3. 相关的缓存建议使用tag功能统一管理
4. 获取缓存时设置默认值，避免空值判断
5. 支持DateTime对象设置过期时间
6. 数组会自动进行JSON编解码
7. 配置了REDIS.PREFIX后会自动处理前缀
