<?php
declare(strict_types = 1);

namespace app\common\service;

/**
 * 客户端指纹ID服务类
 * <AUTHOR> <<EMAIL>>
 * @package app\common\service
 */
class FingerService extends CommonService
{
    /**
     * 请求头中指纹ID名称
     */
    public const HEADER_FINGER_ID_KEY = 'X-Finger-Id';

    /**
     * 获取客户端IP地址
     * @param int $type 返回类型 0:返回IP地址 1:返回IPV4地址数字
     * @param bool $adv 是否进行高级模式获取(有可能被伪装)
     * @return string|int
     */
    public static function getClientIp(int $type = 0, bool $adv = true): string|int
    {
        $ip = '';

        if ($adv) {
            if (isset($_SERVER['HTTP_X_FORWARDED_FOR'])) {
                $arr = explode(',', $_SERVER['HTTP_X_FORWARDED_FOR']);
                $pos = array_search('unknown', $arr);

                if (false !== $pos) {
                    unset($arr[$pos]);
                }
                $ip = trim(current($arr));
            } elseif (isset($_SERVER['HTTP_CLIENT_IP'])) {
                $ip = $_SERVER['HTTP_CLIENT_IP'];
            } elseif (isset($_SERVER['REMOTE_ADDR'])) {
                $ip = $_SERVER['REMOTE_ADDR'];
            }
        } elseif (isset($_SERVER['REMOTE_ADDR'])) {
            $ip = $_SERVER['REMOTE_ADDR'];
        }

        // IP地址合法验证
        $long = sprintf('%u', ip2long($ip));
        $ip   = $long ? [$ip, $long] : ['0.0.0.0', 0];

        return $ip[$type];
    }

    /**
     * 获取客户端指纹唯一标识
     * @return string|null
     */
    public static function getFingerId(): ?string
    {
        // 获取客户端传过来的客户端指纹特征ID
        return request()->header(self::HEADER_FINGER_ID_KEY);
    }

    /**
     * 获取客户端指纹唯一标识
     * @return string
     */
    public static function getClientFingerId(): string
    {
        // 获取客户端传过来的客户端指纹特征ID
        $fingerId = self::getFingerId();

        if (!empty($fingerId)) {
            return $fingerId;
        }

        $finger_info = [
            'ip'              => self::getClientIp(),
            'user_agent'      => request()->header('user-agent'),
            'accept_language' => request()->header('accept-language'),
            // 添加更多浏览器指纹特征
            'screen'          => request()->header('sec-ch-ua-platform'),
            'timezone'        => request()->header('timezone'),
            'connection'      => request()->header('connection'),
            'accept_encoding' => request()->header('accept-encoding'),
            // 可以考虑加入时间戳的年月信息,这样指纹标识会每月更新一次
            'month_stamp' => date('Ym'),
        ];

        // 生成唯一指纹标识
        return md5(json_encode($finger_info));
    }
}
