<?php
declare(strict_types = 1);

namespace app\tenant\service;

use think\facade\Cache;
use think\facade\Log;

/**
 * 缓存服务类
 * 统一管理租户系统中的缓存操作，包括缓存键生成和缓存操作
 * <AUTHOR> <<EMAIL>>
 * @package app\tenant\service
 */
class CacheService extends BaseService
{
    private static ?CacheService $instance = null;

    // 用户token前缀
    protected string $userTokenPrefix = self::APP_PREFIX . 'user:';

    /**
     * 默认缓存时间（秒）
     * @var int
     */
    protected int $defaultExpire = 7200; // 默认2小时

    public function __construct()
    {
        parent::__construct();
    }

    public function __clone()
    {
    }

    public static function getInstance(): self
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }

        return self::$instance;
    }

    /**
     * 获取Redis实例
     * @return object
     */
    public function getRedis(): object
    {
        return Cache::store('redis')->handler();
    }

    /**
     * 统一管理租户用户缓存key
     * @param int $tenantId
     * @param int $userId
     * @param string $suffix 后缀
     * @return string
     */
    public function getUserCacheKey(int $tenantId, int $userId, string $suffix): string
    {
        return $this->userTokenPrefix . $tenantId . ':' . $userId . (empty($suffix) ? '' : ':' . $suffix);
    }

    /**
     * 设置租户用户信息缓存
     * @param array $userData
     * @param int|null $expire 过期时间
     * @return bool
     */
    public function setUserCacheInfo(array $userData, ?int $expire = null): bool
    {
        $userInfoCacheKey = $this->getUserCacheKey($userData['tenant_id'], $userData['id'], 'info');

        // 设置缓存
        return $this->set($userInfoCacheKey, $userData, $expire);
    }

    /**
     * 获取租户用户信息缓存
     * @param int $tenantId
     * @param int $userId
     * @return array
     */
    public function getUserCacheInfo(int $tenantId, int $userId): array
    {
        $userInfoCacheKey = $this->getUserCacheKey($tenantId, $userId, 'info');

        return $this->get($userInfoCacheKey);
    }

    /**
     * 设置缓存
     * @param string $key 缓存键
     * @param mixed $value 缓存值
     * @param int|null $expire 过期时间（秒）
     * @return bool
     */
    public function set(string $key, mixed $value, ?int $expire = null): bool
    {
        try {
            return Cache::set($key, $value, $expire ?? $this->defaultExpire);
        } catch (\Exception $e) {
            Log::error('Cache set error: ' . $e->getMessage());

            return false;
        }
    }

    /**
     * 获取缓存
     * @param string $key 缓存键
     * @return mixed
     */
    public function get(string $key): mixed
    {
        try {
            return Cache::get($key);
        } catch (\Exception $e) {
            Log::error('Cache get error: ' . $e->getMessage());

            return null;
        }
    }

    /**
     * 删除缓存
     * @param string $key 缓存键
     * @return bool
     */
    public function delete(string $key): bool
    {
        try {
            return Cache::delete($key);
        } catch (\Exception $e) {
            Log::error('Cache delete error: ' . $e->getMessage());

            return false;
        }
    }

    /**
     * 设置缓存过期时间
     * @param string $key 缓存键
     * @param int $expire 过期时间（秒）
     * @return bool
     */
    public function expire(string $key, int $expire): bool
    {
        try {
            return $this->getRedis()->expire($key, $expire);
        } catch (\Exception $e) {
            Log::error('Cache expire error: ' . $e->getMessage());

            return false;
        }
    }

    /**
     * 获取缓存过期时间
     * @param string $key 缓存键
     * @return int
     */
    public function ttl(string $key): int
    {
        try {
            // 获取缓存前缀
            $cache_prefix = config('cache.stores.redis.prefix');

            return $this->getRedis()->ttl($cache_prefix . $key);
        } catch (\Exception $e) {
            Log::error('Get cache ttl error: ' . $e->getMessage());

            return 0;
        }
    }
}
