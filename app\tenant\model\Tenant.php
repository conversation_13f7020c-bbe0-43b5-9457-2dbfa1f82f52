<?php
declare(strict_types = 1);

namespace app\tenant\model;

/**
 * 租户模型类
 * <AUTHOR> <<EMAIL>>
 * @package app\tenant\model
 */
class Tenant extends BaseModel
{
    protected $name = 'tenant';

    protected $field = [
        'code',
        'contact_person',
        'contact_phone',
        'description',
        'domain',
        'expire_time',
        'max_users',
        'name',
        'sort',
        'status',
    ];

    /**
     * 添加租户
     * @param $data
     * @return int
     */
    public static function addTenant($data): int
    {
        if ($res = self::create($data)) {
            return $res->id;
        }

        return 0;
    }

    /**
     * 更新租户信息
     * @param $id
     * @param $data
     * @return bool
     */
    public static function editTenant($id, $data): bool
    {
        unset(
            $data['id'],
            $data['create_at'],
        );

        return (bool)self::update($data, ['id' => $id]);
    }

    /**
     * 获取正常租户列表
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return array
     */
    public static function getActiveList(): array
    {
        return self::where('status', 1)->select()->toArray();
    }
}
