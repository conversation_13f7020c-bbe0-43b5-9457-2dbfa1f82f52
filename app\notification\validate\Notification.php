<?php
declare(strict_types = 1);

namespace app\notification\validate;

/**
 * 消息通知验证器
 * <AUTHOR> <<EMAIL>>
 * @package app\notification\validate
 */
class Notification extends BaseValidate
{
    protected $rule = [
        'id'            => 'require|number',
        'ids'           => 'require|array',
        'title'         => 'require|max:255',
        'content'       => 'require',
        'type'          => 'require|number|in:1,2,3,4',
        'action'        => 'require|in:like,dislike,cancel,read,unread',
        'receive_range' => 'require|number|in:1,2,3',
        'target_ids'    => 'requireIf:receive_range,2,3|array',
        'page'          => 'number',
        'limit'         => 'number',
        'category'      => 'number|in:1,2,3,4',
    ];

    protected $message = [
        'id.require'            => '消息ID不能为空',
        'id.number'             => '消息ID必须为数字',
        'ids.require'           => '消息ID列表不能为空',
        'ids.array'             => '消息ID列表必须为数组',
        'title.require'         => '标题不能为空',
        'title.max'             => '标题最多255个字符',
        'content.require'       => '内容不能为空',
        'type.require'          => '消息类型不能为空',
        'type.number'           => '消息类型必须为数字',
        'type.in'               => '消息类型不正确',
        'action.require'        => '操作类型不能为空',
        'action.in'             => '操作类型不正确',
        'receive_range.require' => '接收范围不能为空',
        'receive_range.number'  => '接收范围必须为数字',
        'receive_range.in'      => '接收范围不正确',
        'target_ids.requireIf'  => '目标ID不能为空',
        'target_ids.array'      => '目标ID必须为数组',
        'page.number'           => '页码必须为数字',
        'limit.number'          => '每页数量必须为数字',
        'category.number'       => '分类必须为数字',
        'category.in'           => '分类不正确',
    ];

    protected $scene = [
        'send'        => ['title', 'content', 'type', 'receive_range', 'target_ids'],
        'interact'    => ['id', 'action'],
        'read'        => ['id'],
        'list'        => ['page', 'limit', 'category'],
        'status'      => ['id', 'action'],
        'batchStatus' => ['ids', 'action'],
        'delete'      => ['id'],
        'batchDelete' => ['ids'],
    ];
}
