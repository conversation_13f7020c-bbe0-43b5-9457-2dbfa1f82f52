<?php
declare(strict_types = 1);

namespace app\tenant\validate;

/**
 * 设备数据验证器
 * <AUTHOR> Assistant
 * @package app\tenant\validate
 */
class Device extends BaseValidate
{
    protected $rule = [
        'id'       => 'number',
        'code'     => 'require|max:50|unique:tenant_device,code^tenant_id',
        'name'     => 'require|max:100|unique:tenant_device,name^tenant_id',
        'level'    => 'require|between:1,8',
        'location' => 'max:255',
        'depth'    => 'float',
        'status'   => 'require|in:0,1,2,3',
    ];

    protected $message = [
        'id.number'        => 'ID必须为数字',
        'code.require'     => '设备编码不能为空',
        'code.max'         => '设备编码长度不能超过50个字符',
        'code.unique'      => '设备编码已经存在',
        'name.require'     => '设备名称不能为空',
        'name.max'         => '设备名称长度不能超过100个字符',
        'name.unique'      => '设备名称已经存在',
        'level.require'    => '层位数不能为空',
        'level.between'    => '层位数必须为数字，且在1-8层之间',
        'location.require' => '位置不能为空',
        'location.max'     => '位置长度不能超过255个字符',
        'depth.require'    => '深度不能为空',
        'depth.float'      => '深度必须为浮点数',
        'status.require'   => '状态不能为空',
        'status.in'        => '状态值不正确',
    ];

    protected $scene = [
        'add'    => ['code', 'tenant_id', 'name', 'level', 'location', 'depth', 'status'],
        'edit'   => ['id', 'tenant_id', 'code', 'name', 'level', 'location', 'depth', 'status'],
        'status' => ['id', 'tenant_id', 'status'],
        'delete' => ['id', 'tenant_id'],
    ];
}
