<?php
declare(strict_types = 1);

namespace app\tenant\controller;

use app\common\controller\CommonController;
use app\tenant\service\MqttAclService;
use app\tenant\service\MqttAuthService;
use think\exception\HttpException;
use think\facade\Log;
use think\Response;

/**
 * EMQX认证控制器
 * 提供EMQX HTTP认证插件所需的认证和ACL检查接口
 * <AUTHOR> <<EMAIL>>
 * @package app\api\controller
 */
class Mqtt extends CommonController
{
    /**
     * 客户端认证
     * https://docs.emqx.com/zh/emqx/latest/access-control/authn/http.html
     * @return \think\Response
     */
    public function authenticate(): Response
    {
        // 只允许POST请求
        if ($this->request->isPost() === false) {
            throw new HttpException(403, 'Invalid request method');
        }

        try {
            // 获取认证服务
            $authService = MqttAuthService::getInstance();

            // 使用认证服务处理认证请求
            $result = $authService->authenticate($this->request->param());

            // 返回认证结果
            return json($result);
        } catch (\Exception $e) {
            Log::error('Authentication error: ' . $e->getMessage() . ' ' . $e->getTraceAsString());

            return json(['result' => 'deny', 'message' => 'Internal server error']);
        }
    }

    /**
     * 客户端授权
     * https://docs.emqx.com/zh/emqx/latest/access-control/authz/http.html
     * @return \think\Response
     */
    public function authorize(): Response
    {
        // 只允许POST请求
        if ($this->request->isPost() === false) {
            throw new HttpException(403, 'Invalid request method');
        }

        try {
            // 获取授权服务
            $aclService = MqttAclService::getInstance();

            // 使用授权服务处理授权请求
            $result = $aclService->authorize($this->request->param());

            // 返回授权结果
            return json($result);
        } catch (\Exception $e) {
            Log::debug('MQTT authorize params: ' . json_encode($this->request->param(), JSON_UNESCAPED_UNICODE));

            Log::error('Authorization error: ' . $e->getMessage() . ' ' . $e->getTraceAsString());

            return json(['result' => 'deny', 'message' => 'Internal server error']);
        }
    }
}
