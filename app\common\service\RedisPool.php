<?php
declare(strict_types = 1);

namespace app\common\service;

use Swoole\Coroutine\Channel;
use Swoole\Timer;
use think\facade\Config;
use think\facade\Log;

/**
 * Redis连接池
 * 用于在Swoole协程环境中管理Redis连接
 * <AUTHOR> <<EMAIL>>
 * @package app\common\service
 */
class RedisPool
{
    /**
     * 单例实例
     * @var ?RedisPool
     */
    private static ?RedisPool $instance = null;

    /**
     * 连接池
     * @var ?Channel
     */
    private ?Channel $pool = null;

    /**
     * 连接池大小
     * @var int
     */
    private int $size = 10;

    /**
     * 连接池中当前连接数
     * @var int
     */
    private int $currentCount = 0;

    /**
     * 连接保活时间（秒）
     * @var int
     */
    private int $keepAliveTime = 60;

    /**
     * 连接创建时间记录
     * @var array
     */
    private array $connectionTimes = [];

    /**
     * 活跃连接追踪
     * @var array
     */
    private array $activeConnections = [];

    /** @var ?int Keep alive timer ID */
    private ?int $keepAliveTimerId = null;

    /** @var ?int Check active connections timer ID */
    private ?int $checkConnectionsTimerId = null;

    /**
     * 构造函数
     * @param int $size 连接池大小
     */
    private function __construct(int $size = 10)
    {
        $this->size         = max(1, $size);
        $this->pool         = new Channel($this->size);
        $this->currentCount = 0;

        // 启动连接保活定时器
        if (class_exists('\Swoole\Timer')) {
            $this->keepAliveTimerId = Timer::tick(30000, function() { // 每30秒检查一次
                $this->keepAlive();
            });

            // 启动连接追踪定时器
            $this->checkConnectionsTimerId = Timer::tick(10000, function() { // 每10秒检查一次
                $this->checkActiveConnections();
            });
        }
    }

    /**
     * 获取单例实例
     * @param int $size 连接池大小
     * @return self
     */
    public static function getInstance(int $size = 10): self
    {
        if (self::$instance === null) {
            self::$instance = new self($size);
        }

        return self::$instance;
    }

    /**
     * 获取Redis连接
     * @param float $timeout 超时时间（秒）
     * @return \Redis|null
     */
    public function get(float $timeout = 3.0): ?\Redis
    {
        // 如果连接池中有可用连接，直接返回
        if (!$this->pool->isEmpty()) {
            $redis = $this->pool->pop($timeout);

            // Channel::pop() 在超时时会返回 false
            if ($redis === false) {
                Log::warning('Redis连接池获取连接超时');
                return null;
            }

            // 检查连接是否有效
            if (!$this->checkConnection($redis)) {
                $this->currentCount--;
                unset($this->activeConnections[spl_object_hash($redis)]);
                return $this->get($timeout); // 递归重试
            }

            // 记录活跃连接
            $this->activeConnections[spl_object_hash($redis)] = [
                'time' => time(),
                'cid' => \Swoole\Coroutine::getCid()
            ];

            return $redis;
        }

        // 如果连接池未满，创建新连接
        if ($this->currentCount < $this->size) {
            $redis = $this->createConnection();

            if ($redis) {
                $this->currentCount++;
                $this->connectionTimes[spl_object_hash($redis)] = time();
                $this->activeConnections[spl_object_hash($redis)] = [
                    'time' => time(),
                    'cid' => \Swoole\Coroutine::getCid()
                ];
                Log::info('Redis连接池创建新连接后状态：' . json_encode($this->stats())); // 移动日志到currentCount增加后
                return $redis;
            }
        }

        // 连接池已满，等待可用连接
        $redis = $this->pool->pop($timeout);

        // Channel::pop() 在超时时会返回 false
        if ($redis === false) {
            Log::warning('Redis连接池已满且等待超时，当前状态：' . json_encode($this->stats()));
            return null;
        }

        // 检查连接是否有效
        if (!$this->checkConnection($redis)) {
            $this->currentCount--;
            unset($this->activeConnections[spl_object_hash($redis)]);
            return $this->get($timeout); // 递归重试
        }

        // 记录活跃连接
        $this->activeConnections[spl_object_hash($redis)] = [
            'time' => time(),
            'cid' => \Swoole\Coroutine::getCid()
        ];

        return $redis;
    }

    /**
     * 归还Redis连接到连接池
     * @param \Redis $redis Redis连接
     * @return bool
     */
    public function put(\Redis $redis): bool
    {
        $hash = spl_object_hash($redis);

        // 检查连接是否有效
        if (!$this->checkConnection($redis)) {
            $this->currentCount--;
            unset($this->connectionTimes[$hash]);
            unset($this->activeConnections[$hash]);
            return false;
        }

        // 更新连接时间
        $this->connectionTimes[$hash] = time();
        unset($this->activeConnections[$hash]);

        // 归还连接到连接池
        return $this->pool->push($redis);
    }

    /**
     * 检查连接是否有效
     * @param \Redis $redis
     * @return bool
     */
    private function checkConnection(\Redis $redis): bool
    {
        try {
            return $redis->ping() === '+PONG';
        } catch (\Throwable $e) {
            Log::debug('Redis连接检查失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 连接保活
     * @return void
     */
    private function keepAlive(): void
    {
        $now = time();
        $connections = [];

        // 收集所有连接
        while (!$this->pool->isEmpty()) {
            $redis = $this->pool->pop(0.001);
            if ($redis instanceof \Redis) {
                $connections[] = $redis;
            }
        }

        foreach ($connections as $redis) {
            $hash = spl_object_hash($redis);
            $createTime = $this->connectionTimes[$hash] ?? 0;

            // 检查连接是否过期
            if ($now - $createTime > $this->keepAliveTime) {
                try {
                    $redis->close();
                } catch (\Throwable $e) {
                    Log::debug('关闭过期Redis连接异常: ' . $e->getMessage());
                }
                $this->currentCount--;
                unset($this->connectionTimes[$hash]);
                continue;
            }

            // 检查连接是否有效
            if ($this->checkConnection($redis)) {
                $this->pool->push($redis);
            } else {
                $this->currentCount--;
                unset($this->connectionTimes[$hash]);
            }
        }
    }

    /**
     * 检查活跃连接
     * @return void
     */
    private function checkActiveConnections(): void
    {
        $now = time();
        foreach ($this->activeConnections as $hash => $info) {
            // 如果连接超过30秒未归还，记录警告
            if ($now - $info['time'] > 30) {
                Log::warning("Redis连接可能泄漏: 协程ID={$info['cid']}, 创建时间=" . date('Y-m-d H:i:s', $info['time']));
            }
        }
    }

    /**
     * 关闭连接池
     * @return void
     */
    public function close(): void
    {
        // 清理定时器
        if ($this->keepAliveTimerId !== null && class_exists('\Swoole\Timer')) {
            Timer::clear($this->keepAliveTimerId);
            $this->keepAliveTimerId = null;
        }
        if ($this->checkConnectionsTimerId !== null && class_exists('\Swoole\Timer')) {
            Timer::clear($this->checkConnectionsTimerId);
            $this->checkConnectionsTimerId = null;
        }

        while (!$this->pool->isEmpty()) {
            $redis = $this->pool->pop(0.001);

            // Channel::pop() 在超时时会返回 false
            if ($redis === false) {
                continue;
            }

            if ($redis instanceof \Redis) {
                try {
                    $redis->close();
                } catch (\Throwable $e) {
                    // 忽略关闭连接时的异常
                    Log::debug('关闭Redis连接异常: ' . $e->getMessage());
                }
            }
        }
        $this->currentCount = 0;
    }

    /**
     * 创建新的Redis连接
     * @return \Redis|null
     */
    private function createConnection(): ?\Redis
    {
        try {
            $config = Config::get('cache.stores.redis');
            $redis  = new \Redis();

            // 设置连接超时时间
            $timeout = floatval($config['timeout']) ?? 1.0;

            // 连接Redis服务器
            if (!$redis->connect($config['host'], (int)$config['port'], $timeout)) {
                Log::error("Redis连接失败: {$config['host']}:{$config['port']}");

                return null;
            }

            // 如果有密码，进行认证
            if (!empty($config['password'])) {
                if (!$redis->auth($config['password'])) {
                    Log::error('Redis认证失败');
                    $redis->close();

                    return null;
                }
            }

            // 选择数据库
            if (isset($config['select'])) {
                if (!$redis->select((int)$config['select'])) {
                    Log::error("Redis选择数据库失败: {$config['select']}");
                    $redis->close();

                    return null;
                }
            }

            return $redis;
        } catch (\Throwable $e) {
            Log::error('创建Redis连接异常: ' . $e->getMessage());

            return null;
        }
    }

    /**
     * 获取连接池状态
     * @return array
     */
    public function stats(): array
    {
        return [
            'pool_size' => $this->size,
            'current_count' => $this->currentCount,
            'available' => $this->pool->length(),
            'waiting' => $this->pool->stats()['consumer_num'] ?? 0,
            'connection_times' => count($this->connectionTimes),
            'active_connections' => count($this->activeConnections),
            'active_connection_details' => $this->activeConnections
        ];
    }
}
