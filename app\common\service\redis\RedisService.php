<?php
declare(strict_types = 1);

namespace app\common\service\redis;

use Swoole\Coroutine;
use Swoole\Coroutine\Channel;
use think\facade\Cache;
use think\facade\Config;
use think\facade\Log;

/**
 * Redis连接池服务
 * 集成连接池管理、监控统计、便捷操作于一体的Redis服务
 * <AUTHOR> <<EMAIL>>
 * @package app\common\service\redis
 */
class RedisService
{
    /**
     * 单例实例
     * @var ?RedisService
     */
    private static ?RedisService $instance = null;

    /**
     * 连接池
     * @var ?Channel
     */
    private ?Channel $pool = null;

    /**
     * 连接池大小
     * @var int
     */
    private int $poolSize = 10;

    /**
     * 当前连接数
     * @var int
     */
    private int $currentCount = 0;

    /**
     * 是否启用监控
     * @var bool
     */
    private bool $enableMonitoring = true;

    /**
     * Redis配置
     * @var array
     */
    private array $config;

    /**
     * 统计数据
     * @var array
     */
    private array $stats = [
        // 操作统计
        'total_operations' => 0,
        'successful_operations' => 0,
        'failed_operations' => 0,
        'total_execution_time' => 0.0,
        'avg_execution_time' => 0.0,
        // 连接统计
        'connection_gets' => 0,
        'connection_puts' => 0,
        'connection_failures' => 0,
        'connection_times' => 0,
        'active_connections' => 0,
    ];

    /**
     * 构造函数
     */
    private function __construct()
    {
        $this->config = Config::get('cache.stores.redis', []);
    }

    /**
     * 获取单例实例
     * @return self
     */
    public static function getInstance(): self
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }

        return self::$instance;
    }

    /**
     * 初始化连接池
     */
    private function initPool(): void
    {
        if ($this->pool !== null) {
            return;
        }

        $this->pool = new Channel($this->poolSize);

        // 预创建连接
        for ($i = 0; $i < $this->poolSize; $i++) {
            $redis = $this->createConnection();
            if ($redis) {
                $this->pool->push($redis);
                $this->currentCount++;
            }
        }
    }

    /**
     * 创建Redis连接
     * @return \Redis|null
     */
    private function createConnection(): ?\Redis
    {
        try {
            $redis = new \Redis();

            $host = $this->config['host'] ?? '127.0.0.1';
            $port = (int)($this->config['port'] ?? 6379);
            $timeout = (float)($this->config['timeout'] ?? 3.0);
            $password = $this->config['password'] ?? '';
            $database = (int)($this->config['select'] ?? 0);

            // 连接Redis
            if (!$redis->connect($host, $port, $timeout)) {
                Log::error("Redis连接失败: {$host}:{$port}");
                return null;
            }

            // 认证
            if (!empty($password)) {
                if (!$redis->auth($password)) {
                    Log::error("Redis认证失败");
                    $redis->close();
                    return null;
                }
            }

            // 选择数据库
            if ($database > 0) {
                if (!$redis->select($database)) {
                    Log::error("Redis选择数据库失败: {$database}");
                    $redis->close();
                    return null;
                }
            }

            // 设置选项
            $redis->setOption(\Redis::OPT_SERIALIZER, \Redis::SERIALIZER_PHP);
            $redis->setOption(\Redis::OPT_PREFIX, $this->config['prefix'] ?? '');

            if ($this->enableMonitoring) {
                $this->stats['connection_times']++;
            }

            return $redis;
        } catch (\Exception $e) {
            Log::error('创建Redis连接异常: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * 获取Redis连接
     * @param float $timeout 超时时间
     * @return ?\Redis
     */
    private function getConnection(float $timeout = 3.0): ?\Redis
    {
        try {
            if ($this->enableMonitoring) {
                $this->stats['connection_gets']++;
            }

            // 检查是否在协程环境中
            if (class_exists('\Swoole\Coroutine') && Coroutine::getCid() !== -1) {
                // 动态初始化连接池
                if ($this->pool === null) {
                    $this->initPool();
                }

                // 尝试从连接池获取连接
                $redis = $this->pool->pop($timeout);

                if ($redis === false) {
                    // 超时或连接池为空，尝试创建新连接
                    $redis = $this->createConnection();
                    if (!$redis) {
                        if ($this->enableMonitoring) {
                            $this->stats['connection_failures']++;
                        }
                        Log::warning('无法获取Redis连接');
                        return null;
                    }
                }

                // 检查连接是否有效
                if (!$this->isConnectionValid($redis)) {
                    // 连接无效，重新创建
                    $redis = $this->createConnection();
                    if (!$redis) {
                        if ($this->enableMonitoring) {
                            $this->stats['connection_failures']++;
                        }
                        Log::warning('重新创建Redis连接失败');
                        return null;
                    }
                }

                if ($this->enableMonitoring) {
                    $this->stats['active_connections']++;
                }

                return $redis;
            }

            // 非协程环境，使用ThinkPHP的连接
            return Cache::store('redis')->handler();
        } catch (\Exception $e) {
            if ($this->enableMonitoring) {
                $this->stats['connection_failures']++;
            }
            Log::error('获取Redis连接异常: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 释放Redis连接
     * @param \Redis $redis
     */
    private function releaseConnection(\Redis $redis): void
    {
        // 只有在协程环境中且使用连接池时才需要归还连接
        if ($this->pool && class_exists('\Swoole\Coroutine') && Coroutine::getCid() !== -1) {
            try {
                if ($this->isConnectionValid($redis)) {
                    // 连接有效，归还到连接池
                    if ($this->pool->length() < $this->poolSize) {
                        $this->pool->push($redis);
                    } else {
                        // 连接池已满，关闭连接
                        $redis->close();
                        $this->currentCount--;
                    }
                } else {
                    // 连接无效，关闭连接
                    $redis->close();
                    $this->currentCount--;
                }

                if ($this->enableMonitoring) {
                    $this->stats['connection_puts']++;
                    $this->stats['active_connections'] = max(0, $this->stats['active_connections'] - 1);
                }
            } catch (\Throwable $e) {
                if ($this->enableMonitoring) {
                    $this->stats['connection_failures']++;
                }
                Log::debug('归还Redis连接异常: ' . $e->getMessage());
            }
        } else {
            // 非协程环境的连接由ThinkPHP自动管理，但我们仍然记录为"归还"以保持统计一致性
            if ($this->enableMonitoring) {
                $this->stats['connection_puts']++;
            }
        }
    }

    /**
     * 检查连接是否有效
     * @param \Redis $redis
     * @return bool
     */
    private function isConnectionValid(\Redis $redis): bool
    {
        try {
            return $redis->ping() === '+PONG';
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 执行Redis操作
     * 自动管理连接的获取和归还
     * @param callable $callback Redis操作回调函数
     * @param float $timeout 获取连接超时时间
     * @return mixed 操作结果
     * @throws \Exception
     */
    public function execute(callable $callback, float $timeout = 3.0)
    {
        $startTime = microtime(true);
        $redis = null;
        $success = false;

        try {
            if ($this->enableMonitoring) {
                $this->stats['total_operations']++;
            }

            $redis = $this->getConnection($timeout);

            if (!$redis) {
                throw new \Exception('无法获取Redis连接');
            }

            $result = $callback($redis);
            $success = true;

            return $result;
        } catch (\Exception $e) {
            if ($this->enableMonitoring) {
                $this->stats['failed_operations']++;
                Log::warning('Redis操作失败: ' . $e->getMessage());
            }
            throw $e;
        } finally {
            if ($redis) {
                $this->releaseConnection($redis);
            }

            if ($this->enableMonitoring) {
                $executionTime = microtime(true) - $startTime;
                $this->updateExecutionStats($executionTime, $success);
            }
        }
    }

    /**
     * 执行Redis事务操作
     * @param callable $callback 事务操作回调函数
     * @param float $timeout 获取连接超时时间
     * @return mixed 事务执行结果
     * @throws \Exception
     */
    public function transaction(callable $callback, float $timeout = 3.0)
    {
        return $this->execute(function($redis) use ($callback) {
            $redis->multi();

            try {
                $callback($redis);
                return $redis->exec();
            } catch (\Throwable $e) {
                $redis->discard();
                throw $e;
            }
        }, $timeout);
    }

    /**
     * 执行Redis管道操作
     * @param callable $callback 管道操作回调函数
     * @param float $timeout 获取连接超时时间
     * @return mixed 管道执行结果
     * @throws \Exception
     */
    public function pipeline(callable $callback, float $timeout = 3.0)
    {
        return $this->execute(function($redis) use ($callback) {
            $pipeline = $redis->pipeline();
            $callback($pipeline);
            return $pipeline->exec();
        }, $timeout);
    }

    /**
     * 更新执行统计
     * @param float $executionTime
     * @param bool $success
     */
    private function updateExecutionStats(float $executionTime, bool $success): void
    {
        if ($success) {
            $this->stats['successful_operations']++;
        }

        $this->stats['total_execution_time'] += $executionTime;

        // 计算平均执行时间
        if ($this->stats['total_operations'] > 0) {
            $this->stats['avg_execution_time'] = $this->stats['total_execution_time'] / $this->stats['total_operations'];
        }
    }

    /**
     * 计算成功率
     * @return float
     */
    private function calculateSuccessRate(): float
    {
        if ($this->stats['total_operations'] === 0) {
            return 0.0;
        }

        return round(($this->stats['successful_operations'] / $this->stats['total_operations']) * 100, 2);
    }

    /**
     * 获取连接池状态
     * @return array
     */
    public function getStats(): array
    {
        $poolStats = [];

        if ($this->pool) {
            $poolStats = [
                'pool_size' => $this->poolSize,
                'current_count' => $this->currentCount,
                'available' => $this->pool->length(),
                'waiting' => $this->pool->stats()['consumer_num'] ?? 0,
                'connection_times' => $this->stats['connection_times'],
                'active_connections' => $this->stats['active_connections'],
            ];
        } else {
            $poolStats = [
                'pool_size' => 0,
                'current_count' => 0,
                'available' => 0,
                'waiting' => 0,
                'connection_times' => $this->stats['connection_times'],
                'active_connections' => $this->stats['active_connections'],
            ];
        }

        // 合并连接池统计和操作统计
        return array_merge($poolStats, [
            'monitoring_enabled' => $this->enableMonitoring,
            'operation_stats' => [
                'total_operations' => $this->stats['total_operations'],
                'successful_operations' => $this->stats['successful_operations'],
                'failed_operations' => $this->stats['failed_operations'],
                'total_execution_time' => $this->stats['total_execution_time'],
                'avg_execution_time' => $this->stats['avg_execution_time'],
                'connection_gets' => $this->stats['connection_gets'],
                'connection_puts' => $this->stats['connection_puts'],
                'connection_failures' => $this->stats['connection_failures'],
            ],
            'success_rate' => $this->calculateSuccessRate(),
            'avg_execution_time_ms' => round($this->stats['avg_execution_time'] * 1000, 2),
        ]);
    }

    /**
     * 获取监控报告
     * @return string
     */
    public function getMonitoringReport(): string
    {
        $stats = $this->getStats();

        $swooleExists = class_exists('\Swoole\Coroutine');
        $coroutineId = $swooleExists ? Coroutine::getCid() : -1;
        $hasPool = $this->pool !== null;
        $isCoroutineEnv = $hasPool && $swooleExists && $coroutineId !== -1;
        $envType = $isCoroutineEnv ? '协程环境' : '非协程环境';

        $report = "=== Redis连接池监控报告 ===\n";
        $report .= "监控状态: " . ($stats['monitoring_enabled'] ? '启用' : '禁用') . "\n";
        $report .= "运行环境: {$envType}\n";

        if ($isCoroutineEnv) {
            $report .= "\n--- 连接池状态 ---\n";
            $report .= "连接池大小: {$stats['pool_size']}\n";
            $report .= "当前连接数: {$stats['current_count']}\n";
            $report .= "可用连接数: {$stats['available']}\n";
            $report .= "等待连接数: {$stats['waiting']}\n";
            $report .= "活跃连接数: {$stats['active_connections']}\n";
        } else {
            $report .= "\n--- 连接状态 ---\n";
            $report .= "连接类型: ThinkPHP默认连接\n";
            $report .= "连接管理: 框架自动管理\n";
        }

        $report .= "\n--- 操作统计 ---\n";
        $report .= "总操作数: {$stats['operation_stats']['total_operations']}\n";
        $report .= "成功操作数: {$stats['operation_stats']['successful_operations']}\n";
        $report .= "失败操作数: {$stats['operation_stats']['failed_operations']}\n";
        $report .= "成功率: {$stats['success_rate']}%\n";
        $report .= "平均执行时间: {$stats['avg_execution_time_ms']}ms\n";
        $report .= "\n--- 连接统计 ---\n";
        $report .= "连接获取次数: {$stats['operation_stats']['connection_gets']}\n";
        $report .= "连接归还次数: {$stats['operation_stats']['connection_puts']}\n";
        $report .= "连接失败次数: {$stats['operation_stats']['connection_failures']}\n";

        return $report;
    }

    /**
     * 启用/禁用监控
     * @param bool $enable
     */
    public function setMonitoring(bool $enable): void
    {
        $this->enableMonitoring = $enable;
    }

    /**
     * 重置统计数据
     */
    public function resetStats(): void
    {
        $this->stats = [
            'total_operations' => 0,
            'successful_operations' => 0,
            'failed_operations' => 0,
            'total_execution_time' => 0.0,
            'avg_execution_time' => 0.0,
            'connection_gets' => 0,
            'connection_puts' => 0,
            'connection_failures' => 0,
            'connection_times' => 0,
            'active_connections' => 0,
        ];
    }

    /**
     * 关闭连接池
     */
    public function close(): void
    {
        if ($this->pool) {
            // 关闭所有连接
            while (!$this->pool->isEmpty()) {
                $redis = $this->pool->pop(0.1);
                if ($redis && $redis instanceof \Redis) {
                    $redis->close();
                }
            }

            $this->pool->close();
            $this->pool = null;
            $this->currentCount = 0;
            $this->stats['active_connections'] = 0;
        }
    }

    // ==================== 便捷方法 ====================

    /**
     * 便捷方法：设置键值
     * @param string $key
     * @param mixed $value
     * @param int $expire
     * @return bool
     */
    public function set(string $key, $value, int $expire = 0): bool
    {
        return $this->execute(function($redis) use ($key, $value, $expire) {
            if ($expire > 0) {
                return $redis->setex($key, $expire, $value);
            }
            return $redis->set($key, $value);
        });
    }

    /**
     * 便捷方法：获取键值
     * @param string $key
     * @return mixed
     */
    public function get(string $key)
    {
        return $this->execute(function($redis) use ($key) {
            return $redis->get($key);
        });
    }

    /**
     * 便捷方法：删除键
     * @param string|array $key
     * @return int
     */
    public function del($key): int
    {
        return $this->execute(function($redis) use ($key) {
            return $redis->del($key);
        });
    }

    /**
     * 便捷方法：检查键是否存在
     * @param string $key
     * @return bool
     */
    public function exists(string $key): bool
    {
        return $this->execute(function($redis) use ($key) {
            return $redis->exists($key) > 0;
        });
    }

    /**
     * 便捷方法：设置过期时间
     * @param string $key
     * @param int $seconds
     * @return bool
     */
    public function expire(string $key, int $seconds): bool
    {
        return $this->execute(function($redis) use ($key, $seconds) {
            return $redis->expire($key, $seconds);
        });
    }

    /**
     * 便捷方法：哈希表操作
     * @param string $key
     * @param string $field
     * @param mixed $value
     * @return bool
     */
    public function hSet(string $key, string $field, $value): bool
    {
        return $this->execute(function($redis) use ($key, $field, $value) {
            return $redis->hSet($key, $field, $value);
        });
    }

    /**
     * 便捷方法：获取哈希表字段值
     * @param string $key
     * @param string $field
     * @return mixed
     */
    public function hGet(string $key, string $field)
    {
        return $this->execute(function($redis) use ($key, $field) {
            return $redis->hGet($key, $field);
        });
    }

    /**
     * 便捷方法：获取哈希表所有字段
     * @param string $key
     * @return array
     */
    public function hGetAll(string $key): array
    {
        return $this->execute(function($redis) use ($key) {
            return $redis->hGetAll($key) ?: [];
        });
    }

    /**
     * 便捷方法：集合添加成员
     * @param string $key
     * @param mixed ...$values
     * @return int
     */
    public function sAdd(string $key, ...$values): int
    {
        return $this->execute(function($redis) use ($key, $values) {
            return $redis->sAdd($key, ...$values);
        });
    }

    /**
     * 便捷方法：获取集合所有成员
     * @param string $key
     * @return array
     */
    public function sMembers(string $key): array
    {
        return $this->execute(function($redis) use ($key) {
            return $redis->sMembers($key) ?: [];
        });
    }

    /**
     * 便捷方法：从集合中移除成员
     * @param string $key
     * @param mixed ...$values
     * @return int
     */
    public function sRem(string $key, ...$values): int
    {
        return $this->execute(function($redis) use ($key, $values) {
            return $redis->sRem($key, ...$values);
        });
    }
}
