-- 表结构: system_configure
CREATE TABLE `system_configure` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `group` char(10) NOT NULL DEFAULT '' COMMENT '配置分组',
  `name` varchar(30) NOT NULL DEFAULT '' COMMENT '配置名称',
  `label` varchar(50) NOT NULL DEFAULT '' COMMENT '配置说明',
  `type` enum('input','textarea','password','select','checkbox','radio','switch','number','date','hidden') NOT NULL DEFAULT 'input' COMMENT '配置类型',
  `options` varchar(255) NOT NULL DEFAULT '' COMMENT '配置项（枚举类型时的选项值）',
  `value` text NOT NULL COMMENT '配置值',
  `size` char(1) NOT NULL DEFAULT '' COMMENT '控件大小',
  `placeholder` varchar(255) NOT NULL DEFAULT '' COMMENT '控件placeholder',
  `system` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否系统默认（不可删除）',
  `hidden` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否隐藏',
  `sort` smallint(5) NOT NULL DEFAULT '0' COMMENT '排序',
  `description` varchar(100) NOT NULL DEFAULT '' COMMENT '配置说明',
  `create_at` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_at` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `type` (`type`) USING BTREE,
  KEY `group` (`group`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='系统配置项表';
