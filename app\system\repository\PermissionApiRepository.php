<?php
declare(strict_types = 1);
namespace app\system\repository;

use app\system\model\PermissionApi as PermissionApiModel;
use think\facade\Cache;
use think\facade\Db;

/**
 * 权限API数据仓库类
 * 负责系统API权限相关的数据访问和操作
 * <AUTHOR> <<EMAIL>>
 * @package app\system\repository
 */
class PermissionApiRepository extends BaseRepository
{
    /**
     * 缓存前缀
     */
    public const CACHE_PREFIX = 'system:permission_api:';

    /**
     * 构造方法
     */
    public function __construct()
    {
        // 指定当前模型类
        parent::__construct(PermissionApiModel::class);
    }

    /**
     * 获取权限节点的API列表
     * @param int $permission_id 权限节点ID
     * @throws \Throwable
     * @return array
     */
    public function getApiList(int $permission_id): array
    {
        $cacheKey = self::CACHE_PREFIX . 'list:' . $permission_id;

        return Cache::remember($cacheKey, function() use ($permission_id) {
            return PermissionApiModel::getApiList($permission_id);
        }, 3600);
    }

    /**
     * 保存权限节点的API列表
     * @param int $permission_id 权限节点ID
     * @param array $apiList API列表数据
     * @throws \Exception
     * @return bool
     */
    public function saveApiList(int $permission_id, array $apiList): bool
    {
        Db::startTrans();

        try {
            $result = PermissionApiModel::saveApiList($permission_id, $apiList);

            if ($result) {
                Db::commit();
                // 清除缓存
                Cache::delete(self::CACHE_PREFIX . 'list:' . $permission_id);

                return true;
            }
            Db::rollback();

            return false;
        } catch (\Exception $e) {
            Db::rollback();

            throw $e;
        }
    }

    /**
     * 删除权限节点的API列表
     * @param int $permission_id 权限节点ID
     * @throws \Exception
     * @return bool
     */
    public function deleteApiList(int $permission_id): bool
    {
        Db::startTrans();

        try {
            $result = PermissionApiModel::deleteApisByPermissionId($permission_id);

            if ($result) {
                Db::commit();
                // 清除缓存
                Cache::delete(self::CACHE_PREFIX . 'list:' . $permission_id);

                return true;
            }
            Db::rollback();

            return false;
        } catch (\Exception $e) {
            Db::rollback();

            throw $e;
        }
    }

    /**
     * 批量删除多个权限节点的API
     * @param array $permission_ids 权限节点ID数组
     * @throws \Exception
     * @return bool
     */
    public function batchDeleteApiList(array $permission_ids): bool
    {
        if (empty($permission_ids)) {
            return true;
        }

        Db::startTrans();

        try {
            $result = PermissionApiModel::batchDeleteApisByPermissionIds($permission_ids);

            if ($result) {
                Db::commit();

                // 清除缓存
                foreach ($permission_ids as $permission_id) {
                    Cache::delete(self::CACHE_PREFIX . 'list:' . $permission_id);
                }

                return true;
            }
            Db::rollback();

            return false;
        } catch (\Exception $e) {
            Db::rollback();

            throw $e;
        }
    }
}
