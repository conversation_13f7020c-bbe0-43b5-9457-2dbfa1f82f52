<?php
declare(strict_types = 1);

namespace app\tenant\controller;

use app\common\service\RedisManager;
use app\common\controller\CommonController;
use think\App;
use think\Response;

/**
 * Redis连接池监控控制器
 * <AUTHOR> <<EMAIL>>
 * @package app\tenant\controller
 */
class RedisMonitor extends CommonController
{
    /**
     * Redis管理器实例
     * @var RedisManager|null
     */
    protected ?RedisManager $redisManager = null;

    /**
     * 构造函数
     */
    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->redisManager = RedisManager::getInstance();
    }

    /**
     * 获取Redis连接池状态
     * @return Response
     */
    public function stats(): Response
    {
        try {
            $stats = $this->redisManager->getStats();

            return $this->success([
                'pool_stats' => [
                    'pool_size' => $stats['pool_size'] ?? 0,
                    'current_count' => $stats['current_count'] ?? 0,
                    'available' => $stats['available'] ?? 0,
                    'waiting' => $stats['waiting'] ?? 0,
                    'connection_times' => $stats['connection_times'] ?? 0,
                    'active_connections' => $stats['active_connections'] ?? 0,
                ],
                'operation_stats' => $stats['operation_stats'] ?? [],
                'performance' => [
                    'success_rate' => $stats['success_rate'] ?? 0,
                    'avg_execution_time_ms' => $stats['avg_execution_time_ms'] ?? 0,
                ],
                'monitoring_enabled' => $stats['monitoring_enabled'] ?? false,
                'timestamp' => time(),
            ]);
        } catch (\Exception $e) {
            return $this->error('获取Redis统计信息失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取监控报告
     * @return Response
     */
    public function report(): Response
    {
        try {
            $report = $this->redisManager->getMonitoringReport();

            return $this->success([
                'report' => $report,
                'timestamp' => time(),
            ]);
        } catch (\Exception $e) {
            return $this->error('获取监控报告失败: ' . $e->getMessage());
        }
    }

    /**
     * 重置统计信息
     * @return Response
     */
    public function reset(): Response
    {
        try {
            $this->redisManager->resetStats();

            return $this->success('统计信息已重置');
        } catch (\Exception $e) {
            return $this->error('重置统计信息失败: ' . $e->getMessage());
        }
    }

    /**
     * 启用/禁用监控
     * @return Response
     */
    public function toggle(): Response
    {
        try {
            $enable = $this->request->param('enable/b', true);

            $this->redisManager->setMonitoring($enable);

            $status = $enable ? '启用' : '禁用';

            return $this->success("监控已{$status}");
        } catch (\Exception $e) {
            return $this->error('设置监控状态失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取实时监控数据（用于图表展示）
     * @return Response
     */
    public function realtime(): Response
    {
        try {
            $stats = $this->redisManager->getStats();

            // 构建实时监控数据
            $realtimeData = [
                'timestamp' => time(),
                'pool_usage' => [
                    'total' => $stats['pool_size'] ?? 0,
                    'used' => ($stats['pool_size'] ?? 0) - ($stats['available'] ?? 0),
                    'available' => $stats['available'] ?? 0,
                    'waiting' => $stats['waiting'] ?? 0,
                ],
                'operations' => [
                    'total' => $stats['operation_stats']['total_operations'] ?? 0,
                    'successful' => $stats['operation_stats']['successful_operations'] ?? 0,
                    'failed' => $stats['operation_stats']['failed_operations'] ?? 0,
                    'success_rate' => $stats['success_rate'] ?? 0,
                ],
                'connections' => [
                    'gets' => $stats['operation_stats']['connection_gets'] ?? 0,
                    'puts' => $stats['operation_stats']['connection_puts'] ?? 0,
                    'failures' => $stats['operation_stats']['connection_failures'] ?? 0,
                ],
                'performance' => [
                    'avg_execution_time' => $stats['avg_execution_time_ms'] ?? 0,
                    'total_execution_time' => ($stats['operation_stats']['total_execution_time'] ?? 0) * 1000,
                ],
            ];

            return $this->success($realtimeData);
        } catch (\Exception $e) {
            return $this->error('获取实时监控数据失败: ' . $e->getMessage());
        }
    }

    /**
     * 健康检查
     * @return Response
     */
    public function health(): Response
    {
        try {
            $stats = $this->redisManager->getStats();

            // 健康状态评估
            $health = [
                'status' => 'healthy',
                'issues' => [],
                'score' => 100,
            ];

            // 检查连接池使用率
            $poolSize = $stats['pool_size'] ?? 0;
            $available = $stats['available'] ?? 0;

            if ($poolSize > 0) {
                $usageRate = (($poolSize - $available) / $poolSize) * 100;

                if ($usageRate > 90) {
                    $health['issues'][] = '连接池使用率过高 (' . round($usageRate, 1) . '%)';
                    $health['score'] -= 20;
                } elseif ($usageRate > 80) {
                    $health['issues'][] = '连接池使用率较高 (' . round($usageRate, 1) . '%)';
                    $health['score'] -= 10;
                }
            }

            // 检查成功率
            $successRate = $stats['success_rate'] ?? 100;
            if ($successRate < 95) {
                $health['issues'][] = '操作成功率过低 (' . $successRate . '%)';
                $health['score'] -= 30;
            } elseif ($successRate < 98) {
                $health['issues'][] = '操作成功率较低 (' . $successRate . '%)';
                $health['score'] -= 15;
            }

            // 检查连接失败次数
            $connectionFailures = $stats['operation_stats']['connection_failures'] ?? 0;
            $totalConnections = $stats['operation_stats']['connection_gets'] ?? 1;
            $failureRate = ($connectionFailures / $totalConnections) * 100;

            if ($failureRate > 5) {
                $health['issues'][] = '连接失败率过高 (' . round($failureRate, 1) . '%)';
                $health['score'] -= 25;
            } elseif ($failureRate > 2) {
                $health['issues'][] = '连接失败率较高 (' . round($failureRate, 1) . '%)';
                $health['score'] -= 10;
            }

            // 检查平均执行时间
            $avgTime = $stats['avg_execution_time_ms'] ?? 0;
            if ($avgTime > 100) {
                $health['issues'][] = '平均执行时间过长 (' . $avgTime . 'ms)';
                $health['score'] -= 15;
            } elseif ($avgTime > 50) {
                $health['issues'][] = '平均执行时间较长 (' . $avgTime . 'ms)';
                $health['score'] -= 5;
            }

            // 确定整体状态
            if ($health['score'] >= 90) {
                $health['status'] = 'healthy';
            } elseif ($health['score'] >= 70) {
                $health['status'] = 'warning';
            } else {
                $health['status'] = 'critical';
            }

            return $this->success($health);
        } catch (\Exception $e) {
            return $this->error('健康检查失败: ' . $e->getMessage());
        }
    }

    /**
     * 测试Redis连接
     * @return Response
     */
    public function test(): Response
    {
        try {
            $testKey = 'redis_monitor_test_' . time();
            $testValue = 'test_value_' . uniqid();

            // 执行测试操作
            $startTime = microtime(true);

            $this->redisManager->set($testKey, $testValue, 60);
            $result = $this->redisManager->get($testKey);
            $this->redisManager->del($testKey);

            $endTime = microtime(true);
            $executionTime = round(($endTime - $startTime) * 1000, 2);

            if ($result === $testValue) {
                return $this->success([
                    'status' => 'success',
                    'message' => 'Redis连接测试成功',
                    'execution_time_ms' => $executionTime,
                ]);
            } else {
                return $this->error('Redis连接测试失败: 数据不匹配');
            }
        } catch (\Exception $e) {
            return $this->error('Redis连接测试失败: ' . $e->getMessage());
        }
    }
}
