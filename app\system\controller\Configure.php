<?php
declare(strict_types = 1);

namespace app\system\controller;

use app\system\repository\ConfigureRepository;
use app\system\validate\Configure as ConfigureValidate;
use think\App;
use think\exception\ValidateException;
use think\Response;

/**
 * 系统配置控制器
 * <AUTHOR> <<EMAIL>>
 * @package app\system\controller
 */
class Configure extends BaseController
{
    protected ConfigureRepository $repository;

    public function __construct(App $app, ConfigureRepository $repository)
    {
        parent::__construct($app);

        // 指定对应仓储类
        $this->repository = $repository;
    }

    /**
     * 控件类型获取方法
     * @return \think\Response
     */
    public function widgets(): Response
    {
        $result = [
            'list' => $this->repository->getWidgetTypes(),
        ];

        return $this->success($result);
    }

    /**
     * 配置项列表方法
     * @return \think\Response
     */
    public function list(): Response
    {
        $params   = $this->request->param();
        $pageNo   = $this->request->param('page', 1, 'intval');
        $pageSize = $this->request->param('pageSize', 20, 'intval');

        $result = $this->repository->pageList($params, $pageNo, $pageSize);

        return $this->success($result);
    }

    /**
     * 添加方法
     * @return \think\Response
     */
    public function add(): Response
    {
        $params = $this->request->param();

        try {
            validate(ConfigureValidate::class)->scene('add')->check($params);
        } catch (ValidateException $e) {
            return $this->error($e->getError());
        }

        $data = [
            'group'       => $params['group']       ?? '',
            'type'        => $params['type']        ?? 'input',
            'name'        => $params['name']        ?? '',
            'label'       => $params['label']       ?? '',
            'value'       => $params['value']       ?? '',
            'options'     => $params['options']     ?? '',
            'placeholder' => $params['placeholder'] ?? '',
            'description' => $params['description'] ?? '',
            'sort'        => isset($params['sort']) ? intval($params['sort']) : 0,
            'system'      => 0,
            'create_at'   => time(),
            'status'      => isset($params['status']) ? intval($params['status']) : 1,
        ];

        if ($this->repository->createData($data)) {
            // 写入事件日志
            $this->event_log('添加配置项', $params, []);

            return $this->success('添加成功');
        }

        return $this->error('添加失败');
    }

    /**
     * 编辑方法
     * @return \think\Response
     */
    public function edit(): Response
    {
        $params = $this->request->param();

        try {
            validate(ConfigureValidate::class)->scene('edit')->check($params);
        } catch (ValidateException $e) {
            return $this->error($e->getError());
        }

        $info = $this->repository->getInfo($params['id']);

        if (empty($info)) {
            return $this->error('配置项不存在');
        }

        if ($info['status'] == -1) {
            return $this->error('该配置项已删除，无法操作');
        }

        $data = [
            'group'       => $params['group']       ?? '',
            'type'        => $params['type']        ?? '',
            'name'        => $params['name']        ?? '',
            'label'       => $params['label']       ?? '',
            'value'       => $params['value']       ?? '',
            'options'     => $params['options']     ?? '',
            'placeholder' => $params['placeholder'] ?? '',
            'description' => $params['description'] ?? '',
            'sort'        => isset($params['sort']) ? intval($params['sort']) : 0,
            'status'      => isset($params['status']) ? intval($params['status']) : 1,
            'update_at'   => time(),
        ];

        if ($this->repository->updateData($params['id'], $data)) {
            // 写入事件日志
            $this->event_log('更新配置项', $params, []);

            return $this->success('更新成功');
        }

        return $this->error('更新失败');
    }

    /**
     * 设置状态（禁用启用）
     * @return \think\Response
     */
    public function status(): Response
    {
        $params = $this->request->param();

        try {
            validate(ConfigureValidate::class)->scene('status')->check($params);
        } catch (ValidateException $e) {
            return $this->error($e->getError());
        }

        if ($this->repository->setStatus($params['id'], $params['status'])) {
            // 写入事件日志
            $this->event_log('设置状态', $params, []);

            return $this->success($params['status'] == 0 ? '禁用成功' : '启用成功');
        }

        return $this->error('操作失败');
    }

    /**
     * 删除方法（软删除）
     * @return \think\Response
     */
    public function delete(): Response
    {
        $params = $this->request->param();

        try {
            validate(ConfigureValidate::class)->scene('delete')->check($params);
        } catch (ValidateException $e) {
            return $this->error($e->getError());
        }

        if ($this->repository->recycle($params['id'])) {
            // 写入事件日志
            $this->event_log('删除配置项', $params, []);

            return $this->success('删除成功');
        }

        return $this->error('删除失败');
    }
}
