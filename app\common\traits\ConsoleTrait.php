<?php
declare(strict_types = 1);

namespace app\common\traits;

use think\console\Output;
use think\facade\Log;

/**
 * 控制台打印、日志Trait
 * 用于统一处理控制台输出和系统日志记录
 * @package app\common\traits
 */
trait ConsoleTrait
{
    /**
     * 输出实例
     * @var Output|null
     */
    protected $output;

    /**
     * 设置输出实例
     * @param Output $output
     * @return void
     */
    public function setOutput(Output $output): void
    {
        $this->output = $output;
    }

    /**
     * 记录信息级别的日志
     * @param string $message 日志信息
     * @param array $context 上下文信息
     * @return void
     */
    public function logInfo(string $message, array $context = []): void
    {
        // 控制台输出
        if ($this->output instanceof Output) {
            $this->output->info(date('Y-m-d H:i:s') . ' ' . $message);
        }

        // 系统日志记录
        Log::info($message, $context);
    }

    /**
     * 记录警告级别的日志
     * @param string $message 日志信息
     * @param array $context 上下文信息
     * @return void
     */
    public function logWarning(string $message, array $context = []): void
    {
        // 控制台输出
        if ($this->output instanceof Output) {
            $this->output->warning(date('Y-m-d H:i:s') . ' ' . $message);
        }

        // 系统日志记录
        Log::warning($message, $context);
    }

    /**
     * 记录错误级别的日志
     * @param string $message 日志信息
     * @param array $context 上下文信息
     * @return void
     */
    public function logError(string $message, array $context = []): void
    {
        // 控制台输出
        if ($this->output instanceof Output) {
            $this->output->error(date('Y-m-d H:i:s') . ' ' . $message);
        }

        // 系统日志记录
        Log::error($message, $context);
    }

    /**
     * 记录调试级别的日志
     * @param string $message 日志信息
     * @param array $context 上下文信息
     * @return void
     */
    public function logDebug(string $message, array $context = []): void
    {
        // 控制台输出
        if ($this->output instanceof Output) {
            $this->output->writeln(date('Y-m-d H:i:s') . ' ' . $message);
        }

        // 系统日志记录
        Log::debug($message, $context);
    }
}
