<?php
declare(strict_types = 1);
namespace app\system\controller;

use app\system\model\PermissionRole as PermissionRoleModel;
use app\system\repository\RoleRepository;
use app\system\validate\Role as RoleValidate;
use think\App;
use think\exception\ValidateException;
use think\Response;

/**
 * 角色组管理控制器
 * <AUTHOR> <<EMAIL>>
 * @package app\system\controller
 */
class Role extends BaseController
{
    /**
     * @var RoleRepository
     */
    protected RoleRepository $repository;

    public function __construct(App $app)
    {
        parent::__construct($app);

        // 指定对应仓储类
        $this->repository = new RoleRepository();
    }

    /**
     * 获取单个或多个角色组的权限节点id
     * @return \think\Response
     */
    public function permissions(): Response
    {
        $params = $this->request->param();

        if (empty($params['role_id'])) {
            return $this->error('参数【role_id】不能为空');
        }

        $role_id_arr = is_array($params['role_id']) ? $params['role_id'] : explode(',', (string)$params['role_id']);

        // 或者对应角色组的权限节点ids
        $list = PermissionRoleModel::getPermissions($role_id_arr);
        $list = implode(',', $list);

        return $this->success($list);
    }

    /**
     * 角色列表
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return \think\Response
     */
    public function list(): Response
    {
        $params   = $this->request->param();
        $pageNo   = $this->request->param('page', 1, 'intval');
        $pageSize = $this->request->param('pageSize', 20, 'intval');

        $result = $this->repository->getList($params, $pageNo, $pageSize);

        return $this->success($result);
    }

    /**
     * 添加角色
     * @return \think\Response
     */
    public function add(): Response
    {
        $params = $this->request->param();

        try {
            validate(RoleValidate::class)->scene('add_role')->check($params);
        } catch (ValidateException $e) {
            // 验证失败 输出错误信息
            return $this->error($e->getError());
        }

        $data = [
            'name'        => isset($params['name']) ? trim($params['name']) : '',
            'alias'       => isset($params['alias']) ? trim($params['alias']) : '',
            'description' => isset($params['description']) ? trim($params['description']) : '',
            'sort'        => isset($params['sort']) ? intval($params['sort']) : 0,
            'status'      => isset($params['status']) ? intval($params['status']) : 1,
        ];

        if ($this->repository->createRole($data)) {
            // 写入事件日志
            $this->event_log('添加角色组', $params, []);

            return $this->success('添加成功');
        }

        return $this->error('添加失败');
    }

    /**
     * 编辑角色
     * @return \think\Response
     */
    public function edit(): Response
    {
        $params = $this->request->param();

        try {
            validate(RoleValidate::class)->scene('edit_role')->check($params);
        } catch (ValidateException $e) {
            // 验证失败 输出错误信息
            return $this->error($e->getError());
        }

        $roleInfo = $this->repository->getInfo($params['id']);

        if (empty($roleInfo)) {
            return $this->error('角色不存在');
        }

        if ($roleInfo['status'] == -1) {
            return $this->error('该角色已删除，无法操作');
        }

        $data = [
            'name'        => isset($params['name']) ? trim($params['name']) : '',
            'alias'       => isset($params['alias']) ? trim($params['alias']) : '',
            'description' => isset($params['description']) ? trim($params['description']) : '',
            'sort'        => isset($params['sort']) ? intval($params['sort']) : 0,
            'status'      => isset($params['status']) ? intval($params['status']) : 1,
        ];

        if ($this->repository->updateRole($params['id'], $data)) {
            // 写入事件日志
            $this->event_log('更新角色组', $params, []);

            return $this->success('更新成功');
        }

        return $this->error('更新失败');
    }

    /**
     * 角色排序
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return \think\Response
     */
    public function sort(): Response
    {
        $params = $this->request->param();

        try {
            validate(RoleValidate::class)->scene('sort')->check($params);
        } catch (ValidateException $e) {
            // 验证失败 输出错误信息
            return $this->error($e->getError());
        }

        $info = $this->repository->getInfo($params['id']);

        if (empty($info)) {
            return $this->error('角色不存在');
        }

        if ($info['status'] == -1) {
            return $this->error('该角色已删除，无法操作');
        }

        if ($this->repository->updateSort($params['id'], isset($params['sort']) ? intval($params['sort']) : 0)) {
            // 写入事件日志
            $this->event_log('更新角色排序', $params, []);

            return $this->success('更新成功');
        }

        return $this->error('更新失败');
    }

    /**
     * 设置角色状态（禁用启用）
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return \think\Response
     */
    public function status(): Response
    {
        $params = $this->request->param();

        try {
            validate(RoleValidate::class)->scene('status')->check($params);
        } catch (ValidateException $e) {
            // 验证失败 输出错误信息
            return $this->error($e->getError());
        }

        $result = $params['status'] == 0 ?
            $this->repository->disable($params['id']) :
            $this->repository->enable($params['id']);

        if ($result) {
            // 写入事件日志
            $this->event_log('设置角色组状态', $params, []);

            return $this->success($params['status'] == 0 ? '禁用成功' : '启用成功');
        }

        return $this->error('操作失败');
    }

    /**
     * 删除角色（软删除）
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return \think\Response
     */
    public function delete(): Response
    {
        $params = $this->request->param();

        if (empty($params['id'])) {
            return $this->error('参数ID不能为空');
        }

        if ($this->repository->deleteData($params['id'])) {
            // 写入事件日志
            $this->event_log('删除角色组', $params, []);

            return $this->success('删除成功');
        }

        return $this->error('删除失败');
    }

    /**
     * 角色组节点授权
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return \think\Response
     */
    public function access(): Response
    {
        $params = $this->request->param();

        if (empty($params['role_id'])) {
            return $this->error('角色ID不能为空');
        }

        $info = $this->repository->getInfo($params['role_id']);

        if (empty($info)) {
            return $this->error('角色不存在');
        }

        if ($info['status'] == -1) {
            return $this->error('该角色已删除，无法操作');
        }

        $data = [
            'visible_access' => $params['visible_access'] ?? 0,
            'visible_scope'  => $params['visible_scope']  ?? '',
            'visible_rule'   => $params['visible_rule']   ?? '',
            'dashboard'      => $params['dashboard']      ?? 'default',
        ];

        if ($this->repository->setPermissions($params['role_id'], $params['permissions'] ?? [], $data)) {
            // 写入事件日志
            $this->event_log('角色组节点授权', $params, []);

            return $this->success('更新成功');
        }

        return $this->error('更新失败');
    }
}
