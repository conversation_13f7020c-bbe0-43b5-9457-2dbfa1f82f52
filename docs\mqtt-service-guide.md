# MQTT服务指南

## 简介

MQTT服务是一个基于MQTT协议的消息服务实现，主要用于处理物联网设备的实时通信。该服务采用单例模式设计，确保全局只有一个MQTT客户端实例，支持自动重连、消息发布、订阅等功能。

## 安装依赖

本服务基于 [simps/mqtt](https://github.com/simps/mqtt) 扩展包实现，需要通过 Composer 安装：~~~~

```bash
composer require simps/mqtt ^1.2
```

安装要求：
- PHP >= 7.4
- Swoole >= 4.4.19
- OpenSSL（如需启用TLS加密传输）

## 配置说明

配置文件位于 `config/mqtt.php`，主要配置项包括：

```php
return [
    'host' => env('MQTT_HOST', 'localhost'),      // MQTT服务器地址
    'port' => env('MQTT_PORT', 1883),             // MQTT服务器端口
    'username' => env('MQTT_USERNAME', ''),        // MQTT认证用户名
    'password' => env('MQTT_PASSWORD', ''),        // MQTT认证密码
    'client_id_prefix' => env('MQTT_CLIENT_ID_PREFIX', 'helio_'), // 客户端ID前缀
    'keepalive' => env('MQTT_KEEPALIVE', 60),     // 保活时间（秒）
    'connect_timeout' => env('MQTT_CONNECT_TIMEOUT', 5), // 连接超时时间（秒）
    'reconnect_delay' => env('MQTT_RECONNECT_DELAY', 10), // 重连延迟时间（秒）
    'topic_prefix' => env('MQTT_TOPIC_PREFIX', 'helio/'), // 主题前缀
    'qos' => env('MQTT_QOS', 2),                  // 默认QoS级别
    'retain' => env('MQTT_RETAIN', false),         // 默认是否保留消息
    'clean_session' => env('MQTT_CLEAN_SESSION', true), // 是否清理会话
    'topic_patterns' => [                          // 主题模式定义
        'tenant' => '{prefix}tenant/{tenant_id}/#',           // 租户主题模式
        'user' => '{prefix}tenant/{tenant_id}/user/{user_id}/#', // 用户主题模式
        'device' => '{prefix}tenant/{tenant_id}/device/{device_id}/#', // 设备主题模式
        'device_status' => '{prefix}tenant/{tenant_id}/device/{device_id}/status', // 设备状态监控主题
        'device_control' => '{prefix}tenant/{tenant_id}/device/{device_id}/control', // 设备控制指令主题
        'device_data' => '{prefix}tenant/{tenant_id}/device/{device_id}/data', // 设备数据传输主题
        'heartbeat' => '{prefix}tenant/{tenant_id}/heartbeat/#' // 心跳主题模式
    ]
];
```

## 主题设计

系统采用层级化的主题设计，主要包括以下类型：

1. 租户主题：`{prefix}tenant/{tenant_id}/#`
   - 用于发布租户级别的消息
   - 适用于广播给租户下所有设备
   - 消息格式：
     ```json
     {
       "type": "broadcast",
       "timestamp": "2024-01-20T10:00:00Z",
       "payload": {
         "message": "系统维护通知",
         "level": "info"
       }
     }
     ```

2. 用户主题：`{prefix}tenant/{tenant_id}/user/{user_id}/#`
   - 用于发布用户级别的消息
   - 适用于特定用户的消息推送
   - 子主题说明：
     - notification：用户通知消息
     - command：用户操作指令
   - 消息格式：
     ```json
     {
       "type": "notification",
       "timestamp": "2024-01-20T10:00:00Z",
       "payload": {
         "title": "设备告警",
         "content": "设备温度过高",
         "level": "warning"
       }
     }
     ```

3. 设备主题：`{prefix}tenant/{tenant_id}/device/{device_id}/#`
   - 用于设备相关的消息通信
   - 子主题包括：
     - device_status：设备状态监控
       - 主题：`{prefix}tenant/{tenant_id}/device/{device_id}/status`
       - 用途：监控设备的实时状态信息，包括在线状态、电量、信号强度等
       - 消息格式：
       ```json
       {
         "type": "status",
         "timestamp": "2024-01-20T10:00:00Z",
         "payload": {
           "status": "online",
           "battery": 85,
           "signal": -65,
           "version": "1.2.0"
         }
       }
       ```
     - device_control：设备控制指令
       - 主题：`{prefix}tenant/{tenant_id}/device/{device_id}/control`
       - 用途：发送控制命令到设备，如重启、配置更新等
       - 消息格式：
       ```json
       {
         "type": "control",
         "timestamp": "2024-01-20T10:00:00Z",
         "payload": {
           "command": "restart",
           "params": {
             "delay": 5
           }
         }
       }
       ```
     - device_data：设备数据传输
       - 主题：`{prefix}tenant/{tenant_id}/device/{device_id}/data`
       - 用途：设备定期上报的传感器数据、运行状态等信息
       - 消息格式：
       ```json
       {
         "type": "data",
         "timestamp": "2024-01-20T10:00:00Z",
         "payload": {
           "temperature": 25.6,
           "humidity": 65,
           "pressure": 1013.2
         }
       }
       ```

4. 心跳主题：`{prefix}tenant/{tenant_id}/heartbeat/#`
   - 用于设备心跳检测(监控设备在线状态)
   - 心跳主题规范
   主题格式：`{prefix}tenant/{tenant_id}/heartbeat/{device_type}`
   - `{prefix}`: 配置中的主题前缀
   - `{tenant_id}`: 租户唯一标识
   - `{device_type}`: 设备类型（pc/mobile/iot）
   
   该主题用于接收租户各类终端设备的心跳包，服务端通过订阅租户专属心跳主题实现状态监控。客户端应每分钟发送一次心跳数据，格式为JSON：
   ```json
   {
     "device_id": "设备唯一ID",
     "timestamp": 1672531200,
     "status": "online/offline"
   }
   ```
   - 消息格式：
     ```json
     {
       "type": "heartbeat",
       "timestamp": "2024-01-20T10:00:00Z",
       "payload": {
         "device_id": "device_001",
         "uptime": 3600,
         "rssi": -75
       }
     }
     ```

## QoS级别说明

MQTT提供三种QoS级别，根据业务需求选择：

- QoS 0：最多发送一次，不保证到达
  - 适用于：实时数据采集，允许丢失部分数据
  - 优点：性能最好，开销最小

- QoS 1：至少发送一次，保证到达，可能重复
  - 适用于：重要的控制命令，允许重复
  - 优点：保证消息到达

- QoS 2：只发送一次，保证到达且不重复
  - 适用于：关键业务数据，不允许丢失和重复
  - 优点：最高的服务质量保证

## 心跳机制

系统通过心跳机制监控设备在线状态：

1. 心跳发送
   - 设备定期发送心跳消息
   - 使用 `publishHeartbeat()` 方法发送
   - 推荐心跳间隔：30-60秒
   - 心跳消息包含：
     - 设备ID
     - 时间戳
     - 运行时间
     - 信号强度

2. 心跳超时处理
   - 服务端监控心跳消息
   - 超过指定时间未收到心跳，认为设备离线
   - 默认超时时间：心跳间隔的3倍
   - 超时处理流程：
     1. 更新设备状态为离线
     2. 触发设备离线事件
     3. 发送离线通知
     4. 记录离线日志

3. 心跳异常处理
   - 异常类型：
     - 心跳格式错误
     - 设备ID不匹配
     - 时间戳异常
   - 处理策略：
     - 记录错误日志
     - 发送错误通知
     - 可选择重置连接

## 断线重连机制

系统实现了自动重连机制：

1. 连接断开检测
   - 通过keepalive机制检测连接状态
   - 连接断开时自动触发重连

2. 重连策略
   - 初次重连立即尝试
   - 之后按照配置的延迟时间进行重试
   - 可通过 `reconnect_delay` 配置重连间隔

## 使用示例

### 1. 获取MQTT服务实例

```php
$mqttService = MqttService::getInstance();
```

### 2. 发布消息

```php
// 发布到租户主题
$message = [
    'type' => 'broadcast',
    'timestamp' => date('c'),
    'payload' => [
        'message' => '系统维护通知',
        'level' => 'info'
    ]
];
$mqttService->publishToTenant($tenantId, json_encode($message));

// 发布到用户主题
$message = [
    'type' => 'notification',
    'timestamp' => date('c'),
    'payload' => [
        'title' => '设备告警',
        'content' => '设备温度过高',
        'level' => 'warning'
    ]
];
$mqttService->publishToUser($tenantId, $userId, json_encode($message));

// 发布设备控制命令
$message = [
    'type' => 'control',
    'timestamp' => date('c'),
    'payload' => [
        'command' => 'restart',
        'params' => ['delay' => 5]
    ]
];
$mqttService->publishToDevice($tenantId, $deviceId, json_encode($message));

// 发送心跳
$message = [
    'type' => 'heartbeat',
    'timestamp' => date('c'),
    'payload' => [
        'device_id' => $deviceId,
        'uptime' => 3600,
        'rssi' => -75
    ]
];
$mqttService->publishHeartbeat($tenantId, json_encode($message));
```

### 3. 订阅主题

```php
// 订阅指定主题
$mqttService->subscribe($topic);

// 订阅设备状态主题
$statusTopic = sprintf('%stenant/%s/device/%s/status', $topicPrefix, $tenantId, $deviceId);
$mqttService->subscribe($statusTopic);

// 订阅带回调的主题
$mqttService->subscribe($topic, function($message) {
    $data = json_decode($message, true);
    switch($data['type']) {
        case 'status':
            handleDeviceStatus($data['payload']);
            break;
        case 'data':
            processDeviceData($data['payload']);
            break;
        case 'heartbeat':
            updateDeviceHeartbeat($data['payload']);
            break;
    }
});
```

### 4. 错误处理

```php
try {
    $mqttService->publishToDevice($tenantId, $deviceId, $message);
} catch (MqttConnectionException $e) {
    // 处理连接错误
    Log::error('MQTT连接失败：' . $e->getMessage());
    // 尝试重新连接
    $mqttService->reconnect();
} catch (MqttPublishException $e) {
    // 处理发布错误
    Log::error('消息发布失败：' . $e->getMessage());
    // 实现重试逻辑
    retry(function() use ($mqttService, $tenantId, $deviceId, $message) {
        $mqttService->publishToDevice($tenantId, $deviceId, $message);
    }, 3, 1000);
}
```

## 最佳实践

1. QoS级别选择
   - 普通数据采集：使用QoS 0
   - 设备控制命令：使用QoS 1
   - 关键业务数据：使用QoS 2

2. 主题设计
   - 遵循层级化设计
   - 使用有意义的主题名
   - 避免过深的主题层级

3. 心跳配置
   - 根据网络环境调整心跳间隔
   - 建议心跳间隔小于keepalive时间

4. 异常处理
   - 实现消息发送失败重试
   - 记录关键操作日志
   - 合理设置超时时间

## 注意事项

1. 安全性
   - 使用TLS加密传输
     - 配置SSL/TLS证书
     - 启用双向认证
     - 定期更新证书
   - 配置访问权限控制
     - 实现用户认证
     - 设置ACL规则
     - 限制订阅范围
   - 避免敏感信息明文传输
     - 加密敏感数据
     - 使用安全的密钥管理
     - 实现数据脱敏
   - 防止恶意攻击
     - 限制连接频率
     - 监控异常行为
     - 实现IP黑名单

2. 性能优化
   - 合理使用QoS级别
     - 普通数据：QoS 0
     - 重要通知：QoS 1
     - 关键指令：QoS 2
   - 避免过频繁的消息发送
     - 实现消息缓存
     - 批量处理数据
     - 设置发送间隔
   - 控制消息大小
     - 压缩大数据
     - 分片传输
     - 优化数据结构
   - 连接优化
     - 使用长连接
     - 实现连接池
     - 自动断线重连

3. 运维建议
   - 监控MQTT服务状态
     - 服务器资源监控
     - 连接数监控
     - 消息队列监控
   - 定期检查连接情况
     - 检查心跳状态
     - 清理僵尸连接
     - 分析连接质量
   - 分析消息吞吐量
     - 统计消息数量
     - 监控消息延迟
     - 分析峰值流量
   - 日志管理
     - 记录关键操作
     - 设置日志级别
     - 实现日志轮转
