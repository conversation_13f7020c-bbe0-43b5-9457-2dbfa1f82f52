<?php
declare(strict_types = 1);

namespace app\tenant\model;

use app\common\service\ClientService;
use app\common\service\FingerService;
use app\tenant\context\TenantContext;

/**
 * 租户操作日志模型类
 * <AUTHOR> <<EMAIL>>
 * @package app\tenant\model
 */
class OperationLog extends BaseModel
{
    protected $name = 'tenant_operation_log';

    protected $pk = 'id';

    protected $createTime = 'operate_time';

    protected $autoWriteTimestamp = true;

    protected $updateTime = false;

    /**
     * 记录操作日志
     * @param string $title 操作名称
     * @param array $data 相关数据
     * @return bool
     */
    public static function record(string $title = 'success', array $data = []): bool
    {
        // 从上下文中获取传递过来的数据
        $tenantUser = TenantContext::getInstance()->getUserInfo();

        return self::create([
            'tenant_id'  => $tenantUser['tenant_id'] ?? 0,
            'user_id'    => $tenantUser['id']        ?? 0,
            'username'   => $tenantUser['username']  ?? 'unknown',
            'title'      => $title,
            'ip'         => request()->ip(),
            'path'       => request()->url(),
            'method'     => request()->method(),
            'request_id' => request()->requestId ?: '',
            'client_id'  => ClientService::getClientId(),
            'finger_id'  => FingerService::getClientFingerId(),
            'payload'    => json_encode(request()->param(), JSON_UNESCAPED_UNICODE),
            'data'       => json_encode($data, JSON_UNESCAPED_UNICODE),
        ]) != false;
    }
}
