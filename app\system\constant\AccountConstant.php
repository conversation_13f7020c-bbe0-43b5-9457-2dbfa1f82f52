<?php
declare(strict_types = 1);
namespace app\system\constant;

use app\common\constant\CommonConstant;
use app\common\constant\ResultConstant;

/**
 * 账号验证相关常量 (10100-10199)
 * <AUTHOR> <<EMAIL>>
 * @package app\system\constant
 */
class AccountConstant extends CommonConstant
{
    public const ACCOUNT_LOGIN_SUCCESS = ['code' => ResultConstant::RESULT_SUCCESS_CODE, 'message' => '登录成功'];

    public const ACCOUNT_NOT_EXIST = ['code' => 10100, 'message' => '账号不存在'];

    public const ACCOUNT_FROZEN = ['code' => 10101, 'message' => '该账号已被冻结'];

    public const ACCOUNT_DELETED = ['code' => 10102, 'message' => '该账号已被封存'];

    public const ACCOUNT_STATUS_ABNORMAL = ['code' => 10103, 'message' => '账号状态异常'];

    public const ACCOUNT_PASSWORD_INVALID = ['code' => 10104, 'message' => '帐号或密码错误'];

    public const ACCOUNT_NO_ROLE = ['code' => 10105, 'message' => '未赋予对应的权限组，请联系管理员'];

    public const ACCOUNT_NO_ROLE_PERMISSION = ['code' => 10106, 'message' => '该角色组无任何节点权限，请联系管理员'];

    public const ACCOUNT_USERID_EMPTY = ['code' => 10107, 'message' => '用户ID参数错误'];

    // 设备被拉黑
    public const ACCOUNT_BLACKLISTED = ['code' => 10109, 'message' => '您当前的设备已被禁用，请联系管理员', 'label' => 'blocked'];

    /**
     * 根据code获取message
     * @param int|string $code
     * @return string
     */
    public static function getMessage(int|string $code): string
    {
        foreach (static::getConstants() as $constant) {
            if ($constant['code'] === $code) {
                return $constant['message'];
            }
        }

        return '未知错误';
    }

    public static function getLabel(int|string $code): string
    {
        foreach (static::getConstants() as $constant) {
            if ($constant['code'] === $code) {
                return $constant['label'] ?? '';
            }
        }

        return '';
    }

    private static function getConstants(): array
    {
        $ref = new \ReflectionClass(static::class);

        return $ref->getConstants();
    }
}
