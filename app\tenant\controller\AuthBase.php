<?php
declare(strict_types = 1);

namespace app\tenant\controller;

use app\common\controller\CommonController;
use app\tenant\context\TenantContext;
use app\tenant\exception\AccountException;
use app\tenant\repository\TenantRepository;
use app\tenant\service\LogService;
use app\tenant\service\TokenService;
use think\App;

/**
 * 租户认证控制器基类
 * <AUTHOR> <<EMAIL>>
 * @package app\tenant\controller
 */
class AuthBase extends CommonController
{
    /**
     * 无需登录的方法列表
     */
    protected array $noNeedLogin = [];

    /**
     * @var LogService
     */
    protected LogService $logService;

    /**
     * 构造方法
     * @param \think\App $app
     * @throws \app\tenant\exception\AccountException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function __construct(App $app)
    {
        parent::__construct($app);

        $this->logService = new LogService();

        $this->initialize();
    }

    /**
     * 初始化方法
     * @throws \app\tenant\exception\AccountException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return void
     */
    public function initialize(): void
    {
        parent::initialize();

        // 如果当前方法需要登录验证
        if (!in_array($this->request->action(), $this->noNeedLogin)) {
            $this->checkAuth();
        }
    }

    /**
     * 检查认证token权限
     * @throws \app\tenant\exception\AccountException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return void
     */
    protected function checkAuth(): void
    {
        $tokenService = TokenService::getInstance();

        // 获取accessToken
        $accessToken = $tokenService->getRequestToken();

        if (empty($accessToken)) {
            throw new AccountException('请先登录', AccountException::AUTH_FAILED);
        }

        try {
            // 验证accessToken并获取缓存用户信息
            $userInfo = $tokenService->verifyToken($accessToken);

            // 将租户用户信息存入Context，方便其它地方获取
            TenantContext::getInstance()->setUserInfo($userInfo);
        } catch (\Exception $e) {
            // 前端收到对应label后使用refresh_token获取新的access_token
            $exception = new AccountException('登录已过期,请使用refresh_token刷新', AccountException::INVALID_TOKEN);
            $exception->setExtra(['label' => 'refreshToken']);

            throw $exception;
        }

        // 可以在这里添加其他验证，比如租户状态检查等
        $this->checkTenantStatus();
    }

    /**
     * 检查租户状态
     * @throws \app\tenant\exception\AccountException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return void
     */
    protected function checkTenantStatus(): void
    {
        // 从上文中获取租户编码
        $tenantCode = $this->getTenantCode();

        if (empty($tenantCode)) {
            throw new AccountException('租户信息异常', AccountException::AUTH_FAILED);
        }

        // 检查租户状态
        $checkTenant = (new TenantRepository())->checkTenantStatus($tenantCode);

        if ($checkTenant['status'] === 0) {
            throw new AccountException($checkTenant['message'], AccountException::AUTH_FAILED);
        }
    }

    /**
     * 获取当前租户用户信息
     * @return array
     */
    protected function getUserInfo(): array
    {
        return TenantContext::getInstance()->getUserInfo();
    }

    /**
     * 获取租户用户是否是创始人
     * @return int
     */
    protected function isFounder(): int
    {
        return TenantContext::getInstance()->isFounder();
    }

    /**
     * 获取当前租户ID
     * @return int
     */
    protected function getTenantId(): int
    {
        return TenantContext::getInstance()->getTenantId();
    }

    /**
     * 获取当前租户编码
     * @return string|null
     */
    protected function getTenantCode(): ?string
    {
        return TenantContext::getInstance()->getTenantCode();
    }

    /**
     * 获取当前用户ID
     * @return int
     */
    protected function getUserId(): int
    {
        return TenantContext::getInstance()->getUserId();
    }
}
