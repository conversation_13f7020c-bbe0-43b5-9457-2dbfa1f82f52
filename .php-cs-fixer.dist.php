<?php
use PhpCsFixer\Config;
use <PERSON>p<PERSON><PERSON>ixer\Finder;

/**
 * https://github.com/PHP-CS-Fixer/PHP-CS-Fixer/blob/v3.0.0/UPGRADE-v3.md#renamed-ruless
 * https://cs.symfony.com/doc/rules/whitespace/no_spaces_around_offset.html
 * https://github.com/search?q=.php-cs-fixer.dist.php+lowercase_constants&type=code
 */
$header = <<<'HEADER'
    Copyright (c) 2023 WisCore <<EMAIL>>.
    For the full copyright and license information, please view
    the LICENSE file that was distributed with this source code.
    HEADER;

$rules = [
    '@PHP80Migration' => true,
    //'@PSR2'    => true, // 遵循PSR2
    '@PSR12'              => true, // 遵循PSR12
    'encoding'            => true, // utf8 无bom头编码
    'visibility_required' => [ // 可见性必须在所有属性和方法上声明
        'elements' => ['const', 'method', 'property'],
    ],
    'list_syntax'                                => ['syntax' => 'long'],
    'single_quote'                               => true, // 简单字符串应该使用单引号代替双引号；
    'no_unused_imports'                          => true, // 删除没用到的use引入
    'no_useless_return'                          => true, // 删除函数末尾无用的return
    'no_singleline_whitespace_before_semicolons' => true, // 禁止只有单行空格和分号的写法；
    'self_accessor'                              => true, // 在当前类中使用 self 代替类名；
    'no_empty_statement'                         => true, // 删除多余的分号
    'no_extra_blank_lines'                       => [ // 删除多余空白行
        'tokens' => [
            'break',
            'continue',
            'extra',
            'return',
            'throw',
            'use',
            'parenthesis_brace_block',
            'square_brace_block',
            'curly_brace_block',
        ],
    ],
    'no_blank_lines_after_class_opening' => true, // 类开始标签后不应该有空白行；
    'include'                            => true, // include 和文件路径之间需要有一个空格，文件路径不需要用括号括起来；
    'no_trailing_comma_in_singleline'    => true, // 删除 list 语句中多余的逗号；
    'no_leading_namespace_whitespace'    => true, // 删除namespace声明行包含前导空格
    'standardize_not_equals'             => false, // 使用 <> 代替 !=；
    'declare_equal_normalize'            => [ // 声明语句中的等号应该被空格包围，或者不遵循配置
        'space' => 'single',
    ],
    'align_multiline_comment' => [ // 每行多行 DocComments 必须有一个星号（PSR-5），并且必须与第一行对齐
        'comment_type' => 'all_multiline', // phpdocs_only（默认）、phpdocs_like、all_multiline
    ],
    'array_indentation' => true, // 数组的每个元素必须缩进一次
    'array_syntax'      => [ // 应使用配置的语法声明 PHP 数组
        'syntax' => 'short', // long (默认，用array关键字来定义数组)/short (用[]关键字来定义数组)
    ],
    'backtick_to_shell_exec' => false,
    'binary_operator_spaces' => [ // 等号对齐、数字箭头符号对齐
        'default' => 'align_single_space_minimal', // align_single_space|single_space|align_single_space_minimal
    ],
    'blank_line_after_namespace'   => true, // 命名空间后空一行
    'blank_line_after_opening_tag' => false, // <\?php后面加一个空行
    'blank_line_before_statement'  => [ // 空行换行必须在任何已配置的语句之前
        'statements' => [
            'if',
            'do',
            'for',
            'foreach',
            'switch',
            'while',
            'declare',
            'return',
            'throw',
            'try',
        ],
    ],
    'concat_space' => [ // 点连接符是否需要空格
        'spacing' => 'one',
    ],
    'function_declaration' => [ // function后面是否需要空格
        'closure_function_spacing' => 'none', // none或one，默认为one
    ],
    'type_declaration_spaces'                     => true, // 在闭包函数的function后面是否需要空格，默认为true
    'linebreak_after_opening_tag'                 => true, // 在<\?php 标签所在的行不允许存在代码
    'lowercase_cast'                              => true, // CASE 关键字转换必须小写
    'constant_case'                               => ['case' => 'lower'], // php 常量必须为小写
    'lowercase_keywords'                          => true, // PHP 关键字必须小写
    'lowercase_static_reference'                  => true, // 静态调用必须小写,例如：self, static, parent
    'new_with_parentheses'                        => true, // 使用 new 关键字创建的所有实例必须后跟括号
    'no_blank_lines_after_phpdoc'                 => true, // phpdoc 后面不应该有空行
    'blank_lines_before_namespace'                => false, // 命名空间增加空行
    'no_empty_comment'                            => true, // 不应该存在空注释
    'no_empty_phpdoc'                             => true, // 删除空注释
    'no_leading_import_slash'                     => true, // 在 use 语句中，取消前置斜杠
    'no_multiline_whitespace_around_double_arrow' => true, // 运算符 => 不应被多行空格包围。
    'no_null_property_initialization'             => true, // 属性不能用显式初始化 null
    'no_spaces_after_function_name'               => true, // 在函数或者方法定义的时候，不允许函数和左括号之间有空格
    'spaces_inside_parentheses'                   => true, // 删除括号后内两端的空格
    'no_trailing_whitespace'                      => true, // 删除非空行末尾的尾随空格
    'no_trailing_whitespace_in_comment'           => true, // 注释或 PHPDoc 中必须没有尾随空格
    'no_useless_else'                             => true, // 不需要没有用的 else 分支
    'no_whitespace_before_comma_in_array'         => true, // 删除数组声明中，每个逗号前的空格
    'no_whitespace_in_blank_line'                 => true, // 删除空白行末尾的空白
    'phpdoc_align'                                => [     // 注释对齐方法
        'align' => 'left', // 对齐方式。可以为：vertical（竖向对齐）、left（左侧对齐）
    ],
    'phpdoc_scalar'                                 => true, // phpdoc 标量类型声明时应该使用 int 而不是 integer，bool 而不是 boolean，float 而不是 real 或者 double；
    'phpdoc_trim'                                   => true, // 除了文档块最开始的部分和最后的部分，phpdoc 开始和结束都应该是有内容的
    'phpdoc_summary'                                => false, // PHPDoc 摘要应以句号，感叹号或问号结尾
    'phpdoc_indent'                                 => true, // phpdoc 应该保持缩进
    'phpdoc_order'                                  => true, // 应该对 PHPDoc 中的@param 注释进行排序，以便首先@throws 注释，然后是@return 注释，然后是注释
    'phpdoc_types_order'                            => false, // 对PHPDoc类型进行排序
    'phpdoc_separation'                             => false, // phpdoc 中注释相同的属性应该放在一起，不同的属性之间应该有一个空白行分割
    'phpdoc_add_missing_param_annotation'           => true, // PHPDoc应该包含所有参数的@param
    'phpdoc_trim_consecutive_blank_line_separation' => true, // 在摘要之后和 PHPDoc 中的描述之后删除额外的空白行
    'phpdoc_var_annotation_correct_order'           => true, // @var 和@type 注释必须具有正确顺序的类型和名称
    'single_blank_line_at_eof'                      => true, // 没有结束标记的 PHP 文件必须始终以单个空行换头结束
    'single_line_after_imports'                     => true, // 每个命名空间使用必须在它自己的行上，并且在 use 语句块之后必须有一个空行
    'space_after_semicolon'                         => [ // 分号后修复空格
        'remove_in_empty_for_expressions' => true, // 是否应删除空 for 表达式的空格
    ],
    'switch_case_semicolon_to_colon' => true, // case 之后应该是冒号而不是分号
    'switch_case_space'              => true, // 删除 case 冒号和大小写值之间的额外空格
    'ternary_operator_spaces'        => true, // 三元操作符周围有标准空格
    'trailing_comma_in_multiline'    => [ // PHP 多行数组应该有一个尾随逗号
        'elements' => ['arrays'],
    ],
    'trim_array_spaces'         => true, // 删除数组首或尾随单行空格
    'single_line_comment_style' => [ // 单行注释用// 而非#
        'comment_types' => ['hash'],
    ],
    'combine_consecutive_unsets' => true, // 当多个 unset 使用的时候，合并处理
    'full_opening_tag'           => true, // PHP代码必须使用长的<?php标记或short-echo<?=标记，而不是其他标记变体
    'heredoc_to_nowdoc'          => true, // heredoc转nowdoc
    'indentation_type'           => true, // 代码必须使用配置的缩进类型
    'line_ending'                => true, // 所有PHP文件必须使用相同的行尾
    'method_argument_space'      => true, // 函数参数空格
    'normalize_index_brace'      => true, // 方法声明及调用时，参数之间的逗号前不能有空格，逗号后必须有一个空格
    'no_spaces_around_offset'    => [ // 偏移大括号周围不能有空格
        'positions' => ['inside', 'outside'],
    ],
    //'ordered_imports'             => false, // use排序，不使用，按编辑器的默认排序
    'ordered_imports' => [
        'sort_algorithm' => 'alpha',
    ],
    'single_import_per_statement'           => true, // 每个声明必须有一个use关键字
    'short_scalar_cast'                     => true, // 短名称 Cast(boolean)和(integer)应该写成(bool)和(int)，(double)和(real)写成(float)，(binary)写成(string)。
    'unary_operator_spaces'                 => true, // 一元运算符应放在其操作数附近
    'no_unreachable_default_argument_value' => false, // 有风险。在函数参数中，不能有默认值在非缺省值之前的参数。
    'ordered_class_elements'                => false, // 类元素方法属性排序
    'class_attributes_separation'           => [
        'elements' => [
            'const'    => 'one',
            'method'   => 'one',
            'property' => 'one',
        ],
    ],
];

$finder = Finder::create()
    ->in(__DIR__ . '/app')
    ->in(__DIR__ . '/config')
    ->in(__DIR__ . '/extend')
    ->in(__DIR__ . '/public')
    //->in(__DIR__ . '/route')
    ->append([__FILE__]) // 当前文件
    ->exclude(__DIR__ . '/vendor') // 排除目录
    ->exclude(__DIR__ . '/runtime')
    ->name('*.php')
    ->notName('*.blade.php')
    ->ignoreDotFiles(true)
    ->ignoreVCS(true);

return (new Config())
    ->setFinder($finder)
    ->setRules($rules)
    ->setRiskyAllowed(true)
    ->setUsingCache(true);
