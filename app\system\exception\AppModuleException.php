<?php
declare(strict_types = 1);

namespace app\system\exception;

use app\common\exception\CommonException;

/**
 * 应用模块异常类
 * <AUTHOR> <<EMAIL>>
 * @package app\system\exception
 */
class AppModuleException extends CommonException
{
    public function __construct($message = '', $code = 0, $data = [])
    {
        parent::__construct($message, $code);
        $this->data = $data;
    }

    public function getData()
    {
        return $this->data;
    }
}
