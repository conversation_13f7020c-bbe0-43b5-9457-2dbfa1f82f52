<?php
namespace app\common\traits;

use think\db\Query;
use think\Model;

/**
 * 模型类通用方法复用类
 * <AUTHOR> <<EMAIL>>
 * @package app\common\traits
 */
trait ModelTrait
{
    /**
     * 全局查询条件
     */
    protected static array $conditions = [
        'where'        => [],  // where条件
        'order'        => [],  // 排序条件
        'field'        => [],  // 字段选择
        'withoutField' => [],  // 排除字段
        'limit'        => [],  // limit限制
        'with'         => [],  // 关联模型
    ];

    /**
     * 设置全局查询条件
     * @param array $conditions 查询条件数组
     * @param string $type 条件类型: where, order, field, withoutField, limit
     * @return void
     */
    public static function setCondition(array $conditions, string $type = 'where'): void
    {
        if (!isset(self::$conditions[$type])) {
            self::$conditions[$type] = [];
        }

        foreach ($conditions as $key => $value) {
            if (is_numeric($key)) {
                self::$conditions[$type][] = $value;
            } else {
                self::$conditions[$type][$key] = $value;
            }
        }
    }

    /**
     * 应用全局查询条件
     * @return \think\db\Query
     */
    protected static function applyConditions(): Query
    {
        $query = (new static())->db();

        if (empty(self::$conditions)) {
            return $query;
        }

        // 处理where条件
        if (!empty(self::$conditions['where'])) {
            foreach (self::$conditions['where'] as $key => $value) {
                if (is_numeric($key)) {
                    if (is_array($value)) {
                        if (count($value) === 3) {
                            // 处理['field', 'op', 'value']格式
                            $query->where(...$value);
                        } else {
                            // 处理普通数组条件
                            $query->where($value);
                        }
                    } elseif ($value instanceof \Closure) {
                        // 处理闭包条件
                        $query->where($value);
                    }
                } else {
                    // 处理键值对条件
                    $query->where($key, $value);
                }
            }
        }

        // 处理字段选择
        if (!empty(self::$conditions['field'])) {
            $query->field(self::$conditions['field']);
        }

        // 处理排除字段
        if (!empty(self::$conditions['withoutField'])) {
            $query->withoutField(self::$conditions['withoutField']);
        }

        // 处理排序条件
        if (!empty(self::$conditions['order'])) {
            foreach (self::$conditions['order'] as $field => $type) {
                $query->order($field, $type);
            }
        }

        // 处理limit限制
        if (!empty(self::$conditions['limit'])) {
            $query->limit(...self::$conditions['limit']);
        }

        // 处理关联模型
        if (!empty(self::$conditions['with'])) {
            foreach (self::$conditions['with'] as $relation => $callback) {
                if (is_numeric($relation)) {
                    $query->with($callback);
                } else {
                    $query->with([$relation => $callback]);
                }
            }
        }

        return $query;
    }

    /**
     * 清空查询条件
     * @param string|null $type 条件类型，为null时清空所有
     * @return void
     */
    public static function clearConditions(?string $type = null): void
    {
        if ($type === null) {
            self::$conditions = [
                'where'        => [],  // where条件
                'order'        => [],  // 排序条件
                'field'        => [],  // 字段选择
                'withoutField' => [],  // 排除字段
                'limit'        => [],  // limit限制
                'with'         => [],   // 关联模型
            ];
        } else {
            self::$conditions[$type] = [];
        }
    }

    /**
     * 查询数据记录
     * 调用模型find方法查询数据
     * @param mixed $condition 查询条件
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return ?array 成功返回数据集，数据不存在返回null
     */
    public static function getInfo(mixed $condition): ?array
    {
        // 获取带全局条件的查询构造器
        $query = self::applyConditions();

        if (is_array($condition)) {
            // 数组条件追加到查询构造器
            $query->where($condition);
        } else {
            // 主键查询
            $query->where('id', '=', $condition);
        }

        $data = $query->find();

        return $data ? $data->toArray() : null;
    }

    /**
     * 标准化分页数据列表（系统模块通用实现）
     * 支持关键字搜索和状态过滤，返回统一的分页数据结构
     * @param int $pageNo 当前页码（从1开始）
     * @param int $pageSize 每页数据量
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return array 返回统一分页格式数据，包含：
     *               - total: 总记录数
     *               - list: 当前页数据集合
     *               - page: 当前页码
     *               - pageSize: 每页数量
     */
    public static function pageList(int $pageNo = 1, int $pageSize = 10): array
    {
        // 获取带全局条件的查询构造器
        $query = self::applyConditions();

        $total = $query->count();

        $resData = ['total' => 0, 'list' => [], 'page' => $pageNo, 'pageSize' => $pageSize];

        if ($total == 0) {
            return $resData;
        }

        $list = $query->page($pageNo, $pageSize)->select();

        $resData['total'] = $total;
        $resData['list']  = $list;

        return $resData;
    }

    /**
     * 检查字段值是否已存在
     * 用于唯一性验证（如用户名、手机号等字段查重）
     * @param string $field 要检查的字段名
     * @param mixed $value 要检查的字段值
     * @param int $excludeId 需要排除的主键ID（常用于更新操作时排除自身）
     * @return \think\Model|null 存在返回数据模型对象，不存在返回null
     */
    public static function checkExist(string $field, mixed $value, int $excludeId = 0): ?Model
    {
        try {
            // 获取带全局条件的查询构造器
            $query = self::applyConditions();

            $query->where($field, '=', $value);

            if ($excludeId > 0) {
                $query->where('id', '<>', $excludeId);
            }

            return $query->find();
        } catch (\Exception $exception) {
            return null;
        }
    }

    /**
     * 获取有效记录的下拉列表
     * @param string $value
     * @param string $label
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return array
     */
    public static function getSelects(string $value = 'id', string $label = 'name'): array
    {
        return self::where('status', '=', 1)
            ->field([$value, $label])
            ->select()
            ->toArray();
    }
}
