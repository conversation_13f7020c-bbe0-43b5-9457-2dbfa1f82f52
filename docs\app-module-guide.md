# 应用模块开发指南

## 目录
1. [功能概述](#功能概述)
2. [应用结构](#应用结构)
3. [开发规范](#开发规范)
4. [应用生命周期](#应用生命周期)
5. [接口说明](#接口说明)
6. [使用示例](#使用示例)

## 功能概述
应用模块系统提供了一种可插拔的应用扩展机制，支持应用的安装、卸载、启用、禁用和升级等完整生命周期管理。

## 应用结构
```
appName/                     # 应用目录
├── config/                 # 配置目录
│   ├── config.php         # 应用配置文件
│   ├── menu.php          # 菜单配置文件
│   └── info.php          # 应用信息配置文件
├── controller/            # 控制器目录
├── model/                # 模型目录
├── service/              # 服务类目录
├── database/             # 数据库相关
│   ├── install.sql      # 安装SQL
│   ├── uninstall.sql    # 卸载SQL
│   └── upgrade/         # 升级SQL目录
└── {appName}App.php      # 应用主类
```

## 开发规范

### 1. 应用信息配置 (info.php)
```php
return [
    'name'        => '应用名称',
    'code'        => '应用标识',
    'version'     => '1.0.0',
    'description' => '应用描述',
    'author'      => '作者',
    'website'     => '网站',
    'require'     => [
        'php' => '>=8.1.0',
        'framework' => '>=1.0.0',
    ],
];
```

### 2. 应用主类规范
```php
namespace app\appName;

use helio\App;

class AppNameApp extends App
{
    public function install()
    {
        // 安装逻辑
    }

    public function uninstall()
    {
        // 卸载逻辑
    }

    public function enable()
    {
        // 启用逻辑
    }

    public function disable()
    {
        // 禁用逻辑
    }

    public function upgrade($version)
    {
        // 升级逻辑
    }
}
```

### 3. 数据库规范
```sql
-- 创建数据表建议以应用标识为前缀
CREATE TABLE `app_demo_users` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `username` varchar(32) NOT NULL COMMENT '用户名',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='示例用户表';
```

### 4. 版本号规范
- 遵循语义化版本 2.0.0 (SemVer)规范
- 格式：主版本号.次版本号.修订号
- 示例：1.0.0、1.1.0、1.1.1

### 5. 模型规范

## 应用生命周期

1. **安装**
   - 验证应用信息
   - 执行数据库安装脚本
   - 注册应用菜单
   - 初始化数据

2. **启用/禁用**
   - 更新应用状态
   - 更新菜单状态
   - 执行相应的启用/禁用回调

3. **升级**
   - 验证版本号格式
   - 检查版本兼容性
   - 执行升级脚本
   - 更新版本信息

4. **卸载**
   - 检查应用依赖
   - 删除应用数据
   - 删除应用菜单
   - 执行卸载回调

## 接口说明

### AppModuleService 核心方法

```php
// 检查应用状态
checkAppModuleStatus(string $appCode): array

// 安装应用
install(string $appCode): bool

// 卸载应用
uninstall(string $appCode): bool

// 启用应用
enable(string $appCode): bool

// 禁用应用
disable(string $appCode): bool

// 升级应用
upgrade(string $appCode, string $version): bool

// 批量更新应用状态
batchUpdateStatus(array $appCodes, int $status): bool
```

## 使用示例

### 1. 开发新应用

```php
// 1. 创建应用目录结构
// 2. 配置应用信息
// 3. 实现应用主类
// 4. 开发业务逻辑
```

### 2. 调用应用管理接口

```php
$service = new AppModuleService($app);

// 安装应用
$service->install('demoApp');

// 启用应用
$service->enable('demoApp');

// 升级应用
$service->upgrade('demoApp', '1.1.0');
```

## 错误码说明

### 应用基础错误码 (10000-10999)
| 错误码 | 说明 |
|--------|------|
| 10001 | 应用不存在 |
| 10002 | 应用安装失败 |
| 10003 | 应用卸载失败 |
| 10004 | 应用启用失败 |
| 10005 | 应用禁用失败 |
| 10006 | 应用升级失败 |
| 10007 | 应用存在依赖无法卸载 |
| 10008 | 批量更新应用状态失败 |
| 10009 | 无效的状态值 |
| 10010 | 应用已安装 |
| 10011 | 应用信息无效 |

### 应用状态错误码 (11000-11999)
| 错误码 | 说明 |
|--------|------|
| 11001 | 应用未安装 |
| 11002 | 应用已禁用 |
| 11003 | 应用版本过低 |
| 11004 | 无效的版本号格式 |

### 应用配置错误码 (12000-12999)
| 错误码 | 说明 |
|--------|------|
| 12001 | 配置文件不存在 |
| 12002 | 配置保存失败 |
| 12003 | 配置格式无效 |

### 菜单相关错误码 (13000-13999)
| 错误码 | 说明 |
|--------|------|
| 13001 | 菜单配置文件不存在 |
| 13002 | 无效的菜单配置 |
| 13003 | 菜单配置缺少必要字段 |
| 13004 | 菜单创建失败 |

### 依赖相关错误码 (14000-14999)
| 错误码 | 说明 |
|--------|------|
| 14001 | 依赖检查失败 |
| 14002 | 依赖版本不匹配 |

### 缓存相关错误码 (15000-15999)
| 错误码 | 说明 |
|--------|------|
| 15001 | 缓存设置失败 |
| 15002 | 缓存获取失败 |

### 生命周期钩子说明

1. **install()**
   - 执行时机：应用安装时
   - 建议操作：
     * 创建数据表
     * 初始化基础数据
     * 注册系统钩子
     * 创建资源目录
   - 返回值：bool，安装成功返回true
   - 异常处理：抛出AppInstallException

2. **uninstall()**
   - 执行时机：应用卸载时
   - 建议操作：
     * 备份重要数据
     * 清理数据表
     * 删除上传文件
     * 注销系统钩子
   - 返回值：bool，卸载成功返回true
   - 异常处理：抛出AppUninstallException

3. **enable()**
   - 执行时机：应用启用时
   - 建议操作：启动定时任务、初始化缓存
   - 注意事项：确保依赖服务可用

4. **disable()**
   - 执行时机：应用禁用时
   - 建议操作：停止定时任务、清理缓存
   - 注意事项：保存必要的状态数据

5. **upgrade($version)**
   - 执行时机：应用版本升级时
   - 建议操作：数据结构变更、数据迁移
   - 注意事项：需要考虑向下兼容

## 安全规范

1. **数据验证**
   - 所有用户输入必须验证
   - 使用验证器进行数据验证
   - 防止 SQL 注入和 XSS 攻击

2. **权限控制**
   - 实现细粒度的权限控制
   - 使用 RBAC 权限体系
   - 避免越权访问

3. **敏感信息**
   - 密码等敏感信息加密存储
   - 配置文件注意访问权限
   - 日志中避免记录敏感信息

## 最佳实践

### 1. 目录结构优化
```
appName/
├── config/
│   └── route.php          # 路由配置
├── database/
│   ├── migrations/        # 数据库迁移文件
│   └── seeds/            # 数据填充文件
├── validate/             # 验证器目录
└── event/               # 事件目录
```

### 2. 错误处理
```php
try {
    // 业务逻辑
} catch (AppModuleException $e) {
    // 应用级异常处理
} catch (\Exception $e) {
    // 系统级异常处理
}
```

### 3. 缓存使用
```php
// 建议使用统一的缓存前缀
$cacheKey = 'app_demo:user:' . $userId;
Cache::get($cacheKey);
```

### 4. 依赖声明
```php
// 在 info.php 中声明依赖
return [
    // ...existing code...
    'require' => [
        'system' => '>=1.0.0',     // 系统版本要求
        'php' => '>=7.4.0',        // PHP 版本要求
        'extensions' => [          // 扩展依赖
            'pdo',
            'redis'
        ],
        'apps' => [               // 应用依赖
            'user' => '>=1.0.0'
        ]
    ]
];
```

### 1. 异常处理规范

```php
use app\system\exception\AppModuleException;

class DemoApp extends App
{
    public function install()
    {
        try {
            // 验证环境
            $this->checkEnvironment();
            
            // 创建数据表
            $this->createTables();
            
            // 初始化数据
            $this->initData();
            
            return true;
        } catch (\Exception $e) {
            throw new AppModuleException('安装失败：' . $e->getMessage());
        }
    }
}
```

### 2. 数据迁移规范
```php
// database/migrations/20240101_create_demo_table.php
return [
    'up' => function ($schema) {
        $schema->create('demo_table', function ($table) {
            $table->id();
            $table->string('name')->comment('名称');
            $table->timestamps();
            $table->softDeletes();
        });
    },
    'down' => function ($schema) {
        $schema->dropIfExists('demo_table');
    }
];
```

### 3. 配置文件规范
```php
// config/config.php
return [
    // 应用级配置
    'app' => [
        'cache_prefix' => 'demo_app:',
        'upload_path' => 'uploads/demo/',
        'api_version' => 'v1',
    ],
    
    // 功能模块配置
    'features' => [
        'enable_log' => true,
        'enable_cache' => true,
    ],
    
    // 依赖服务配置
    'services' => [
        'redis' => [
            'host' => '127.0.0.1',
            'port' => 6379,
        ]
    ]
];
```

### 4. 性能优化建议
- 使用数据库索引
- 合理使用缓存
- 异步处理耗时操作
- 避免N+1查询问题
- 定期清理临时数据
- 使用队列处理大批量任务

### 5. 安全检查清单
- [ ] 输入数据严格验证
- [ ] SQL防注入处理
- [ ] XSS防护
- [ ] CSRF防护
- [ ] 文件上传限制
- [ ] 敏感信息加密
- [ ] 访问权限控制
- [ ] 日志脱敏处理

## 调试与测试

### 1. 开发环境配置
```php
// config/app.php
return [
    'app_debug' => true,
    'app_trace' => true
];
```

### 2. 单元测试
```php
// tests/AppTest.php
class AppTest extends TestCase
{
    public function testInstall()
    {
        // 测试应用安装
    }
}
```

### 3. 日志记录
```php
Log::info('App operation', [
    'action' => 'install',
    'app_code' => 'demo',
    'version' => '1.0.0'
]);
```

## 常见问题

1. **安装失败**
   - 检查依赖是否满足
   - 检查数据表是否存在
   - 查看错误日志

2. **升级异常**
   - 备份重要数据
   - 确认版本兼容性
   - 按顺序执行升级脚本

3. **性能优化**
   - 合理使用缓存
   - 优化数据库查询
   - 避免循环依赖
