<?php
declare(strict_types = 1);

namespace app\tenant\service;

use app\tenant\model\MqttClientAuth as MqttClientAuthModel;
use app\tenant\repository\TenantRepository;
use think\facade\Config;
use think\facade\Log;

/**
 * MQTT认证服务类
 * 提供EMQX HTTP认证插件所需的认证逻辑
 * 负责验证客户端身份和权限
 * <AUTHOR> <<EMAIL>>
 * @package app\tenant\service
 */
class MqttAuthService extends BaseService
{
    /**
     * 单例实例
     */
    private static ?MqttAuthService $instance = null;

    /**
     * 私有构造函数，防止外部直接实例化
     */
    private function __construct()
    {
        // 私有构造函数，防止外部直接实例化
    }

    /**
     * 私有克隆方法，防止对象被克隆
     */
    private function __clone()
    {
        // 私有克隆方法，防止对象被克隆
    }

    /**
     * 获取单例实例
     * @return MqttAuthService
     */
    public static function getInstance(): self
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }

        return self::$instance;
    }

    /**
     * 认证客户端连接
     * @param array $params 认证参数
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return array
     */
    public function authenticate(array $params): array
    {
        // 客户端ID
        $clientId = $params['clientid'] ?? '';
        // 客户端用户名，格式为{商户编码@商户用户id}
        $username = $params['username'] ?? '';
        $password = $params['password'] ?? '';

        // 记录认证请求日志
        Log::debug('MQTT authenticate params: ' . json_encode([
            'clientid' => $clientId,
            'username' => $username,
        ], JSON_UNESCAPED_UNICODE));

        // 首先检查数据库中是否有静态配置的客户端认证信息
        $authInfo = MqttClientAuthModel::findByUsername($username);

        // 根据数据库中预设的鉴权信息进行认证
        if ($authInfo && $authInfo['status']) {
            // 验证密码
            if ($authInfo['password'] === $password) {
                return $this->allow(
                    ['is_superuser' => (bool)$authInfo['is_superuser']],
                    ['username' => $username, 'type' => 'super_user']
                );
            }

            return $this->deny('Invalid credentials', ['username' => $username]);
        }

        // 验证是否是租户客户端连接
        // 验证username格式：{tenant_code@user_name}
        if (!preg_match('/^[a-zA-Z0-9]+@[a-zA-Z0-9]+$/', $username)) {
            return $this->deny('Invalid username format', ['username' => $username]);
        }

        // 从username中提取tenant_code和user_id
        list($tenantCode, $userId) = explode('@', $username);

        if (empty($tenantCode) || empty($userId)) {
            return $this->deny('Invalid tenant code or username', ['username' => $username]);
        }

        // 验证租户状态
        $tenantRepository = new TenantRepository();
        $tenant           = $tenantRepository->findByCode($tenantCode);

        if (!$tenant) {
            return $this->deny('Tenant not found', ['tenant_code' => $tenantCode]);
        }

        $tenant = $tenant->toArray();

        if ($tenant['status'] !== 1) {
            return $this->deny('Tenant is not active', ['tenant_code' => $tenantCode]);
        }

        // 验证用户身份
        try {
            $tokenService = TokenService::getInstance();
            $tokenInfo    = $tokenService->verifyToken($password);

            // 转换成int
            $userId = (int)$userId;

            // 验证token中的用户信息是否匹配
            if ($tokenInfo['tenant_code'] !== $tenantCode || (int)$tokenInfo['id'] !== $userId) {
                return $this->deny('Invalid token', ['username' => $username]);
            }

            // 获取token的过期时间
            $tokenExpireTime = $tokenService->getTokenExpireTime((int)$tenant['id'], $userId);

            // 构建预设权限列表 (ACL)
            // 从EMQX 5.8.0开始，可以在认证时返回ACL规则，避免后续对每个主题都进行单独的授权查询
            $aclRules = $this->buildAclRules($tenant, $userId);

            // 从EMQX 5.7.0开始，支持设置客户端属性，用于规则引擎或增强授权
            $clientAttrs = $this->buildClientAttrs($tenant, $userId, $tokenInfo);

            // 验证token成功返回允许登录，并将过期时间设置为token过期后10秒
            return $this->allow(
                [
                    'is_superuser' => false, // 是否超级用户
                    'expire_at'    => time() + $tokenExpireTime + 10,
                    'acl'          => $aclRules, // 权限预设，返回针对当前用户默认允许的ACL规则给EMQX（如需要）
                    //'client_attrs' => $clientAttrs, // 返回当前商户用户信息给EMQX（如需要）
                ],
                [
                    'username'     => $username,
                    'tenant_code'  => $tenantCode,
                    'user_id'      => $userId,
                    'client_attrs' => $clientAttrs,
                ]
            );
        } catch (\Exception $e) {
            return $this->deny('Invalid token', [
                'username' => $username,
                'error'    => $e->getMessage(),
            ]);
        }
    }

    /**
     * 构建客户端ACL规则
     * @param array $tenant 租户信息
     * @param int $userId 用户ID
     * @return array ACL规则列表
     * @文档 https://docs.emqx.com/zh/emqx/latest/access-control/authn/acl.html
     */
    private function buildAclRules(array $tenant, int $userId): array
    {
        // 获取MQTT配置
        $mqttConfig = Config::get('mqtt');
        // 主题前缀
        $topicPrefix = $mqttConfig['topic_prefix'] ?? '';

        $tenantTopicPrefix = $topicPrefix . 'tenant/';
        $userTopicPrefix   = $tenantTopicPrefix . $tenant['code'] . '/' . $userId . '/';

        // 基本ACL规则
        $rules = [
            // 允许发布当前端的状态主题
            [
                'permission' => 'allow',
                'action'     => 'publish',
                'topic'      => $userTopicPrefix . 'status',
            ],
            // 允许发布、订阅用户自己的通知主题
            [
                'permission' => 'allow',
                'action'     => 'all',
                'topic'      => $userTopicPrefix . 'notification',
            ],
            // 允许订阅用户自己的所有主题
            [
                'permission' => 'allow',
                'action'     => 'subscribe',
                'topic'      => $userTopicPrefix . '#',
            ],
            // 允许订阅租户级别的通知主题（广播消息）
            [
                'permission' => 'allow',
                'action'     => 'subscribe',
                'topic'      => $tenantTopicPrefix . $tenant['code'] . '/notification',
            ],
            // 允许订阅租户公共主题
            /*[
                'permission' => 'allow',
                'action'     => 'subscribe',
                'topic'      => $tenantTopicPrefix . $tenant['code'] . '/public/#',
            ],*/
        ];

        // 如果用户有权限发布租户级别的通知，添加相应的规则
        if ($this->canPublishTenantNotification($tenant, $userId)) {
            $rules[] = [
                'permission' => 'allow',
                'action'     => 'publish',
                'topic'      => $tenantTopicPrefix . $tenant['code'] . '/notification',
            ];
        }

        // 添加其他规则
        $rules = array_merge($rules, [
            // 允许订阅设备心跳主题（用于接收其它设备心跳）
            [
                'permission' => 'allow',
                'action'     => 'subscribe',
                'topic'      => $tenantTopicPrefix . $tenant['code'] . '/device/+/heartbeat',
            ],
            // 允许云端设备发布设备探测主题
            [
                'permission' => 'allow',
                'action'     => 'publish',
                'topic'      => $tenantTopicPrefix . $tenant['code'] . '/device/+/trace',
            ],
            // 允许发布到设备控制主题
            [
                'permission' => 'allow',
                'action'     => 'publish',
                'topic'      => $tenantTopicPrefix . $tenant['code'] . '/device/+/control',
            ],
            // 拒绝访问其他用户的主题
            [
                'permission' => 'deny',
                'action'     => 'all',
                'topic'      => $tenantTopicPrefix . $tenant['code'] . '/+/#',
            ],
            // 拒绝访问其他租户的主题
            [
                'permission' => 'deny',
                'action'     => 'all',
                'topic'      => $topicPrefix . 'tenant/#',
            ],
        ]);

        return $rules;
    }

    /**
     * 构建客户端属性
     * @param array $tenant 租户信息
     * @param int $userId 用户ID
     * @param array $tokenInfo Token信息
     * @return array 客户端属性
     */
    private function buildClientAttrs(array $tenant, int $userId, array $tokenInfo): array
    {
        return [
            'tenant_id'   => (string)$tenant['id'],
            'tenant_code' => $tenant['code'],
            'user_id'     => (string)$userId,
            'role'        => isset($tokenInfo['role']) ? (string)$tokenInfo['role'] : 'user',
            'login_at'    => (string)time(),
        ];
    }

    /**
     * 记录日志并返回允许结果
     * @param array $data 返回数据
     * @param array $logData 日志数据
     * @return array 允许结果
     */
    private function allow(array $data = [], array $logData = []): array
    {
        // 构建日志消息
        $logMessage = 'MQTT authenticate allowed: ' . $this->formatLogData($logData);
        Log::debug($logMessage);

        return array_merge(['result' => 'allow'], $data);
    }

    /**
     * 记录日志并返回拒绝结果
     * @param string $message 拒绝原因
     * @param array $logData 日志数据
     * @return array 拒绝结果
     */
    private function deny(string $message, array $logData = []): array
    {
        // 构建日志消息
        $logMessage = 'MQTT authenticate denied: ' . $message . ' ' . $this->formatLogData($logData);
        Log::warning($logMessage);

        return ['result' => 'deny', 'message' => $message];
    }

    /**
     * 格式化日志数据为字符串
     * @param array $logData 日志数据
     * @return string 格式化后的字符串
     */
    private function formatLogData(array $logData): string
    {
        if (empty($logData)) {
            return '';
        }

        $parts = [];

        foreach ($logData as $key => $value) {
            $parts[] = $key . '=' . (is_scalar($value) ? $value : json_encode($value, JSON_UNESCAPED_UNICODE));
        }

        return '[' . implode(', ', $parts) . ']';
    }

    /**
     * 判断用户是否有权限发布租户级别的通知
     * @param array $tenant 租户信息
     * @param int $userId 用户ID
     * @return bool 是否有权限
     */
    private function canPublishTenantNotification(array $tenant, int $userId): bool
    {
        $cacheUserInfo = CacheService::getInstance()->getUserCacheInfo((int)$tenant['id'], $userId);

        // 如果用户是创始人，允许发布租户级别的通知
        return $cacheUserInfo['is_founder'] ?? false;
    }
}
