<?php
declare(strict_types = 1);

namespace app\tenant\service\login;

use app\tenant\model\User as UserModel;
use app\tenant\repository\UserRepository;

/**
 * 账号密码登录策略类
 * 实现了基于用户名和密码的登录验证
 * 包含以下主要功能：
 * - 验证用户提供的用户名和密码
 * - 支持租户隔离的用户验证
 * - 使用密码哈希进行安全验证
 * <AUTHOR> <<EMAIL>>
 * @package app\tenant\service\login
 */
class AccountLoginStrategy implements LoginStrategyInterface
{
    /**
     * 执行账户登录验证
     * @param array $credentials 登录凭证，需要包含tenant_id、username和password字段
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return UserModel|null
     */
    public function login(array $credentials): ?UserModel
    {
        if (!isset($credentials['tenant_id']) || !isset($credentials['username']) || !isset($credentials['password'])) {
            return null;
        }

        $user = (new UserRepository())->findByUsername($credentials['username'], $credentials['tenant_id']);

        if (!$user || !$user->verifyPassword($credentials['password'])) {
            return null;
        }

        return $user;
    }

    /**
     * 获取登录类型
     * @return string
     */
    public function getType(): string
    {
        return 'account';
    }
}
