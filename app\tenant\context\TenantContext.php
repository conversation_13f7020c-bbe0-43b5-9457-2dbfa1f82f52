<?php
declare(strict_types = 1);

/**
 * 租户上下文管理类
 * 负责管理租户用户信息，提供租户相关的上下文数据访问，包括：
 * - 租户用户信息的设置与获取
 * - 租户ID和租户编码的获取
 * - 用户ID的获取
 * - 上下文数据的清理
 * <AUTHOR> <<EMAIL>>
 * @package app\tenant\context
 */
namespace app\tenant\context;

use app\tenant\repository\UserRepository;

class TenantContext
{
    private static ?self $instance = null;

    private array $userInfo = [];

    private function __construct()
    {
    }

    public static function getInstance(): self
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }

        return self::$instance;
    }

    /**
     * 设置租户用户信息
     * @param array $userInfo
     */
    public function setUserInfo(array $userInfo = []): void
    {
        $this->userInfo = $userInfo;
    }

    /**
     * 获取租户用户是否是创始人
     * @return int
     */
    public function isFounder(): int
    {
        // 从用户完整信息里获取
        return $this->getUserInfo(true)['founder'] ?? 0;
    }

    /**
     * 获取租户ID
     * @return int
     */
    public function getTenantId(): int
    {
        return $this->userInfo['tenant_id'] ?? 0;
    }

    /**
     * 获取租户编码
     * @return ?string
     */
    public function getTenantCode(): ?string
    {
        return $this->userInfo['tenant_code'] ?? null;
    }

    /**
     * 获取租户用户信息
     * @param bool $complete 是否用户完整数据，false时返回用户简易数据
     * @return array
     */
    public function getUserInfo(bool $complete = false): array
    {
        if ($complete) {
            // 查询完整信息
            $this->userInfo = (new UserRepository())->getTenantUserById($this->getUserId());
        }

        return $this->userInfo;
    }

    /**
     * 获取用户ID
     * @return int
     */
    public function getUserId(): int
    {
        return $this->userInfo['id'] ?? 0;
    }

    /**
     * 清除租户用户信息
     */
    public function clearUserInfo(): void
    {
        $this->userInfo = [];
    }
}
