<?php
declare(strict_types = 1);

namespace app\system\validate;

use think\Validate;

/**
 * 操作日志验证器
 * 负责操作日志相关数据的验证规则
 * <AUTHOR> <<EMAIL>>
 * @package app\system\validate
 */
class OperationLog extends Validate
{
    /**
     * 验证规则
     * @var array
     */
    protected $rule = [
        'id'          => 'require|number',
        'user_id'     => 'require|number',
        'event'       => 'require|max:100',
        'path'        => 'require|max:200',
        'params'      => 'require',
        'description' => 'require|max:500',
        'user_agent'  => 'require|max:500',
        'client_ip'   => 'require|ip',
        'mem'         => 'float',
        'reqs'        => 'float',
        'runtime'     => 'float',
        'page'        => 'number',
        'pageSize'    => 'number',
    ];

    /**
     * 错误信息
     * @var array
     */
    protected $message = [
        'id.require'          => 'ID不能为空',
        'id.number'           => 'ID必须为数字',
        'user_id.require'     => '用户ID不能为空',
        'user_id.number'      => '用户ID必须为数字',
        'event.require'       => '事件名称不能为空',
        'event.max'           => '事件名称最多不能超过100个字符',
        'path.require'        => '请求路径不能为空',
        'path.max'            => '请求路径最多不能超过200个字符',
        'params.require'      => '请求参数不能为空',
        'description.require' => '描述不能为空',
        'description.max'     => '描述最多不能超过500个字符',
        'user_agent.require'  => '用户代理不能为空',
        'user_agent.max'      => '用户代理最多不能超过500个字符',
        'client_ip.require'   => '客户端IP不能为空',
        'client_ip.ip'        => '客户端IP格式不正确',
        'mem.float'           => '内存使用量必须为浮点数',
        'reqs.float'          => '请求数必须为浮点数',
        'runtime.float'       => '运行时间必须为浮点数',
        'page.number'         => '页码必须为数字',
        'pageSize.number'     => '每页数量必须为数字',
    ];

    /**
     * 验证场景
     * @var array
     */
    protected $scene = [
        'list'   => ['page', 'pageSize'],
        'read'   => ['id'],
        'delete' => ['id'],
    ];
}
