<?php
namespace app\system\repository;

use app\system\model\Configure as ConfigureModel;

/**
 * 配置数据仓库类
 * 负责系统配置项的数据访问和操作
 * <AUTHOR> <<EMAIL>>
 * @package app\system\repository
 */
class ConfigureRepository extends BaseRepository
{
    /**
     * 构造方法
     */
    public function __construct()
    {
        // 指定当前模型类名而非实例，避免条件累积问题
        parent::__construct(ConfigureModel::class);
    }

    /**
     * 获取控件类型列表
     * @return array
     */
    public function getWidgetTypes(): array
    {
        return ConfigureModel::$widgetType;
    }

    /**
     * 获取全部分组和对应数据
     * @return array
     */
    public function getGroupListData(): array
    {
        $where = [
            ['gro.hidden', '=', 0],
            ['gro.status', '=', 1],
            ['conf.status', '=', 1],
        ];

        $list = $this->model->alias('conf')
            ->join('system_configure_group gro', 'conf.group = gro.name', 'LEFT')
            ->where($where)
            ->order(['gro.sort' => 'ASC', 'conf.sort' => 'ASC'])
            ->field(['conf.*', 'gro.name' => 'group_name', 'gro.label' => 'group_label', 'gro.icon' => 'group_icon', 'gro.hidden' => 'group_hidden'])
            ->select()
            ->toArray();

        return $this->formatGroupListData($list);
    }

    /**
     * 获取指定分组的配置数据
     * @param string $group
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return array
     */
    public function getGroupData(string $group = 'default'): array
    {
        $list = $this->model->where([
            ['group', '=', $group],
            ['hidden', '=', 0],
            ['status', '=', 1],
        ])
        ->field(['name', 'value'])
        ->select()
        ->toArray();

        $data = [];

        foreach ($list as $item) {
            $data[$item['name']] = $item['value'];
        }

        return $data;
    }

    /**
     * 保存分组配置数据
     * @param string $group
     * @param array $data
     * @return bool
     */
    public function saveGroupData(string $group, array $data): bool
    {
        if ($group && $data) {
            foreach ($data as $key => $value) {
                $this->model->update([
                    'value'     => $value,
                    'update_at' => time(),
                ], [
                    ['group', '=', $group],
                    ['name', '=', $key],
                ]);
            }
        }

        return true;
    }

    /**
     * 格式化分组列表数据
     * @param array $list
     * @return array
     */
    protected function formatGroupListData(array $list): array
    {
        $data = [
            'group'  => [],
            'config' => [],
        ];

        foreach ($list as $item) {
            $group_name = $item['group'];
            unset($item['group']);

            if (!isset($data['group'][$group_name])) {
                $data['group'][$group_name] = [
                    'name'   => $item['group_name'],
                    'label'  => $item['group_label'],
                    'icon'   => $item['group_icon'],
                    'hidden' => $item['group_hidden'],
                ];
            }

            if (!empty($item['config'])) {
                $item['config'] = json_decode($item['config'], true);
            }

            if (!empty($item['options'])) {
                $item['options'] = json_decode($item['options'], true);
            }

            $data['config'][$group_name][] = $item;
        }

        // 默认分组
        $data['default'] = key($data['group']);

        return $data;
    }
}
