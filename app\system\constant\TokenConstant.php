<?php
declare(strict_types = 1);
namespace app\system\constant;

use app\common\constant\CommonConstant;

/**
 * token相关常量 (10200-10299)
 * <AUTHOR> <<EMAIL>>
 * @package app\system\constant
 */
class TokenConstant extends CommonConstant
{
    public const ACCESS_TOKEN_EMPTY = ['code' => 10200, 'message' => '无效的会话，或者会话已过期，请重新登录', 'label' => 'needReLogin'];

    public const ACCESS_TOKEN_ERROR = ['code' => 10201, 'message' => '请求ACCESS_TOKEN中缺少必要信息', 'label' => 'needReLogin'];

    public const ACCESS_TOKEN_INVALID = ['code' => 10202, 'message' => 'TOKEN缓存失效，请重新登录', 'label' => 'needReLogin'];

    public const ACCESS_TOKEN_EXPIRED = ['code' => 10203, 'message' => 'ACCESS_TOKEN已过期，请重新获取', 'label' => 'refreshToken'];

    public const TOKEN_VERIFY_INVALID = ['code' => 10204, 'message' => 'TOKEN无效，请重新获取', 'label' => 'needReLogin'];

    public const TOKEN_BEFORE_VALID = ['code' => 10205, 'message' => 'TOKEN使用时间还未开始', 'label' => 'needReLogin'];

    public const TOKEN_SIGNATURE_INVALID = ['code' => 10206, 'message' => 'TOKEN签名错误', 'label' => 'needReLogin'];

    public const TOKEN_INVALID_ARGUMENT = ['code' => 10207, 'message' => 'TOKEN参数无效', 'label' => 'needReLogin'];

    public const TOKEN_TYPE_ERROR = ['code' => 10208, 'message' => 'TOKEN类型错误', 'label' => 'needReLogin'];

    public const REFRESH_TOKEN_EXPIRED = ['code' => 10209, 'message' => '登录状态已过期，您可以继续留在该页面，或者重新登录', 'label' => 'needReLogin'];

    public const REFRESH_TOKEN_INVALID = ['code' => 10210, 'message' => '登录状态已失效，要继续使用请重新登录', 'label' => 'needReLogin'];

    public const REFRESH_TOKEN_ERROR = ['code' => 10211, 'message' => 'REFRESH_TOKEN错误', 'label' => 'needReLogin'];

    public const ISSUE_TOKEN_FAILED = ['code' => 10212, 'message' => '签发TOKEN失败', 'label' => 'needReLogin'];

    public const LOGIN_CACHE_EMPTY = ['code' => 10220, 'message' => '无效的会话，或者会话已过期，请重新登录', 'label' => 'refreshToken'];

    public const LOGIN_NODE_EMPTY = ['code' => 10221, 'message' => '无操作权限，请重新登录获取授权', 'label' => 'refreshToken'];

    public const LOGIN_DEVICE_EMPTY = ['code' => 10222, 'message' => '无效的会话，设备指纹信息已过期，请重新登录', 'label' => 'kickOuted'];

    /**
     * 根据code获取message
     * @param int|string $code
     * @return string
     */
    public static function getMessage(int|string $code): string
    {
        foreach (static::getConstants() as $constant) {
            if ($constant['code'] === $code) {
                return $constant['message'];
            }
        }

        return '未知错误';
    }

    public static function getLabel(int|string $code): string
    {
        foreach (static::getConstants() as $constant) {
            if ($constant['code'] === $code) {
                return $constant['label'] ?? '';
            }
        }

        return '';
    }

    private static function getConstants(): array
    {
        $ref = new \ReflectionClass(static::class);

        return $ref->getConstants();
    }
}
