<?php
declare(strict_types = 1);

namespace app\tenant\controller;

use app\common\service\mqtt\MqttService;
use app\tenant\context\TenantContext;
use think\App;
use think\facade\Log;
use think\Response;

/**
 * SSE消息通知控制器
 * 用于实现服务器推送事件(Server-Sent Events)功能，向客户端发送实时通知
 * <AUTHOR> <<EMAIL>>
 * @package app\tenant\controller
 */
class Sse extends AuthBase
{
    /**
     * 活跃的SSE连接列表
     * 格式: [tenantId => [userId => connection, ...], ...]
     * @var array
     */
    protected static array $connections = [];

    /**
     * MQTT服务实例
     * @var MqttService
     */
    protected MqttService $mqttService;

    /**
     * 构造方法
     * @param App $app
     * @throws \app\tenant\exception\AccountException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->mqttService = MqttService::getInstance();
    }

    /**
     * 建立SSE连接
     * 客户端通过此接口建立长连接，接收服务器推送的消息
     * @return Response
     */
    public function connect(): Response
    {
        // 获取当前租户和用户信息
        $tenantId = TenantContext::getInstance()->getTenantId();
        $userId   = $this->getUserId();

        // 设置响应头，启用SSE
        $response = Response::create();
        $response->header([
            'Content-Type'      => 'text/event-stream',
            'Cache-Control'     => 'no-cache',
            'Connection'        => 'keep-alive',
            'X-Accel-Buffering' => 'no', // 禁用Nginx缓冲
        ]);

        // 注册连接
        $this->registerConnection($tenantId, $userId);

        // 发送初始连接成功消息
        $response->content($this->formatSseMessage('connected', json_encode([
            'status'    => 'success',
            'message'   => '连接成功',
            'timestamp' => time(),
        ])));

        // 记录日志
        Log::info("SSE连接已建立: 租户ID {$tenantId}, 用户ID {$userId}");

        return $response;
    }

    /**
     * 向指定租户的所有用户发送通知
     * @param int $tenantId 租户ID
     * @param string $event 事件名称
     * @param array $data 消息数据
     * @return bool
     */
    public function sendToTenant(int $tenantId, string $event, array $data): bool
    {
        if (!isset(self::$connections[$tenantId]) || empty(self::$connections[$tenantId])) {
            return false;
        }

        $message = $this->formatSseMessage($event, json_encode($data, JSON_UNESCAPED_UNICODE));

        foreach (self::$connections[$tenantId] as $userId => $connection) {
            try {
                // 向连接发送消息
                // 在实际实现中，这里需要根据框架的具体实现方式发送数据
                // 由于ThinkPHP不直接支持SSE长连接保持，这里仅作为示例
                // 实际应用中可能需要使用Swoole等扩展来实现

                // 记录日志
                Log::info("向租户 {$tenantId} 的用户 {$userId} 发送SSE消息: {$event}");
            } catch (\Exception $e) {
                Log::error('发送SSE消息失败: ' . $e->getMessage());
                // 移除失效的连接
                $this->removeConnection($tenantId, $userId);
            }
        }

        return true;
    }

    /**
     * 向指定用户发送通知
     * @param int $tenantId 租户ID
     * @param int $userId 用户ID
     * @param string $event 事件名称
     * @param array $data 消息数据
     * @return bool
     */
    public function sendToUser(int $tenantId, int $userId, string $event, array $data): bool
    {
        if (!isset(self::$connections[$tenantId][$userId])) {
            return false;
        }

        try {
            $message = $this->formatSseMessage($event, json_encode($data, JSON_UNESCAPED_UNICODE));

            // 向连接发送消息
            // 同样，这里需要根据实际框架实现方式发送数据

            // 记录日志
            Log::info("向租户 {$tenantId} 的用户 {$userId} 发送SSE消息: {$event}");

            return true;
        } catch (\Exception $e) {
            Log::error('发送SSE消息失败: ' . $e->getMessage());
            // 移除失效的连接
            $this->removeConnection($tenantId, $userId);

            return false;
        }
    }

    /**
     * 注册SSE连接
     * @param int $tenantId 租户ID
     * @param int $userId 用户ID
     */
    protected function registerConnection(int $tenantId, int $userId): void
    {
        if (!isset(self::$connections[$tenantId])) {
            self::$connections[$tenantId] = [];
        }

        // 在实际实现中，这里应该存储连接对象
        // 由于ThinkPHP默认不支持长连接保持，这里仅作为示例
        self::$connections[$tenantId][$userId] = true;
    }

    /**
     * 移除SSE连接
     * @param int $tenantId 租户ID
     * @param int $userId 用户ID
     */
    protected function removeConnection(int $tenantId, int $userId): void
    {
        if (isset(self::$connections[$tenantId][$userId])) {
            unset(self::$connections[$tenantId][$userId]);

            // 如果租户下没有任何连接，清理租户记录
            if (empty(self::$connections[$tenantId])) {
                unset(self::$connections[$tenantId]);
            }

            Log::info("SSE连接已移除: 租户ID {$tenantId}, 用户ID {$userId}");
        }
    }

    /**
     * 格式化SSE消息
     * @param string $event 事件名称
     * @param string $data 消息数据
     * @return string
     */
    protected function formatSseMessage(string $event, string $data): string
    {
        return "event: {$event}\ndata: {$data}\n\n";
    }

    /**
     * 发送心跳包
     * 用于保持连接活跃
     * @return string
     */
    protected function sendHeartbeat(): string
    {
        return ":\n\n"; // SSE注释行，用作心跳
    }

    /**
     * 通过MQTT发布SSE消息
     * 允许其他服务通过MQTT向SSE控制器发送消息，再由SSE控制器推送给客户端
     * @param string $topic MQTT主题
     * @param array $message 消息内容
     * @return bool
     */
    public function publishViaMqtt(string $topic, array $message): bool
    {
        try {
            return $this->mqttService->publish($topic, json_encode($message, JSON_UNESCAPED_UNICODE));
        } catch (\Exception $e) {
            Log::error('通过MQTT发布SSE消息失败: ' . $e->getMessage());

            return false;
        }
    }
}
