<?php
declare(strict_types = 1);

namespace app\tenant\command;

use app\common\service\mqtt\MqttService;
use Swoole\Coroutine;

use function Swoole\Coroutine\run;

use Swoole\Runtime;
use think\console\Input;
use think\console\input\Option;
use think\console\Output;

/**
 * 客户端状态模拟器命令
 * 用于模拟客户端发送状态消息（上线/离线）
 * <AUTHOR> <<EMAIL>>
 * @package app\tenant\command
 */
class ClientStatusSimulatorCommand extends BaseCommand
{
    /**
     * MQTT配置
     * @var array
     */
    private array $config = [];

    /**
     * 进程锁文件路径
     * @var string
     */
    private string $lockFile = '';

    /**
     * MQTT服务实例
     * @var MqttService
     */
    protected MqttService $mqttService;

    /**
     * 是否继续运行
     * @var bool
     */
    protected bool $running = true;

    /**
     * 模拟客户端列表
     * @var array
     */
    protected array $simulatedClients = [];

    /**
     * 客户端类型列表
     * @var array
     */
    protected array $clientTypes = ['web', 'mobile', 'desktop', 'app'];

    public function __construct()
    {
        parent::__construct();

        // 合并全局配置和tenant模块配置
        $this->config = getAppConfig('mqtt', 'tenant');
        // 初始化进程锁文件路径
        $this->lockFile = runtime_path() . 'client_status_simulator.lock';
    }

    /**
     * 配置指令
     * php think tenant:clientStatusSimulator
     * php think tenant:clientStatusSimulator -t shengli
     * php think tenant:clientStatusSimulator --tenant 租户编码
     * @return void
     */
    protected function configure(): void
    {
        $this->setName('tenant:clientStatusSimulator')
            ->setDescription('启动客户端状态模拟器服务')
            ->addOption('tenant', 't', Option::VALUE_OPTIONAL, '指定租户编码，默认为shengli', 'shengli')
            ->addOption('clients', 'c', Option::VALUE_OPTIONAL, '模拟客户端数量', 5);
    }

    /**
     * 执行指令
     * @param Input $input
     * @param Output $output
     * @return void
     */
    protected function execute(Input $input, Output $output): void
    {
        // 检查是否已有实例在运行
        if (file_exists($this->lockFile)) {
            $pid = file_get_contents($this->lockFile);

            if ($pid && posix_kill((int)$pid, 0)) {
                $this->logError("已有一个客户端状态模拟器实例正在运行，进程ID：{$pid}");

                return;
            }

            // 如果进程不存在，删除锁文件
            unlink($this->lockFile);
        }

        // 创建进程锁
        file_put_contents($this->lockFile, getmypid());

        Runtime::enableCoroutine();

        // 在协程环境中运行MQTT客户端
        run(function() use ($input, $output) {
            // 初始化MQTT服务
            $this->mqttService = MqttService::getInstance();

            if (!$this->mqttService->connect()) {
                $this->logError('MQTT服务连接失败，失败原因请查看Log');

                return;
            }

            // 获取租户编码参数
            $tenantCode = $input->getOption('tenant');
            // 获取模拟客户端数量
            $clientCount = (int)$input->getOption('clients');

            $this->logInfo("开始模拟租户【{$tenantCode}】的{$clientCount}个客户端状态变化");

            // 注册信号处理
            pcntl_signal(SIGTERM, [$this, 'handleSignal']);
            pcntl_signal(SIGINT, [$this, 'handleSignal']);

            // 创建信号处理协程
            Coroutine::create(function() {
                while ($this->running) {
                    pcntl_signal_dispatch();
                    Coroutine::sleep(0.1); // 每100ms检查一次信号
                }
            });

            // 为每个模拟客户端创建一个协程
            for ($i = 1; $i <= $clientCount; $i++) {
                $userId     = 1000 + $i;
                $clientType = $this->clientTypes[array_rand($this->clientTypes)];

                Coroutine::create(function() use ($tenantCode, $userId, $clientType) {
                    $this->simulateClientStatus($tenantCode, $userId, $clientType);
                });
            }

            // 主循环
            while ($this->running) {
                Coroutine::sleep(1);
            }

            // 优雅退出
            $this->logWarning('客户端状态模拟器服务已停止');

            // 删除进程锁
            if (file_exists($this->lockFile)) {
                unlink($this->lockFile);
            }
        });
    }

    /**
     * 模拟客户端状态变化
     * @param string $tenantCode 租户编码
     * @param int $userId 用户ID
     * @param string $clientType 客户端类型
     */
    protected function simulateClientStatus(string $tenantCode, int $userId, string $clientType): void
    {
        $clientId                          = "{$tenantCode}_{$userId}_{$clientType}";
        $this->simulatedClients[$clientId] = true;

        while ($this->running && isset($this->simulatedClients[$clientId])) {
            try {
                // 设置遗嘱消息
                $this->setLastWillMessage($tenantCode, $userId, $clientType);

                // 模拟客户端上线
                $this->sendClientStatus($tenantCode, $userId, $clientType, 'online');
                $this->logInfo("客户端上线: 租户[{$tenantCode}] 用户[{$userId}] 类型[{$clientType}]");

                // 随机在线时间（30秒到3分钟）
                $onlineTime = random_int(30, 180);
                $startTime  = time();

                // 在线期间定期发送心跳
                $heartbeatInterval = 15; // 每15秒发送一次心跳

                while ($this->running && (time() - $startTime) < $onlineTime) {
                    // 发送心跳
                    $this->sendClientStatus($tenantCode, $userId, $clientType, 'alive');
                    $this->logInfo("客户端心跳: 租户[{$tenantCode}] 用户[{$userId}] 类型[{$clientType}]");

                    // 等待下一次心跳
                    Coroutine::sleep($heartbeatInterval);
                }

                // 模拟客户端正常离线
                $this->sendClientStatus($tenantCode, $userId, $clientType, 'offline');
                $this->logInfo("客户端离线: 租户[{$tenantCode}] 用户[{$userId}] 类型[{$clientType}] 在线时长: " . (time() - $startTime) . '秒');

                // 随机离线时间（10秒到1分钟）
                $offlineTime = random_int(10, 60);
                Coroutine::sleep($offlineTime);
            } catch (\Exception $e) {
                $this->logError('模拟客户端状态异常: ' . $e->getMessage());
                Coroutine::sleep(5); // 发生异常时等待5秒后重试
            }
        }
    }

    /**
     * 发送客户端状态消息
     * @param string $tenantCode 租户编码
     * @param int $userId 用户ID
     * @param string $clientType 客户端类型
     * @param string $status 状态（online/offline）
     * @return bool
     */
    protected function sendClientStatus(string $tenantCode, int $userId, string $clientType, string $status): bool
    {
        try {
            // 构建状态主题
            $statusTopic = "helio/tenant/{$tenantCode}/userId/{$userId}/heartbeat";

            // 构建状态数据
            $statusData = [
                'payload' => [
                    'status'     => $status,
                    'timestamp'  => time() * 1000, // 毫秒时间戳
                    'tenantCode' => $tenantCode,
                    'userId'     => (string)$userId,
                    'clientType' => $clientType,
                ],
            ];

            // 根据状态类型设置不同的QoS和retain值
            if ($status === 'online' || $status === 'offline') {
                // 上线和离线消息使用QoS=1，retain=true
                return $this->mqttService->publish(
                    $statusTopic,
                    json_encode($statusData, JSON_UNESCAPED_UNICODE),
                    1,  // QoS=1
                    0,  // dup=0
                    1   // retain=true
                );
            }

            // 心跳消息使用QoS=0，retain=false
            return $this->mqttService->publish(
                $statusTopic,
                json_encode($statusData, JSON_UNESCAPED_UNICODE),
                0,  // QoS=0
                0,  // dup=0
                0   // retain=false
            );
        } catch (\Exception $e) {
            $this->logError('发送客户端状态消息异常: ' . $e->getMessage());

            return false;
        }
    }

    /**
     * 设置客户端遗嘱消息
     * @param string $tenantCode 租户编码
     * @param int $userId 用户ID
     * @param string $clientType 客户端类型
     * @return bool
     */
    protected function setLastWillMessage(string $tenantCode, int $userId, string $clientType): bool
    {
        try {
            // 在实际环境中，遗嘱消息应该在MQTT连接时设置
            // 这里我们只是模拟记录一下，实际上无法在连接后设置遗嘱消息

            // 构建遗嘱主题
            $willTopic = "helio/tenant/{$tenantCode}/userId/{$userId}/heartbeat";

            // 构建遗嘱数据
            $willData = [
                'payload' => [
                    'status'     => 'offline', // 遗嘱消息状态为offline
                    'timestamp'  => time() * 1000, // 毫秒时间戳
                    'tenantCode' => $tenantCode,
                    'userId'     => (string)$userId,
                    'clientType' => $clientType,
                    'reason'     => '客户端异常断开连接',
                ],
            ];

            $this->logInfo("客户端设置遗嘱消息: 租户[{$tenantCode}] 用户[{$userId}] 类型[{$clientType}]");

            // 注意：在实际环境中，遗嘱消息应该在MQTT连接时通过连接选项设置
            // 这里我们只是模拟记录，返回true表示模拟成功
            return true;
        } catch (\Exception $e) {
            $this->logError('设置客户端遗嘱消息异常: ' . $e->getMessage());

            return false;
        }
    }

    /**
     * 处理指定信号
     * @param int $signal
     */
    public function handleSignal(int $signal): void
    {
        switch ($signal) {
            case SIGTERM:
            case SIGINT:
                $this->running = false;
                break;
        }
    }
}
