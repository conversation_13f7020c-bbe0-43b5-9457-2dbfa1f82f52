<?php
namespace app\system\validate;

/**
 * 配置分组验证器
 * <AUTHOR> <<EMAIL>>
 * @package app\system\validate
 */
class ConfigureGroup extends BaseValidate
{
    protected $rule = [
        'id'     => 'require',
        'name'   => 'require|max:25|unique:system_configure_group,name',
        'label'  => 'require|max:25|unique:system_configure_group,label',
        'sort'   => 'between:-1000,1000',
        'hidden' => 'in:0,1',
        'status' => 'in:0,1',
    ];

    protected $message = [
        'id.require'    => 'ID不能为空',
        'name.require'  => '分组名称不能为空',
        'name.max'      => '分组名称最多不能超过25个字符',
        'label.require' => '分组别名不能为空',
        'label.max'     => '分组别名最多不能超过25个字符',
        'sort.between'  => '排序号只能是-1000到1000之间的整数值',
        'hidden.in'     => '隐藏状态错误',
        'status.in'     => '状态值错误',
        'name.unique'   => '分组名称已存在',
        'label.unique'  => '分组别名已存在',
    ];

    protected $scene = [
        'add'    => ['name','label','sort','hidden','status'],
        'edit'   => ['id','name','label','sort','hidden','status'],
        'hidden' => ['id', 'hidden'],
        'status' => ['id', 'status'],
        'delete' => ['id'],
    ];
}
