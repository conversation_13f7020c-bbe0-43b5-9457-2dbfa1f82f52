<?php
// 全局中间件定义文件
use app\common\middleware\AllowCrossDomainMiddleware;
use app\common\middleware\RemovePoweredByMiddleware;
use app\common\middleware\RequestIdMiddleware;
use app\system\middleware\AppModuleCheckMiddleware;

return [
    // 全局请求缓存
    // \think\middleware\CheckRequestCache::class,
    // 多语言加载
    // \think\middleware\LoadLangPack::class,
    // Session初始化
    // \think\middleware\SessionInit::class,
    // 跨域中间件
    AllowCrossDomainMiddleware::class,
    // 移除header中X-Powered-By中间件
    RemovePoweredByMiddleware::class,
    // 响应头中添加requestId中间件
    RequestIdMiddleware::class,
    // 应用模块检查中间件（用于检测应用模块是否安装）
    AppModuleCheckMiddleware::class,
];
