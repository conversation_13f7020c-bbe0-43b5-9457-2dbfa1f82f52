<?php
declare(strict_types = 1);

namespace app\tenant\command;

use app\common\service\mqtt\MqttClientFactory;
use Swoole\Coroutine;

use function Swoole\Coroutine\run;

use Swoole\Runtime;
use think\console\Input;
use think\console\input\Option;
use think\console\Output;

/**
 * 真实设备模拟器命令
 * 用于模拟真实设备的连接、心跳和异常离线场景
 * <AUTHOR> <<EMAIL>>
 * @package app\tenant\command
 */
class RealDeviceSimulatorCommand extends BaseCommand
{
    /**
     * MQTT配置
     * @var array
     */
    private array $config = [];

    /**
     * 进程锁文件路径
     * @var string
     */
    private string $lockFile = '';

    /**
     * 是否继续运行
     * @var bool
     */
    protected bool $running = true;

    /**
     * 设备列表
     * @var array
     */
    protected array $devices = [];

    /**
     * 心跳间隔（秒）
     * @var int
     */
    protected int $heartbeatInterval = 10;

    /**
     * 租户编码
     * @var string
     */
    protected string $tenantCode = 'shengli';

    /**
     * MQTT客户端工厂
     * @var MqttClientFactory
     */
    protected MqttClientFactory $mqttClientFactory;

    public function __construct()
    {
        parent::__construct();

        // 获取tenant模块的mqtt配置
        $this->config = getAppConfig('mqtt', 'tenant');
        // 初始化进程锁文件路径
        $this->lockFile = runtime_path() . 'real_device_simulator.lock';
        // 初始化MQTT客户端工厂
        $this->mqttClientFactory = new MqttClientFactory();
    }

    /**
     * 配置指令
     * php think tenant:realDeviceSimulator
     * php think tenant:realDeviceSimulator -t shengli
     * php think tenant:realDeviceSimulator --tenant 租户编码
     * php think tenant:realDeviceSimulator -d 10001,10003,10005
     * php think tenant:realDeviceSimulator --devices 10001,10003,10005
     * @return void
     */
    protected function configure(): void
    {
        $this->setName('tenant:realDeviceSimulator')
            ->setDescription('启动真实设备模拟器服务')
            ->addOption('tenant', 't', Option::VALUE_OPTIONAL, '指定租户编码，默认为shengli', 'shengli')
            ->addOption('devices', 'd', Option::VALUE_OPTIONAL, '指定设备ID列表，多个ID用逗号分隔', '10001,10003,10005,10007,10009');
    }

    /**
     * 执行指令
     * @param Input $input
     * @param Output $output
     * @return void
     */
    protected function execute(Input $input, Output $output): void
    {
        // 检查是否已有实例在运行
        if (file_exists($this->lockFile)) {
            $pid = file_get_contents($this->lockFile);

            if ($pid && posix_kill((int)$pid, 0)) {
                $this->logError("已有一个真实设备模拟器实例正在运行，进程ID：{$pid}");

                return;
            }

            // 如果进程不存在，删除锁文件
            unlink($this->lockFile);
        }

        // 创建进程锁
        file_put_contents($this->lockFile, getmypid());

        // 获取租户编码
        $this->tenantCode = $input->getOption('tenant');

        // 获取设备ID列表
        $deviceIds = $input->getOption('devices');
        $deviceIds = explode(',', $deviceIds);

        Runtime::enableCoroutine();

        // 在协程环境中运行设备模拟器
        run(function() use ($deviceIds) {
            // 注册信号处理
            pcntl_signal(SIGTERM, [$this, 'handleSignal']);
            pcntl_signal(SIGINT, [$this, 'handleSignal']);

            // 创建信号处理协程
            Coroutine::create(function() {
                while ($this->running) {
                    pcntl_signal_dispatch();
                    Coroutine::sleep(0.1); // 每100ms检查一次信号
                }
            });

            // 为每个设备创建一个协程
            foreach ($deviceIds as $deviceId) {
                Coroutine::create(function() use ($deviceId) {
                    $this->simulateDevice($deviceId);
                });
            }

            // 主循环
            while ($this->running) {
                Coroutine::sleep(1);
            }

            // 优雅退出
            $this->logWarning('真实设备模拟器服务已停止');

            // 删除进程锁
            if (file_exists($this->lockFile)) {
                unlink($this->lockFile);
            }
        });
    }

    /**
     * 模拟单个设备行为
     * @param string $deviceId
     */
    protected function simulateDevice(string $deviceId): void
    {
        while ($this->running) {
            try {
                // 随机决定设备是否上线
                $onlineTime  = random_int(60, 300); // 在线时间1-5分钟
                $offlineTime = random_int(30, 180); // 离线时间30秒-3分钟

                // 随机决定是正常断开还是异常断开
                $isNormalDisconnect = (random_int(1, 10) <= 5); // 50%概率正常断开，50%概率异常断开

                // 设备上线（创建真实的MQTT连接，并设置遗嘱消息）
                $device = $this->connectDevice($deviceId, $isNormalDisconnect);

                if (!$device) {
                    $this->logError("设备 {$deviceId} 连接失败，等待5秒后重试");
                    Coroutine::sleep(5);
                    continue;
                }

                $this->logInfo("设备 {$deviceId} 上线成功" . ($isNormalDisconnect ? '（将正常断开）' : '（将异常断开）'));

                // 发送上线状态
                $this->sendDeviceStatus($device, 'online');

                // 在线期间发送心跳
                $startTime = time();

                while ($this->running && (time() - $startTime) < $onlineTime && isset($this->devices[$deviceId])) {
                    // 发送心跳
                    $this->sendDeviceHeartbeat($device);

                    // 等待下一次心跳
                    Coroutine::sleep($this->heartbeatInterval);
                }

                // 设备离线
                if (isset($this->devices[$deviceId])) {
                    if ($isNormalDisconnect) {
                        // 正常断开：先发送离线消息，再断开连接
                        $this->sendDeviceStatus($device, 'offline');
                        $this->disconnectDevice($deviceId, true);
                        $this->logInfo("设备 {$deviceId} 正常断开连接");
                    } else {
                        // 异常断开：直接断开连接，不发送离线消息
                        // 这将触发MQTT服务器发送预设的遗嘱消息
                        $this->disconnectDevice($deviceId, false);
                        $this->logInfo("设备 {$deviceId} 异常断开连接（将触发遗嘱消息）");
                    }
                }

                // 等待一段时间后重新上线
                Coroutine::sleep($offlineTime);
            } catch (\Exception $e) {
                $this->logError("设备 {$deviceId} 模拟异常: " . $e->getMessage());

                // 确保设备断开连接
                if (isset($this->devices[$deviceId])) {
                    $this->disconnectDevice($deviceId, false);
                }

                Coroutine::sleep(5); // 发生异常时等待5秒后重试
            }
        }
    }

    /**
     * 连接设备到MQTT服务器
     * @param string $deviceId 设备ID
     * @param bool $isNormalDisconnect 是否正常断开
     * @return array|null 设备信息
     */
    protected function connectDevice(string $deviceId, bool $isNormalDisconnect): ?array
    {
        try {
            // 设置遗嘱消息（只有异常断开时才会发送）
            $willTopic   = "helio/tenant/{$this->tenantCode}/device/{$deviceId}/heartbeat";
            $willMessage = json_encode([
                'deviceId'   => $deviceId,
                'tenantCode' => $this->tenantCode,
                'timestamp'  => time(),
                'status'     => 'offline',
                'reason'     => '设备异常断开连接',
            ], JSON_UNESCAPED_UNICODE);

            // 设置遗嘱消息
            $willConfig = [
                'topic'   => $willTopic,
                'message' => $willMessage,
                'qos'     => 1,
                'retain'  => 1,
            ];

            // 为设备创建独立的MQTT客户端实例
            $mqttClient = $this->mqttClientFactory->create("device_{$deviceId}", $willConfig);

            // 检查连接是否成功
            if (!$mqttClient->isConnected()) {
                return null;
            }

            // 保存设备信息
            $this->devices[$deviceId] = [
                'mqtt'               => $mqttClient,
                'deviceId'           => $deviceId,
                'isNormalDisconnect' => $isNormalDisconnect,
            ];

            return $this->devices[$deviceId];
        } catch (\Exception $e) {
            $this->logError("设备 {$deviceId} 连接异常: " . $e->getMessage());

            return null;
        }
    }

    /**
     * 断开设备连接
     * @param string|int $deviceId 设备ID
     * @param bool $graceful 是否优雅断开
     * @return bool
     */
    protected function disconnectDevice($deviceId, bool $graceful): bool
    {
        if (!isset($this->devices[$deviceId])) {
            return false;
        }

        try {
            $device = $this->devices[$deviceId];

            if ($graceful) {
                // 正常断开连接
                $device['mqtt']->disconnect();
            } else {
                // 异常断开连接（不发送DISCONNECT包）
                // 这将触发MQTT服务器发送预设的遗嘱消息
                // 注意：这里我们直接关闭连接，而不是调用disconnect方法
                // 在实际环境中，这相当于设备突然断电或断网
                // 我们通过将MQTT客户端实例设为null来模拟这种情况
                $device['mqtt'] = null;
            }

            // 从设备列表中移除
            unset($this->devices[$deviceId]);

            return true;
        } catch (\Exception $e) {
            $this->logError("设备 {$deviceId} 断开连接异常: " . $e->getMessage());

            // 确保设备从列表中移除
            unset($this->devices[$deviceId]);

            return false;
        }
    }

    /**
     * 发送设备状态消息
     * @param array $device 设备信息
     * @param string $status 状态：online/offline
     * @return bool
     */
    protected function sendDeviceStatus(array $device, string $status): bool
    {
        try {
            $mqttService = $device['mqtt'];
            $deviceId    = $device['deviceId'];

            // 构建状态主题
            $statusTopic = "helio/tenant/{$this->tenantCode}/device/{$deviceId}/heartbeat";

            // 构建状态数据
            $statusData = [
                'deviceId'   => $deviceId,
                'tenantCode' => $this->tenantCode,
                'timestamp'  => time(),
                'status'     => $status,
            ];

            // 发送状态消息（上线、离线消息使用QoS=1，retain=true）
            return $mqttService->publish(
                $statusTopic,
                json_encode($statusData, JSON_UNESCAPED_UNICODE),
                1,  // QoS=1
                0,  // dup=0
                1   // retain=true
            );
        } catch (\Exception $e) {
            $this->logError('发送设备状态消息异常: ' . $e->getMessage());

            return false;
        }
    }

    /**
     * 发送设备心跳
     * @param array $device 设备信息
     * @return bool
     */
    protected function sendDeviceHeartbeat(array $device): bool
    {
        try {
            $mqttService = $device['mqtt'];
            $deviceId    = $device['deviceId'];

            // 构建心跳主题
            $heartbeatTopic = "helio/tenant/{$this->tenantCode}/device/{$deviceId}/heartbeat";

            // 构建心跳数据
            $heartbeatData = [
                'deviceId'   => $deviceId,
                'tenantCode' => $this->tenantCode,
                'timestamp'  => time(),
                'status'     => 'alive',
            ];

            // 发送心跳消息（QoS=0，retain=false）
            $result = $mqttService->publish(
                $heartbeatTopic,
                json_encode($heartbeatData, JSON_UNESCAPED_UNICODE),
                0,  // QoS=0
                0,  // dup=0
                0   // retain=false
            );

            if ($result) {
                $this->logInfo("设备 {$deviceId} 发送心跳成功");
            } else {
                $this->logWarning("设备 {$deviceId} 发送心跳失败");
            }

            return $result;
        } catch (\Exception $e) {
            $this->logError('发送心跳消息异常: ' . $e->getMessage());

            return false;
        }
    }

    /**
     * 处理指定信号
     * @param int $signal
     */
    public function handleSignal(int $signal): void
    {
        switch ($signal) {
            case SIGTERM:
            case SIGINT:
                $this->running = false;

                // 断开所有设备连接
                foreach ($this->devices as $deviceId => $device) {
                    $this->disconnectDevice($deviceId, true);
                }
                break;
        }
    }
}
