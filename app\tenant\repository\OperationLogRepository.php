<?php
declare(strict_types = 1);

namespace app\tenant\repository;

use app\tenant\model\OperationLog as OperationLogModel;

/**
 * 租户操作日志仓库类
 * <AUTHOR> <<EMAIL>>
 * @package app\tenant\repository
 */
class OperationLogRepository extends BaseRepository
{
    /**
     * 构造方法
     */
    public function __construct()
    {
        // 指定当前模型类名而非实例，避免条件累积问题
        parent::__construct(OperationLogModel::class);
    }

    /**
     * 获取用户操作日志列表
     * @param array $params 查询参数
     * @param int $pageNo 页码
     * @param int $pageSize 每页数量
     * @return array
     */
    public function getUserOperationLogs(array $params, int $pageNo = 1, int $pageSize = 20): array
    {
        // 获取查询参数
        $tenantId = $params['tenant_id'] ?? $this->getTenantId();
        $keyword  = trim($params['keyword'] ?? '');
        $userId   = intval($params['user_id'] ?? 0);

        // 设置基础查询条件 - 非删除数据
        $this->setCondition([
            ['tenant_id', '=', $tenantId],
            ['user_id', '=', $userId],
        ]);

        // 设置搜索条件
        if ($keyword !== '') {
            $this->setCondition([
                function($query) use ($keyword) {
                    $query->whereOr([
                        ['title', 'LIKE', "%{$keyword}%"],
                        //['description', 'LIKE', "%{$params['keyword']}%"],
                    ]);
                },
            ]);
        }

        // 添加默认排序条件到参数中，让 pageList 处理
        if (empty($params['prop'])) {
            $params['prop'] = 'operate_time';
        }

        if (empty($params['order'])) {
            $params['order'] = 'descending'; // 'descending' 对应 'desc'
        }

        // 复用通用分页方法
        return $this->pageList($params, $pageNo, $pageSize);
    }
}
