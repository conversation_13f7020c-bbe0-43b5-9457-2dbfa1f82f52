<?php
declare(strict_types = 1);

namespace app\tenant\model;

use think\model\relation\BelongsTo;

/**
 * 角色-设备关联模型类
 * <AUTHOR> <<EMAIL>>
 * @package app\tenant\controller
 */
class RoleDevice extends BaseModel
{
    protected $name = 'tenant_role_device';

    // 移除主键定义，使用复合主键
    protected $pk = ['tenant_id', 'role_id', 'device_id'];

    // 关闭自动维护时间戳
    protected $autoWriteTimestamp = false;

    /**
     * 定义与角色表的关联
     * @return \think\model\relation\BelongsTo
     */
    public function role(): BelongsTo
    {
        return $this->belongsTo(Role::class, 'role_id', 'id');
    }

    /**
     * 定义与设备表的关联
     * @return \think\model\relation\BelongsTo
     */
    public function device(): BelongsTo
    {
        return $this->belongsTo(Device::class, 'device_id', 'id');
    }
}
