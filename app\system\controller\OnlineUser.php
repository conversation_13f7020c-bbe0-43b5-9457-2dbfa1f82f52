<?php
declare(strict_types = 1);

namespace app\system\controller;

use app\system\service\OnlineUserService;
use think\Response;

/**
 * 在线用户控制器
 * <AUTHOR> <<EMAIL>>
 * @package app\system\controller
 */
class OnlineUser extends BaseController
{
    /**
     * 获取在线用户列表
     * @return \think\Response
     */
    public function list(): Response
    {
        $params = $this->request->only(['page', 'pageSize', 'keyword']);
        $params['page']     ??= 1;
        $params['pageSize'] ??= 20;

        // 获取在线用户列表(带分页)
        $result = OnlineUserService::getOnlineUsers($params);

        return $this->success($result);
    }

    /**
     * 踢出指定会话
     * @return \think\Response
     */
    public function kickOut(): Response
    {
        $item = $this->request->post('item');

        if (empty($item)) {
            return $this->error('参数错误');
        }

        // 解析用户ID和指纹ID
        list($userId, $fingerId) = explode(':', $item);

        if (!$userId || !$fingerId) {
            return $this->error('用户ID和指纹ID不能为空');
        }

        if (OnlineUserService::kickOutUser((int)$userId, $fingerId)) {
            // 记录操作日志
            $this->event_log('踢出用户', [
                'user_id'   => $userId,
                'finger_id' => $fingerId,
            ], []);

            return $this->success('操作成功');
        }

        return $this->error('操作失败');
    }

    /**
     * 拉黑用户
     * @return \think\Response
     */
    public function block(): Response
    {
        $item = $this->request->post('item');
        // 封禁时长
        $blockPeriod = $this->request->post('block_period', 0);

        if (empty($item)) {
            return $this->error('参数错误');
        }

        // 解析用户ID和指纹ID
        list($userId, $fingerId) = explode(':', $item);

        if (!$userId || !$fingerId) {
            return $this->error('用户ID和指纹ID不能为空');
        }

        $result = OnlineUserService::blacklistUser((int)$userId, $fingerId, (int)$blockPeriod);

        if (!$result['status']) {
            return $this->error($result['message']);
        }

        // 记录操作日志
        $this->event_log('拉黑用户', [
            'user_id'   => $userId,
            'finger_id' => $fingerId,
        ], []);

        return $this->success('操作成功');
    }

    /**
     * 批量踢出用户
     * @return \think\Response
     */
    public function batchKickOut(): Response
    {
        $items = $this->request->post('items/a');

        if (empty($items) || !is_array($items)) {
            return $this->error('请选择要踢出的用户');
        }

        $success = true;

        foreach ($items as $item) {
            list($userId, $fingerId) = explode(':', $item);

            if (!OnlineUserService::kickOutUser((int)$userId, $fingerId)) {
                $success = false;
            }
        }

        if ($success) {
            // 记录操作日志
            $this->event_log('批量踢出用户', ['items' => $items], []);

            return $this->success('操作成功');
        }

        return $this->error('部分操作失败');
    }

    /**
     * 批量拉黑用户
     * @return \think\Response
     */
    public function batchBlock(): Response
    {
        $items = $this->request->post('items/a');

        if (empty($items) || !is_array($items)) {
            return $this->error('请选择要拉黑的用户');
        }

        $success = true;
        $errors  = [];

        foreach ($items as $item) {
            list($userId, $fingerId) = explode(':', $item);

            $result = OnlineUserService::blacklistUser((int)$userId, $fingerId);

            if (!$result['status']) {
                $success  = false;
                $errors[] = sprintf('用户 %s 指纹 %s: %s', $userId, $fingerId, $result['message']);
            }
        }

        if ($success) {
            // 记录操作日志
            $this->event_log('批量拉黑用户', ['items' => $items], []);

            return $this->success('操作成功');
        }

        return $this->error('部分操作失败: ' . implode('; ', $errors));
    }

    /**
     * 获取黑名单列表
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return \think\Response
     */
    public function blackList(): Response
    {
        $params = $this->request->only(['page', 'pageSize', 'keyword']);
        $params['page']     ??= 1;
        $params['pageSize'] ??= 20;

        // 获取黑名单列表(带分页)
        $result = OnlineUserService::getBlacklistUsers($params);

        return $this->success($result);
    }

    /**
     * 移出黑名单
     * @return \think\Response
     */
    public function removeBlock(): Response
    {
        $item = $this->request->post('item');

        if (empty($item)) {
            return $this->error('参数错误');
        }

        // 解析用户ID和指纹ID
        list($userId, $fingerId) = explode(':', $item);

        if (!$userId || !$fingerId) {
            return $this->error('用户ID和指纹ID不能为空');
        }

        $result = OnlineUserService::unblacklistUser((int)$userId, $fingerId);

        if (!$result['status']) {
            return $this->error($result['message']);
        }

        // 记录操作日志
        $this->event_log('移出黑名单', [
            'user_id'   => $userId,
            'finger_id' => $fingerId,
        ], []);

        return $this->success('操作成功');
    }

    /**
     * 批量移出黑名单
     * @return \think\Response
     */
    public function batchRemoveBlock(): Response
    {
        $items = $this->request->post('items/a');

        if (empty($items) || !is_array($items)) {
            return $this->error('请选择要移出黑名单的用户');
        }

        $success = true;
        $errors  = [];

        foreach ($items as $item) {
            list($userId, $fingerId) = explode(':', $item);

            $result = OnlineUserService::unblacklistUser((int)$userId, $fingerId);

            if (!$result['status']) {
                $success  = false;
                $errors[] = sprintf('用户 %s 指纹 %s: %s', $userId, $fingerId, $result['message']);
            }
        }

        if ($success) {
            // 记录操作日志
            $this->event_log('批量移出黑名单', ['items' => $items], []);

            return $this->success('操作成功');
        }

        return $this->error('部分操作失败: ' . implode('; ', $errors));
    }
}
