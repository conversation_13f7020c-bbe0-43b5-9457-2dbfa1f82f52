<?php
declare(strict_types = 1);

namespace app\tenant\model;

/**
 * 租户用户模型类
 * <AUTHOR> <<EMAIL>>
 * @package app\tenant\model
 */
class User extends BaseModel
{
    protected $name = 'tenant_user';

    protected $pk = 'id';

    protected $createTime = 'create_at';

    protected $updateTime = 'update_at';

    // 字段类型
    protected $type = [
        'id'        => 'integer',
        'tenant_id' => 'integer',
        'founder'   => 'integer',
    ];

    // 上次登录时间获取器
    protected function getLastLoginTimeAttr($value): string
    {
        return !empty($value) ? date('Y-m-d H:i:s', $value) : '';
    }

    // 自动完成设置上次登录Ip
    protected function setLastLoginIpAttr($value): int
    {
        $ipLong = ip2long($value);

        return $ipLong !== false ? $ipLong : 0;
    }

    // 上次登录Ip获取器
    protected function getLastLoginIpAttr($value): string
    {
        return $value ? long2ip((int)$value) : '';
    }

    /**
     * 密码加密
     * @param $value
     * @return string
     */
    public function setPasswordAttr($value): string
    {
        return password_hash($value, PASSWORD_DEFAULT);
    }

    /**
     * 验证密码
     * @param $password
     * @return bool
     */
    public function verifyPassword($password): bool
    {
        if (!empty($this->password)) {
            return password_verify($password, $this->password);
        }

        return false;
    }
}
