<?php
declare(strict_types = 1);

namespace app\tenant\repository;

use app\common\repository\CommonRepository;
use app\tenant\model\MqttClientAuth as MqttClientAuthModel;
use app\tenant\model\MqttTopicAcl as MqttTopicAclModel;
use think\facade\Db;

/**
 * MQTT数据仓库类
 * 提供MQTT客户端认证和主题授权的数据访问方法
 * <AUTHOR> <<EMAIL>>
 * @package app\common\repository
 */
class MqttRepository extends CommonRepository
{
    /**
     * 获取客户端认证列表
     * @param array $params 查询参数
     * @param int $pageNo 页码
     * @param int $pageSize 每页数量
     * @param int $tenantId 租户ID，0表示全局配置
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return array
     */
    public function getClientAuthList(array $params = [], int $pageNo = 1, int $pageSize = 20, int $tenantId = 0): array
    {
        $query = MqttClientAuthModel::where([]);

        // 如果指定了租户ID，则只查询该租户的认证信息
        if ($tenantId > 0) {
            $query->where('tenant_id', $tenantId);
        }

        // 关键词搜索
        if (!empty($params['keyword'])) {
            $query->where(function($q) use ($params) {
                $q->whereOr([
                    ['client_id', 'like', "%{$params['keyword']}%"],
                    ['username', 'like', "%{$params['keyword']}%"],
                    ['description', 'like', "%{$params['keyword']}%"],
                ]);
            });
        }

        // 分页查询
        $total = $query->count();
        $list  = $query->page($pageNo, $pageSize)
            ->order('id', 'desc')
            ->select()
            ->toArray();

        return [
            'list'     => $list,
            'total'    => $total,
            'pageNo'   => $pageNo,
            'pageSize' => $pageSize,
        ];
    }

    /**
     * 获取主题授权规则列表
     * @param array $params 查询参数
     * @param int $pageNo 页码
     * @param int $pageSize 每页数量
     * @param int $tenantId 租户ID，0表示全局配置
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return array
     */
    public function getTopicAclList(array $params = [], int $pageNo = 1, int $pageSize = 20, int $tenantId = 0): array
    {
        $query = MqttTopicAclModel::where([]);

        // 如果指定了租户ID，则只查询该租户的授权规则
        if ($tenantId > 0) {
            $query->where('tenant_id', $tenantId);
        }

        // 关键词搜索
        if (!empty($params['keyword'])) {
            $query->where(function($q) use ($params) {
                $q->whereOr([
                    ['client_id', 'like', "%{$params['keyword']}%"],
                    ['username', 'like', "%{$params['keyword']}%"],
                    ['topic', 'like', "%{$params['keyword']}%"],
                    ['description', 'like', "%{$params['keyword']}%"],
                ]);
            });
        }

        // 分页查询
        $total = $query->count();
        $list  = $query->page($pageNo, $pageSize)
            ->order('priority', 'desc')
            ->select()
            ->toArray();

        return [
            'list'     => $list,
            'total'    => $total,
            'pageNo'   => $pageNo,
            'pageSize' => $pageSize,
        ];
    }

    /**
     * 添加客户端认证
     * @param array $data 认证数据
     * @return bool|int 成功返回ID，失败返回false
     */
    public function addClientAuth(array $data): bool|int
    {
        $auth = new MqttClientAuthModel();

        $auth->tenant_id    = $data['tenant_id'] ?? 0;
        $auth->client_id    = $data['client_id'];
        $auth->username     = $data['username'];
        $auth->password     = $data['password'];
        $auth->is_superuser = $data['is_superuser'] ?? 0;
        $auth->status       = $data['status']       ?? 1;
        $auth->description  = $data['description']  ?? '';

        if ($auth->save()) {
            return $auth->id;
        }

        return false;
    }

    /**
     * 添加主题授权规则
     * @param array $data 授权数据
     * @return bool|int 成功返回ID，失败返回false
     */
    public function addTopicAcl(array $data): bool|int
    {
        $acl = new MqttTopicAclModel();

        $acl->tenant_id   = $data['tenant_id'] ?? 0;
        $acl->client_id   = $data['client_id'] ?? null;
        $acl->username    = $data['username']  ?? null;
        $acl->topic       = $data['topic'];
        $acl->action      = $data['action'];
        $acl->permission  = $data['permission'];
        $acl->qos         = $data['qos']         ?? -1;
        $acl->retain      = $data['retain']      ?? false;
        $acl->priority    = $data['priority']    ?? 0;
        $acl->status      = $data['status']      ?? 1;
        $acl->description = $data['description'] ?? '';

        if ($acl->save()) {
            return $acl->id;
        }

        return false;
    }

    /**
     * 批量设置租户的主题授权规则
     * @param int $tenantId 租户ID
     * @param array $rules 规则列表
     * @return bool
     */
    public function batchSetTenantAcl(int $tenantId, array $rules): bool
    {
        // 开启事务
        Db::startTrans();

        try {
            // 删除该租户的所有现有规则
            MqttTopicAclModel::where('tenant_id', $tenantId)->delete();

            // 添加新规则
            $newRules = [];

            foreach ($rules as $rule) {
                if (empty($rule['topic']) || empty($rule['action']) || empty($rule['permission'])) {
                    continue;
                }

                $newRules[] = [
                    'tenant_id'   => $tenantId,
                    'client_id'   => $rule['client_id'] ?? null,
                    'topic'       => $rule['topic'],
                    'action'      => $rule['action'],
                    'permission'  => $rule['permission'],
                    'qos'         => $rule['qos']         ?? -1,
                    'retain'      => $rule['retain']      ?? false,
                    'priority'    => $rule['priority']    ?? 0,
                    'status'      => $rule['status']      ?? 1,
                    'description' => $rule['description'] ?? '',
                    'create_at'   => time(),
                    'update_at'   => time(),
                ];
            }

            if (!empty($newRules)) {
                (new MqttTopicAclModel())->saveAll($newRules);
            }

            // 提交事务
            Db::commit();

            return true;
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();

            return false;
        }
    }
}
