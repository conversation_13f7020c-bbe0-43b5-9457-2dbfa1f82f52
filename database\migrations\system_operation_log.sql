-- 表结构: system_operation_log
CREATE TABLE `system_operation_log` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '管理员id',
  `device_id` varchar(32) NOT NULL DEFAULT '' COMMENT '设备ID',
  `request_id` varchar(50) NOT NULL DEFAULT '' COMMENT '请求ID',
  `event` varchar(255) NOT NULL DEFAULT '' COMMENT '操作事件',
  `path` varchar(255) NOT NULL DEFAULT '' COMMENT '路由地址',
  `params` text COMMENT '携带参数',
  `description` text COMMENT '记录详情',
  `user_agent` varchar(255) NOT NULL COMMENT '浏览器信息',
  `client_ip` bigint(20) NOT NULL DEFAULT '0' COMMENT '操作者ip',
  `mem` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '内存消耗',
  `reqs` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '吞吐量',
  `runtime` decimal(10,6) unsigned NOT NULL DEFAULT '0.000000' COMMENT '响应时间',
  `create_at` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_user_device` (`user_id`,`device_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='系统操作日志表';
