-- 表结构: tenant_login_log
CREATE TABLE `tenant_login_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户ID',
  `user_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '用户ID',
  `username` varchar(30) NOT NULL DEFAULT '' COMMENT '用户账号',
  `login_type` varchar(20) NOT NULL DEFAULT '' COMMENT '登录方式（account-账号密码,mobile-手机验证码）',
  `title` varchar(255) NOT NULL DEFAULT '' COMMENT '登录消息',
  `ip` varchar(45) NOT NULL DEFAULT '' COMMENT '登录IP',
  `user_agent` varchar(255) NOT NULL DEFAULT '' COMMENT '浏览器信息',
  `request_id` varchar(100) NOT NULL DEFAULT '' COMMENT '请求Id',
  `client_id` varchar(100) NOT NULL DEFAULT '' COMMENT '客户端ID',
  `device_id` varchar(100) NOT NULL DEFAULT '' COMMENT '设备ID',
  `login_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '登录时间',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '登录状态：1成功 0失败',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_tenant_user` (`tenant_id`,`user_id`) USING BTREE,
  KEY `idx_login_time` (`login_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='租户用户登录日志表';
