```sql
-- ----------------------------
-- Table structure for helio_system_app
-- ----------------------------
DROP TABLE IF EXISTS `helio_system_app`;
CREATE TABLE `helio_system_app` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '应用ID',
  `name` varchar(50) NOT NULL DEFAULT '' COMMENT '应用名称',
  `code` varchar(50) NOT NULL DEFAULT '' COMMENT '应用标识',
  `version` varchar(20) NOT NULL DEFAULT '' COMMENT '版本号',
  `description` varchar(255) NOT NULL DEFAULT '' COMMENT '应用描述',
  `icon` varchar(100) NOT NULL DEFAULT '' COMMENT '应用图标',
  `author` varchar(50) NOT NULL DEFAULT '' COMMENT '作者',
  `website` varchar(100) NOT NULL DEFAULT '' COMMENT '官网',
  `menu_id` int(11) NOT NULL DEFAULT 0 COMMENT '关联的顶级菜单ID',
  `config` text COMMENT '应用配置JSON',
  `is_system` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否系统应用(1是 0否)',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态(1:启用,0:禁用)',
  `create_at` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_at` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_code` (`code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统应用表';

-- ----------------------------
-- Table structure for helio_system_app_install
-- ----------------------------
DROP TABLE IF EXISTS `helio_system_app_install`;
CREATE TABLE `helio_system_app_install` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `app_id` int(11) NOT NULL DEFAULT 0 COMMENT '应用ID',
  `tenant_id` int(11) NOT NULL DEFAULT 0 COMMENT '租户ID',
  `config` text COMMENT '应用配置JSON',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态(1:启用,0:禁用)',
  `expire_time` int(11) NOT NULL DEFAULT 0 COMMENT '过期时间',
  `create_at` int(11) NOT NULL DEFAULT 0 COMMENT '安装时间',
  `update_at` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_app_tenant` (`app_id`,`tenant_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='应用安装表';
```

同时需要修改权限表:

```sql
-- 在 helio_system_permission 表中添加字段
ALTER TABLE `helio_system_permission` 
ADD COLUMN `app_code` varchar(50) NOT NULL DEFAULT '' COMMENT '所属应用标识',
ADD COLUMN `is_system` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否系统权限(1是 0否)';
```

主要功能设计:

1. 应用管理
```
- 应用列表
- 应用安装/卸载
- 应用启用/禁用 
- 应用配置管理
- 应用版本管理
```

2. 应用开发规范
```
app/
  ├── demo/                    # 示例应用
  │   ├── controller/         # 控制器
  │   ├── model/             # 模型
  │   ├── view/              # 视图
  │   ├── config/            # 配置文件
  │   │   ├── menu.php      # 菜单配置
  │   │   └── info.php      # 应用信息
  │   ├── database/          # 数据库文件
  │   │   ├── install.sql   # 安装SQL
  │   │   └── uninstall.sql # 卸载SQL
  │   └── Demo.php          # 应用主类
```

3. 应用生命周期管理
```php
interface AppInterface {
    // 安装
    public function install();
    
    // 卸载
    public function uninstall();
    
    // 启用
    public function enable();
    
    // 禁用 
    public function disable();
    
    // 升级
    public function upgrade($version);
}
```

4. 权限管理
- 应用安装时自动注册菜单和权限
- 支持按租户分别启用/禁用应用功能
- 可以针对不同租户单独配置应用权限

5. 数据隔离
- 应用表需要添加 tenant_id 字段
- 查询时自动携带租户条件
- 支持跨租户数据共享配置

6. 扩展机制
```php
// 事件钩子
class AppEvents {
    const BEFORE_INSTALL = 'app.before_install';
    const AFTER_INSTALL = 'app.after_install';
    const BEFORE_UNINSTALL = 'app.before_uninstall';
    const AFTER_UNINSTALL = 'app.after_uninstall';
}

// 依赖注入
class AppProvider extends ServiceProvider {
    public function register() {
        // 注册服务
    }
}
```

使用示例:

1. 创建新应用
```php
// app/demo/config/info.php
return [
    'name' => '示例应用',
    'code' => 'demo',
    'version' => '1.0.0',
    'description' => '这是一个示例应用',
    'author' => 'Demo',
    'website' => 'https://demo.com'
];

// app/demo/config/menu.php
return [
    [
        'name' => 'demo',
        'title' => '示例应用',
        'icon' => 'demo-icon',
        'sort' => 0,
        'children' => [
            [
                'name' => 'demo.list',
                'title' => '示例列表',
                'component' => 'demo/list'
            ]
        ]
    ]
];
```

2. 安装应用
```php
$app = new DemoApp();
$app->install();

// 自动创建菜单
// 自动注册权限
// 执行数据库脚本
// 生成配置文件
```

这样的设计可以:

1. 支持应用的插件化管理
2. 实现应用的标准化开发
3. 支持租户级别的应用管理
4. 提供完整的应用生命周期
5. 灵活的扩展机制

您觉得这个设计方案如何?如果有任何疑问或建议我很乐意继续讨论。
