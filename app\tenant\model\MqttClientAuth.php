<?php
declare(strict_types = 1);

namespace app\tenant\model;

/**
 * MQTT客户端认证模型
 * <AUTHOR> <<EMAIL>>
 * @package app\common\model
 */
class MqttClientAuth extends BaseModel
{
    /**
     * 表名
     * @var string
     */
    protected $name = 'mqtt_client_auth';

    /**
     * 允许写入的字段
     * @var array
     */
    protected $field = [
        'tenant_id',
        'client_id',
        'username',
        'password',
        'is_superuser',
        'status',
        'description',
    ];

    /**
     * 根据客户端ID和租户ID查找认证信息
     * @param string $clientId 客户端ID
     * @param int $tenantId 租户ID
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return array|null 认证信息
     */
    public static function findByClientId(string $clientId, int $tenantId = 0): ?array
    {
        $auth = self::where([
            ['client_id', '=', $clientId],
            ['tenant_id', '=', $tenantId],
            ['status', '=', 1],
        ])->find();

        return $auth ? $auth->toArray() : null;
    }

    /**
     * 根据用户名查找认证信息
     * @param string $username 用户名
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return array|null 认证信息
     */
    public static function findByUsername(string $username): ?array
    {
        $auth = self::where([
            ['username', '=', $username],
            ['status', '=', 1],
        ])->find();

        return $auth ? $auth->toArray() : null;
    }
}
