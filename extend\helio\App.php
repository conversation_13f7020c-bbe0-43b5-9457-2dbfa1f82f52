<?php
declare(strict_types = 1);

namespace helio;

abstract class App implements AppInterface
{
    protected array $info = [];

    protected array $config = [];

    public function __construct()
    {
        // 加载应用信息
        $this->info = $this->getInfo();
        // 加载应用配置
        $this->config = $this->getConfig();
    }

    abstract public function install();

    abstract public function uninstall();

    abstract public function enable();

    abstract public function disable();

    abstract public function upgrade($version);

    // 获取应用信息
    public function getInfo()
    {
        $class = new \ReflectionClass($this);
        $path  = dirname($class->getFileName());
        $file  = $path . '/config/info.php';

        return include $file;
    }

    // 获取应用配置
    public function getConfig()
    {
        $class = new \ReflectionClass($this);
        $path  = dirname($class->getFileName());
        $file  = $path . '/config/config.php';

        return file_exists($file) ? include $file : [];
    }
}
