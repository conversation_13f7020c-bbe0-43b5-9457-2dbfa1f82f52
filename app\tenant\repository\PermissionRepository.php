<?php
namespace app\tenant\repository;

use app\tenant\model\Permission as PermissionModel;
use app\tenant\model\PermissionApi as PermissionApiModel;
use app\tenant\model\PermissionIdentity as PermissionIdentityModel;
use app\tenant\service\MenuService;
use owns\library\NodeTree;
use think\facade\Cache;

/**
 * 权限节点数据仓库类
 * 负责系统权限节点相关的数据访问和操作
 * <AUTHOR> <<EMAIL>>
 * @package app\tenant\repository
 */
class PermissionRepository extends BaseRepository
{
    /**
     * 缓存前缀
     */
    public const CACHE_PREFIX = 'tenant:permission:';

    /**
     * 构造方法
     */
    public function __construct()
    {
        // 指定当前模型类名而非实例，避免条件累积问题
        parent::__construct(PermissionModel::class);
    }

    /**
     * 获取权限节点树
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return array
     */
    public function getPermissionTree(): array
    {
        $list = $this->model->getRulelist();

        foreach ($list as $key => $value) {
            $list[$key]['status_cn'] = $this->getStatusText($value['status']);
        }

        return (new NodeTree())->toLayer($list);
    }

    /**
     * 获取权限节点列表
     * @param array $params 查询参数
     * @param bool $treeMode 是否显示tree格式
     * @throws \Throwable
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return array
     */
    public function getPermissionList(array $params, bool $treeMode = true): array
    {
        $map = [];

        $allow_fields = [
            'id', 'pid', 'title', 'name', 'path', 'redirect', 'component',
            'active', 'icon', 'color', 'type', 'is_show', 'fullpage', 'tag',
            'create_at', 'update_at', 'sort', 'status',
        ];

        if (!empty($params['path'])) {
            $map[] = ['path', 'LIKE', '%' . trim($params['path']) . '%'];
        }

        if (!empty($params['title'])) {
            $map[] = ['title', 'LIKE', '%' . trim($params['title']) . '%'];
        }

        if (!empty($params['type'])) {
            $map[] = ['type', '=', trim($params['type'])];
        }

        if (isset($params['is_show'])) {
            $map[] = ['is_show', '=', intval($params['is_show'])];
        }

        if (isset($params['status'])) {
            $map[] = ['status', '=', intval($params['status'])];
        } else {
            $map[] = ['status', '>=', 0];
        }

        $list = $this->model->where($map)
            ->order('sort', 'asc')
            ->order('id', 'asc')
            ->field($allow_fields)
            ->select()
            ->toArray();

        foreach ($list as $key => $value) {
            $list[$key]['status_cn'] = $this->getStatusText($value['status']);
            $list[$key]['apiList']   = (new PermissionApiRepository())->getApiList($value['id']);
        }

        if ($treeMode) {
            $list = (new NodeTree())->toLayer($list);
        }

        return $list;
    }

    /**
     * 获取用户权限菜单数据
     * @param $user
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return array
     */
    public function getUserPermissionMenu($user): array
    {
        $result = [
            'menu_data'       => [],
            'permission_data' => [],
        ];

        $menu_list = [];

        if (isset($user['founder'])) {
            // 获取菜单数据，超管时为所有状态正常的菜单列表
            if ($user['founder'] === 1) {
                // 获取所有状态正常的菜单列表
                $menu_list = PermissionModel::getRulelist();
            } else {
                // 获取当前用户身份对应的所有节点权限id列表
                $permissionIdsArr = PermissionIdentityModel::where('identity', 'device_admin')->column('permission_id');

                if ($permissionIdsArr) {
                    // 获取当前用户身份对应的所有节点权限列表
                    $menu_list = PermissionModel::where([
                        ['id', 'IN', $permissionIdsArr],
                        ['status', '=', 1],
                    ])->select()->toArray();
                }
            }
        }

        if (!empty($menu_list)) {
            // 菜单标准输出
            $menu_data       = MenuService::format($menu_list);
            $permission_data = MenuService::permissions($menu_list);

            $result = [
                'menu_data'       => $menu_data,
                'permission_data' => $permission_data,
            ];
        }

        return $result;
    }

    /**
     * 获取权限节点信息
     * @param int $id
     * @return array
     */
    public function getPermissionInfo(int $id): array
    {
        return $this->getInfo($id) ?: [];
    }

    /**
     * 获取树形结构
     * @throws \Throwable
     * @return array
     */
    public function getTree(): array
    {
        $cacheKey = self::CACHE_PREFIX . 'tree:all';

        return Cache::remember($cacheKey, function() {
            $query = $this->model->where('status', '>=', 0);

            $list = $query->order('sort', 'asc')
                ->select()
                ->toArray();

            return (new NodeTree([
                'id'    => 'id',
                'pid'   => 'pid',
                'title' => 'title',
                'child' => 'children',
            ]))->toLayer($list);
        }, 3600);
    }

    /**
     * 获取用户的权限节点菜单
     * @param array $userInfo
     * @param array $userNodes
     * @throws \Throwable
     * @return array
     */
    public function getUserMenus(array $userInfo, array $userNodes): array
    {
        $cacheKey = self::CACHE_PREFIX . 'user_menus:' . $userInfo['id'];

        return Cache::remember($cacheKey, function() use ($userInfo, $userNodes) {
            $data      = [];
            $userRules = $this->getUserRules($userInfo, $userNodes);

            foreach ($userRules as $value) {
                if ($value['type'] == 1) {
                    $data[] = $value;
                }
            }

            return $data;
        }, 3600);
    }

    /**
     * 获取用户的所有菜单节点规则列表
     * @param array $userInfo
     * @param array $userNodes
     * @throws \Throwable
     * @return array
     */
    public function getUserRules(array $userInfo, array $userNodes): array
    {
        $cacheKey = self::CACHE_PREFIX . 'user_rules:' . $userInfo['id'];

        return Cache::remember($cacheKey, function() use ($userInfo, $userNodes) {
            $map[] = ['status', '=', 1];

            if ($userInfo['is_super'] == 0) {
                $map[] = ['id', 'IN', $userNodes];
            }

            return $this->model->where($map)
                ->order('sort', 'asc')
                ->select()
                ->toArray();
        }, 3600);
    }

    /**
     * 获取用户的所有权限节点API接口列表
     * @param array $userInfo
     * @param array $userNodes
     * @throws \Throwable
     * @return array
     */
    public function getUserAuthApiList(array $userInfo, array $userNodes): array
    {
        $cacheKey = self::CACHE_PREFIX . 'user_api_list:' . $userInfo['id'];

        return Cache::remember($cacheKey, function() use ($userInfo, $userNodes) {
            $map[] = ['status', '=', 1];

            if ($userInfo['is_super'] == 0) {
                $map[] = ['id', 'IN', $userNodes];
            }

            // 查询出当前用户的权限菜单
            $list = $this->model->where($map)->order('sort', 'asc')->select()->toArray();

            if (!empty($list)) {
                // 查询出权限节点对应的接口列表
                return PermissionApiModel::where([['permission_id', 'IN', $userNodes]])->select()->toArray();
            }

            return [];
        }, 3600);
    }

    /**
     * 获取指定id的子数据集，包括当前
     * @param array $src_ids
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return array
     */
    private function getChildIds(array $src_ids = []): array
    {
        $ret_ids = $src_ids;
        $list    = $this->model->where([['status', '>=', 0]])
            ->field(['id','pid','name'])
            ->select()
            ->toArray();

        $nodeTree = new NodeTree([
            'id'    => 'id',
            'pid'   => 'pid',
            'title' => 'name',
            'child' => 'children',
        ]);

        if (!empty($list)) {
            foreach ($src_ids as $id) {
                $child_ids_arr = $nodeTree->getChildIds($list, $id);
                $ret_ids       = array_merge($ret_ids, $child_ids_arr);
            }
        }

        $array_map = array_map('intval', array_unique($ret_ids));
        sort($array_map);

        return $array_map;
    }

    /**
     * 添加权限节点
     * @param array $data
     * @return int|bool
     */
    public function addPermission(array $data): int|bool
    {
        return $this->model->addPermission($data);
    }

    /**
     * 编辑权限节点
     * @param int $id
     * @param array $data
     * @param array $apiList
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \Exception
     * @return bool
     */
    public function editPermission(int $id, array $data, array $apiList = []): bool
    {
        // 获取节点信息
        $info = $this->getInfo($id);

        if (empty($info)) {
            throw new \Exception('节点不存在');
        }

        if ($info['status'] == -1) {
            throw new \Exception('该节点已删除，无法操作');
        }

        $result = $this->updateData($id, $data);

        if ($result) {
            // 更新成功后写入权限接口
            (new PermissionApiRepository())->saveApiList($id, $apiList);
        }

        return $result;
    }

    /**
     * 设置节点状态
     * @param mixed $ids
     * @param int $status
     * @return bool
     */
    public function setStatus(mixed $ids, int $status): bool
    {
        return $this->model->setStatus($ids, $status);
    }
}
