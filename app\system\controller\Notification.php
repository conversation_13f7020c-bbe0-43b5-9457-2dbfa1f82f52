<?php
declare(strict_types = 1);

namespace app\system\controller;

use app\notification\repository\NotificationRepository;
use app\notification\traits\NotificationTrait;
use app\system\repository\DepartmentRepository;
use app\system\repository\UserRepository;
use owns\library\NodeTree;
use think\App;
use think\Request;
use think\Response;

/**
 * 消息通知控制器
 * 相关服务：
 * 启动消息通知分发任务：php think queue:work --queue notification
 * 后面需要将{同步计数到数据库（syncCountsToDB）做成任务放到计划任务或者消息队列中}
 * <AUTHOR> <<EMAIL>>
 * @package app\system\controller
 */
class Notification extends BaseController
{
    use NotificationTrait;

    /**
     * @var NotificationRepository
     */
    protected NotificationRepository $repository;

    public function __construct(App $app, NotificationRepository $repository)
    {
        parent::__construct($app);
        $this->repository = $repository;
    }

    /**
     * 获取当前用户ID
     * @return int
     */
    protected function getCurrentUserId(): int
    {
        return $this->getUserId();
    }

    /**
     * 获取接收者列表
     * @return \think\Response
     */
    public function receivers(): Response
    {
        $users = (new UserRepository())->getActiveUsers();

        return $this->success($users);
    }

    /**
     * 获取部门列表
     * @return \think\Response
     */
    public function departments(): Response
    {
        $departments = (new DepartmentRepository())->getActiveDepartments();

        $nodeTree = new NodeTree([
            'id'    => 'id', // id名称
            'pid'   => 'pid', // pid名称
            'title' => 'name', // 标题名称
            'child' => 'children', // 子元素键名
        ]);

        $departments = $nodeTree->toLayer($departments);

        return $this->success($departments);
    }

    /**
     * 获取已发送消息列表
     * @param \think\Request $request
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return \think\Response
     */
    public function sentList(Request $request): Response
    {
        $params = $request->param();
        $result = $this->repository->getSentList($this->getUserId(), $params);

        return $this->success($result);
    }
}
