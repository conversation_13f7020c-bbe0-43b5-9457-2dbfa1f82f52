<?php
declare(strict_types = 1);

namespace app\notification\traits;

use app\notification\model\Notification as NotificationModel;
use app\notification\repository\NotificationRepository;
use app\notification\service\NotificationCounterService;
use app\notification\validate\Notification as NotificationValidate;
use think\Request;
use think\Response;

/**
 * 消息通知Trait
 * 提供基础的消息通知功能，使用该trait的类需要实现getCurrentUserId方法
 * <AUTHOR> <<EMAIL>>
 * @package app\notification\traits
 */
trait NotificationTrait
{
    /**
     * @var NotificationRepository
     */
    protected NotificationRepository $repository;

    /**
     * 获取当前用户ID
     * 使用该trait的类必须实现此方法以提供正确的用户ID
     * @return int
     */
    abstract protected function getCurrentUserId(): int;

    /**
     * 获取当前租户ID
     * 默认返回0，在tenant模块中可以重写此方法返回当前租户ID
     * @return int
     */
    protected function getTenantId(): int
    {
        return 0;
    }

    /**
     * 获取消息列表
     * @param \think\Request $request
     * @return \think\Response
     */
    public function list(Request $request): Response
    {
        $validate = new NotificationValidate();
        $params   = $request->param();

        if (!$validate->scene('list')->check($params)) {
            return $this->error($validate->getError());
        }

        $result = $this->repository->getReceivedList($this->getCurrentUserId(), $params);

        return $this->success($result);
    }

    /**
     * 获取消息详情
     * @param \think\Request $request
     * @return \think\Response
     */
    public function detail(Request $request): Response
    {
        $validate = new NotificationValidate();
        $data     = $request->param();

        if (!$validate->scene('read')->check($data)) {
            return $this->error($validate->getError());
        }

        try {
            $detail = $this->repository->getNotificationInfo($data['id']);

            if (!$detail) {
                return $this->error('消息不存在');
            }

            return $this->success($detail);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 标记消息状态（已读/未读）
     * @param \think\Request $request
     * @return \think\Response
     */
    public function status(Request $request): Response
    {
        $validate = new NotificationValidate();
        $data     = $request->param();

        $data['id'] = $request->param('id', 0, 'intval');
        // 增加默认值，兼容验证器
        $data['action'] = $request->param('action', 'read');

        if (!$validate->scene('status')->check($data)) {
            return $this->error($validate->getError());
        }
        $result = $this->repository->changeReadStatus($data['id'], $this->getCurrentUserId(), $data['action']);

        if ($result['status']) {
            return $this->success($result['message']);
        }

        return $this->error($result['message']);
    }

    /**
     * 消息互动
     * @param \think\Request $request
     * @return \think\Response
     */
    public function interact(Request $request): Response
    {
        $validate = new NotificationValidate();
        $data     = $request->param();

        if (!$validate->scene('interact')->check($data)) {
            return $this->error($validate->getError());
        }

        $notificationId = (int)$data['id'];
        // 修改参数映射：like=1(点赞), dislike=-1(踩), cancel=0(取消)
        $type = $data['action'] === 'like' ? 1 : ($data['action'] === 'dislike' ? -1 : 0);

        $result = $this->repository->interact($notificationId, $this->getCurrentUserId(), $type);

        if ($result['status']) {
            return $this->success($result['message']);
        }

        return $this->error($result['message']);
    }

    /**
     * 获取未读消息数
     * @return \think\Response
     */
    public function unreadCount(): Response
    {
        $count = $this->repository->getUnreadCount($this->getCurrentUserId());

        return $this->success(['count' => $count]);
    }

    /**
     * 获取消息分类
     * @return \think\Response
     */
    public function categories(): Response
    {
        $categories = [
            ['id' => NotificationModel::TYPE_SYSTEM, 'name' => '系统消息'],
            ['id' => NotificationModel::TYPE_NOTICE, 'name' => '公告通知'],
            ['id' => NotificationModel::TYPE_MESSAGE, 'name' => '私信'],
        ];

        return $this->success($categories);
    }

    /**
     * 发送消息
     * @param \think\Request $request
     * @return \think\Response
     */
    public function send(Request $request): Response
    {
        $validate = new NotificationValidate();
        $data     = $request->param();

        // 确保 target_ids 是数组
        $data['target_ids'] = empty($data['target_ids']) ? [] : (is_array($data['target_ids']) ? $data['target_ids'] : json_decode($data['target_ids'], true));

        if (!$validate->scene('send')->check($data)) {
            return $this->error($validate->getError());
        }
        $result = $this->repository->send($data, $this->getCurrentUserId());

        if ($result['status'] === 1) {
            return $this->success();
        }

        return $this->error($result['message']);
    }

    /**
     * 删除接收到的消息
     * @param \think\Request $request
     * @return \think\Response
     */
    public function delete(Request $request): Response
    {
        $validate = new NotificationValidate();
        $data     = ['id' => $request->param('id')];

        if (!$validate->scene('delete')->check($data)) {
            return $this->error($validate->getError());
        }

        // 使用仓库方法进行软删除
        $result = $this->repository->softDelete($data['id'], $this->getCurrentUserId());

        if ($result) {
            return $this->success();
        }

        return $this->error('删除失败');
    }

    /**
     * 批量标记已读/未读
     * @param \think\Request $request
     * @return \think\Response
     */
    public function batchStatus(Request $request): Response
    {
        $validate = new NotificationValidate();
        $params   = [
            'ids'    => $request->param('ids'),
            'action' => $request->param('action', 'read'),
        ];

        if (!$validate->scene('batchStatus')->check($params)) {
            return $this->error($validate->getError());
        }

        $result = $params['action'] === 'read' ? $this->repository->batchMarkRead(
            $params['ids'],
            $this->getCurrentUserId()
        ) : $this->repository->batchMarkUnread($params['ids'], $this->getCurrentUserId());

        if ($result['status']) {
            return $this->success($result['message']);
        }

        return $this->error($result['message']);
    }

    /**
     * 批量删除消息
     * @param \think\Request $request
     * @return \think\Response
     */
    public function batchDelete(Request $request): Response
    {
        $validate = new NotificationValidate();
        $data     = ['ids' => $request->param('ids')];

        if (!$validate->scene('batchDelete')->check($data)) {
            return $this->error($validate->getError());
        }

        if ($this->repository->batchSoftDelete($data['ids'], $this->getCurrentUserId())) {
            return $this->success('删除成功');
        }

        return $this->error('删除失败');
    }

    /**
     * 全部标记已读
     * @return \think\Response
     */
    public function markAllRead(): Response
    {
        $result = $this->repository->markAllRead($this->getCurrentUserId());

        if ($result['status']) {
            return $this->success($result['message']);
        }

        return $this->error($result['message']);
    }

    /**
     * 获取消息互动状态和计数
     * @param \think\Request $request
     * @throws \Exception
     * @return \think\Response
     */
    public function interactStatus(Request $request): Response
    {
        $validate = new NotificationValidate();
        $data     = ['id' => $request->param('id', 0, 'intval')];

        if (!$validate->scene('read')->check($data)) {
            return $this->error($validate->getError());
        }

        // 获取当前用户的互动状态
        $interactStatus = $this->repository->getUserInterAction($data['id'], $this->getCurrentUserId());

        if (!$interactStatus['status']) {
            return $this->error($interactStatus['message'] ?? '获取互动状态失败');
        }

        // 获取实时计数
        $counts = (new NotificationCounterService())->getRealTimeCount($data['id']);

        return $this->success([
            'interact_status' => $interactStatus['data']  ?? 0,  // 1=已点赞 -1=已踩 0=未操作
            'like_count'      => $counts['like_count']    ?? 0,
            'dislike_count'   => $counts['dislike_count'] ?? 0,
        ]);
    }
}
