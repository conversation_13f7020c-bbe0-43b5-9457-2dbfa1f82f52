<?php
declare(strict_types = 1);

namespace app\tenant\service;

use owns\library\NodeTree;

/**
 * 菜单节点服务类
 * <AUTHOR> <<EMAIL>>
 * @package app\tenant\service
 */
class MenuService extends BaseService
{
    /**
     * 菜单标准输出
     * @param array $list
     * @return array
     */
    public static function format(array $list = []): array
    {
        if ($list) {
            $data = [];

            foreach ($list as $key => $value) {
                $data[$key]['id']                       = $value['id'];
                $data[$key]['pid']                      = $value['pid'];
                $data[$key]['name']                     = $value['name'];
                $data[$key]['path']                     = $value['path'];
                $data[$key]['component']                = $value['component'];
                $data[$key]['redirect']                 = $value['redirect'];
                $data[$key]['meta']['title']            = $value['title'];
                $data[$key]['meta']['type']             = $value['type'];
                $data[$key]['meta']['hidden']           = !$value['is_show'];
                $data[$key]['meta']['icon']             = $value['icon'];
                $data[$key]['meta']['active']           = $value['active']   ?? '';
                $data[$key]['meta']['color']            = $value['color']    ?? '';
                $data[$key]['meta']['fullpage']         = $value['fullpage'] ?? 0;
                $data[$key]['meta']['affix']            = (bool)$value['affix'];
                $data[$key]['meta']['fullscreen']       = (bool)$value['fullscreen'];
                $data[$key]['meta']['minimizable']      = (bool)$value['minimizable'];
                $data[$key]['meta']['notOnTab']         = (bool)$value['not_on_tab'];
                $data[$key]['meta']['notKeepAlive']     = (bool)$value['not_keepalive'];
                $data[$key]['meta']['hiddenBreadcrumb'] = (bool)($value['hiddenBreadcrumb'] ?? 0);
            }

            // 将数据层级化
            $data = NodeTree::toLayer($data);

            // 层级菜单卸载处理不必要的字段
            $layer_menus = self::layerUnsetField($data);
        } else {
            $layer_menus = [];
        }

        return $layer_menus;
    }

    /**
     * 获取用户权限节点的别名数据集
     * @param array $list
     * @return array
     */
    public static function permissions(array $list = []): array
    {
        // 必须重建索引，否则前端报错
        return array_values(array_unique(array_column($list, 'name')));
    }

    /**
     * 层级菜单卸载处理
     * @param array $layer
     * @return array
     */
    private static function layerUnsetField(array $layer = []): array
    {
        foreach ($layer as $k => &$item) {
            // 不需要id
            if (isset($item['id'])) {
                unset($item['id']);
            }

            // 不需要pid
            if (isset($item['pid'])) {
                unset($item['pid']);
            }

            // redirect为空时不需要
            if (isset($item['redirect']) && empty($item['redirect'])) {
                unset($item['redirect']);
            }

            // component为空时不需要
            if (isset($item['component']) && empty($item['component'])) {
                unset($item['component']);
            }

            // hidden为空时不需要
            if (isset($item['meta']['hidden']) && $item['meta']['hidden'] === false) {
                unset($item['meta']['hidden']);
            }

            // icon为空时不需要
            if (isset($item['meta']['icon']) && empty($item['meta']['icon'])) {
                unset($item['meta']['icon']);
            }

            // affix为空时不需要
            if (isset($item['meta']['affix']) && $item['meta']['affix'] === false) {
                unset($item['meta']['affix']);
            }

            // hiddenBreadcrumb为空时不需要
            if (isset($item['meta']['hiddenBreadcrumb']) && $item['meta']['hiddenBreadcrumb'] === 0) {
                unset($item['meta']['hiddenBreadcrumb']);
            }

            // active为空时不需要
            if (isset($item['meta']['active']) && empty($item['meta']['active'])) {
                unset($item['meta']['active']);
            }

            // color为空时不需要
            if (isset($item['meta']['color']) && empty($item['meta']['color'])) {
                unset($item['meta']['color']);
            }

            // fullpage为空时不需要
            if (isset($item['meta']['fullpage']) && $item['meta']['fullpage'] === 0) {
                unset($item['meta']['fullpage']);
            }

            // 处理菜单类型type
            if (isset($item['meta']['type'])) {
                // 删除按钮，接口
                if (in_array($item['meta']['type'], ['node', 'button'])) {
                    unset($layer[$k]);
                }
            }

            if (!empty($item['children'])) {
                $item['children'] = self::layerUnsetField($item['children']);
            }
        }

        // 层级菜单卸载空children
        foreach ($layer as $key => $value) {
            if (isset($value['children']) && empty($value['children'])) {
                unset($layer[$key]['children']);
            }
        }

        return $layer;
    }
}
