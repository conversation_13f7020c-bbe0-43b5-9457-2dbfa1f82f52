<?php
declare(strict_types = 1);
namespace app\common\model;

use app\common\traits\DataStatusTrait;
use app\common\traits\ModelTrait;
use think\db\Query;
use think\Model;

/**
 * 全局模型基类
 * <AUTHOR> <<EMAIL>>
 * @package app\common\model
 */
class CommonModel extends Model
{
    use DataStatusTrait;
    use ModelTrait;

    protected $createTime = 'create_at';

    protected $updateTime = 'update_at';

    /**
     * 分页
     * @param Query $query
     * @param array $param
     * @return \think\db\Query
     */
    public function scopePages(Query $query, array $param): Query
    {
        return $query->page($param['page'], $param['pageSize']);
    }
}
