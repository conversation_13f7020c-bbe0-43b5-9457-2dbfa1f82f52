<?php
namespace app\common\middleware;

use Closure;
use think\Request;
use think\Response;

/**
 * 移除header中X-Powered-By中间件
 * <AUTHOR> <<EMAIL>>
 * @package app\common\middleware
 */
class RemovePoweredByMiddleware
{
    public function handle(Request $request, Closure $next, ?array $header = []): Response
    {
        header_remove('X-Powered-By'); // 移除X-Powered-By头

        return $next($request);
    }
}
