<?php
declare(strict_types = 1);

namespace app\tenant\service;

use app\common\service\mqtt\MqttService;
use think\facade\Log;

/**
 * 租户设备心跳服务类
 * 用于处理租户设备的心跳机制，包括心跳消息发送、状态监控和异常处理
 * <AUTHOR> <<EMAIL>>
 * @package app\tenant\service
 */
class TenantDeviceHeartbeatService extends BaseService
{
    // 心跳状态缓存前缀
    private const CACHE_PREFIX = 'tenant_device_heartbeat:';

    // 缓存key定义
    private const CACHE_KEY = [
        'STATUS'  => 'status', // 心跳状态(HASH)
        'HISTORY' => 'history', // 心跳历史(ZSET)
    ];

    /**
     * 发送设备心跳消息
     * @param int $tenantId 租户ID
     * @param string $deviceId 设备ID
     * @param array $data 心跳数据
     * @return bool
     */
    public static function sendHeartbeat(int $tenantId, string $deviceId, array $data = []): bool
    {
        try {
            $message = [
                'type'      => 'device_heartbeat',
                'timestamp' => date('c'),
                'payload'   => array_merge([
                    'tenant_id' => $tenantId,
                    'device_id' => $deviceId,
                    'uptime'    => time(),
                ], $data),
            ];

            $mqtt = MqttService::getInstance();

            return $mqtt->publishHeartbeat($tenantId, json_encode($message));
        } catch (\Exception $e) {
            Log::error('Failed to send device heartbeat: ' . $e->getMessage());

            return false;
        }
    }

    /**
     * 更新设备在线心跳状态
     * @param int $tenantId 租户ID
     * @param string $deviceId 设备ID
     * @param array $data 心跳数据
     * @return bool
     */
    public static function updateOnlineStatus(int $tenantId, string $deviceId, array $data = []): bool
    {
        try {
            $redis = self::getRedisHandler();
            $now   = time();

            // 更新心跳状态
            $statusKey = self::getHeartbeatKey($tenantId, $deviceId, 'STATUS');
            $redis->hMSet($statusKey, array_merge([
                'last_heartbeat' => $now,
                'status'         => 'online',
                'device_id'      => $deviceId,
            ], $data));

            // 记录心跳历史，并限制最大记录数
            $historyKey = self::getHeartbeatKey($tenantId, $deviceId, 'HISTORY');
            $redis->zAdd($historyKey, $now, json_encode([
                'timestamp' => $now,
                'device_id' => $deviceId,
                'data'      => $data,
            ]));

            $maxRecords = config('mqtt.heartbeat_max_records', 10); // 默认10条

            // 保留最近的记录，删除更早的记录
            $totalRecords = $redis->zCard($historyKey);

            if ($totalRecords > $maxRecords) {
                $redis->zRemRangeByRank($historyKey, 0, $totalRecords - ($maxRecords + 1));
            }

            // 设置过期时间
            $expire = config('mqtt.heartbeat_expire', 180); // 默认3分钟
            $redis->expire($statusKey, $expire);
            $redis->expire($historyKey, $expire * 2); // 历史记录保留更长时间

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to update device heartbeat: ' . $e->getMessage());

            return false;
        }
    }

    /**
     * 检查设备在线状态
     * @param int $tenantId 租户ID
     * @param string $deviceId 设备ID
     * @return bool
     */
    public static function isOnline(int $tenantId, string $deviceId): bool
    {
        $redis     = self::getRedisHandler();
        $statusKey = self::getHeartbeatKey($tenantId, $deviceId, 'STATUS');

        $status = $redis->hGet($statusKey, 'status');

        return $status === 'online';
    }

    /**
     * 获取设备最后心跳时间
     * @param int $tenantId 租户ID
     * @param string $deviceId 设备ID
     * @return int|null
     */
    public static function getLastHeartbeat(int $tenantId, string $deviceId): ?int
    {
        $redis     = self::getRedisHandler();
        $statusKey = self::getHeartbeatKey($tenantId, $deviceId, 'STATUS');

        $lastHeartbeat = $redis->hGet($statusKey, 'last_heartbeat');

        return $lastHeartbeat ? (int)$lastHeartbeat : null;
    }

    /**
     * 检查并处理设备心跳超时
     * @param int $tenantId 租户ID
     * @param string $deviceId 设备ID
     * @return bool 是否发生超时
     */
    public static function checkHeartbeatTimeout(int $tenantId, string $deviceId): bool
    {
        $redis         = self::getRedisHandler();
        $statusKey     = self::getHeartbeatKey($tenantId, $deviceId, 'STATUS');
        $lastHeartbeat = $redis->hGet($statusKey, 'last_heartbeat');

        if (!$lastHeartbeat) {
            return false;
        }

        $now       = time();
        $expire    = config('mqtt.heartbeat_expire', 180);
        $isTimeout = ($now - (int)$lastHeartbeat) > $expire;

        if ($isTimeout) {
            // 更新状态为离线
            $redis->hSet($statusKey, 'status', 'offline');

            // 记录离线事件
            self::logOfflineEvent($tenantId, $deviceId, (int)$lastHeartbeat);

            // 发送离线通知
            self::sendOfflineNotification($tenantId, $deviceId);
        }

        return $isTimeout;
    }

    /**
     * 记录设备离线事件
     * @param int $tenantId 租户ID
     * @param string $deviceId 设备ID
     * @param int $lastHeartbeat 最后心跳时间
     */
    private static function logOfflineEvent(int $tenantId, string $deviceId, int $lastHeartbeat): void
    {
        Log::warning(sprintf(
            'Tenant [%s] Device [%s] went offline. Last heartbeat: %s',
            $tenantId,
            $deviceId,
            date('Y-m-d H:i:s', $lastHeartbeat)
        ));
    }

    /**
     * 发送设备离线通知
     * @param int $tenantId 租户ID
     * @param string $deviceId 设备ID
     */
    private static function sendOfflineNotification(int $tenantId, string $deviceId): void
    {
        try {
            $message = [
                'type'      => 'device_status_change',
                'timestamp' => date('c'),
                'payload'   => [
                    'tenant_id'    => $tenantId,
                    'device_id'    => $deviceId,
                    'status'       => 'offline',
                    'offline_time' => time(),
                ],
            ];

            $mqtt = MqttService::getInstance();
            $mqtt->publish("helio/tenant/{$tenantId}/device/{$deviceId}/heartbeat", json_encode($message));
        } catch (\Exception $e) {
            Log::error('Failed to send offline notification: ' . $e->getMessage());
        }
    }

    /**
     * 更新设备离线状态
     * @param int $tenantId 租户ID
     * @param string $deviceId 设备ID
     * @param array $data 离线数据
     * @return bool
     */
    public static function updateOfflineStatus(int $tenantId, string $deviceId, array $data = []): bool
    {
        try {
            $redis = self::getRedisHandler();
            $now   = time();

            // TODO: 后期可能需要将设备最后在线时间、历史记录等信息保存到数据库中
            // 直接删除设备状态缓存key
            $statusKey  = self::getHeartbeatKey($tenantId, $deviceId, 'STATUS');
            $historyKey = self::getHeartbeatKey($tenantId, $deviceId, 'HISTORY');
            $redis->del($statusKey);
            $redis->del($historyKey);

            // 记录离线事件并发送通知
            self::logOfflineEvent($tenantId, $deviceId, $now);
            self::sendOfflineNotification($tenantId, $deviceId);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to update device offline status: ' . $e->getMessage());

            return false;
        }
    }

    /**
     * 获取设备心跳缓存key
     * @param int $tenantId 租户ID
     * @param string $deviceId 设备ID
     * @param string $type 缓存类型
     * @return string
     */
    private static function getHeartbeatKey(int $tenantId, string $deviceId, string $type): string
    {
        return self::CACHE_PREFIX . $tenantId . ':' . $deviceId . ':' . self::CACHE_KEY[$type];
    }
}
