<?php
declare(strict_types = 1);

namespace app\tenant\exception;

use app\common\traits\ResultTrait;
use think\exception\Handle as BaseHandle;
use think\exception\ValidateException;
use think\Response;
use Throwable;

/**
 * 租户模块异常处理器
 * 负责处理租户模块的业务异常和验证异常，提供统一的异常响应格式
 * <AUTHOR> <<EMAIL>>
 * @package app\tenant\exception
 */
class Handle extends BaseHandle
{
    use ResultTrait;

    public function render($request, Throwable $e): Response
    {
        // 处理业务异常
        if ($e instanceof AccountException) {
            $responseData = $e->getResponseData();

            return $this->result($responseData['message'], $responseData['data'], $responseData['status'], $responseData['code'], $responseData['extra']);
        }

        // 处理验证异常
        if ($e instanceof ValidateException) {
            return $this->result($e->getMessage(), null, 0);
        }

        // 其他异常交给父类处理
        return parent::render($request, $e);
    }
}
