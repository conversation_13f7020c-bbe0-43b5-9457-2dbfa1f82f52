<?php
declare(strict_types = 1);
namespace app\system\constant;

use app\common\constant\CommonConstant;

/**
 * 拦截器相关常量 (10010-10099)
 * <AUTHOR> <<EMAIL>>
 * @package app\system\constant
 */
class InterceptConstant extends CommonConstant
{
    public const USER_FORCE_LOCKSCREEN = ['code' => 10010, 'message' => '请解锁屏幕', 'label' => 'lockscreen'];

    public const USER_FORCE_CHANGE_PASSWORD = ['code' => 10011, 'message' => '由于安全因素，请修改密码后使用', 'label' => 'force_change_password'];

    /**
     * 根据code获取message
     * @param int|string $code
     * @return string
     */
    public static function getMessage(int|string $code): string
    {
        foreach (static::getConstants() as $constant) {
            if ($constant['code'] === $code) {
                return $constant['message'];
            }
        }

        return '未知错误';
    }

    public static function getLabel(int|string $code): string
    {
        foreach (static::getConstants() as $constant) {
            if ($constant['code'] === $code) {
                return $constant['label'] ?? '';
            }
        }

        return '';
    }

    private static function getConstants(): array
    {
        $ref = new \ReflectionClass(static::class);

        return $ref->getConstants();
    }
}
