<?php
namespace app\common\exception;

/**
 * 自定义异常基类
 * <AUTHOR> <<EMAIL>>
 * @package app\common\exception
 */
abstract class CommonException extends \Exception
{
    /**
     * 错误描述
     * @var string
     */
    public $message = '';

    /**
     * 错误编码
     * @var int
     */
    public $code = 0;

    /**
     * 响应数据
     */
    public mixed $data;

    /**
     * 额外数据
     */
    public mixed $extra;

    /**
     * 获取响应数据
     * @return array
     */
    public function getResponseData(): array
    {
        return [
            'code'    => $this->code,
            'message' => $this->message,
            'data'    => $this->data  ?? null,
            'extra'   => $this->extra ?? null,
        ];
    }
}
