<?php
declare(strict_types = 1);

namespace app\tenant\model;

use app\common\service\ClientService;
use app\common\service\FingerService;
use app\tenant\context\TenantContext;

/**
 * 租户登录日志模型类
 * <AUTHOR> <<EMAIL>>
 * @package app\tenant\model
 */
class LoginLog extends BaseModel
{
    protected $name = 'tenant_login_log';

    protected $pk = 'id';

    protected $createTime = 'login_time';

    protected $updateTime = false;

    // 自动完成设置登录时间
    protected function setLoginTimeAttr(): int
    {
        return time();
    }

    // 登录时间获取器
    protected function getLoginTimeAttr($value): string
    {
        return $value ? date('Y-m-d H:i:s', $value) : '';
    }

    /**
     * 记录登录日志
     * @param string $loginType 登录类型
     * @param string $title 登录消息
     * @param int $status 登录状态（1：成功，0：失败）
     * @return bool
     */
    public static function recordLogin(string $loginType = '', string $title = 'success', int $status = 1): bool
    {
        // 从上下文中获取传递过来的数据
        $userInfo = TenantContext::getInstance()->getUserInfo();

        return self::create([
            'tenant_id'  => $userInfo['tenant_id'] ?? 0,
            'user_id'    => $userInfo['id']        ?? 0,
            'username'   => $userInfo['username']  ?? 'unknown',
            'login_type' => $loginType,
            'title'      => $title,
            'ip'         => request()->ip(),
            'user_agent' => request()->header('User-Agent'),
            'request_id' => request()->requestId ?: '',
            'client_id'  => ClientService::getClientId(),
            'finger_id'  => FingerService::getClientFingerId(),
            'login_time' => time(),
            'status'     => $status,
        ]) != false;
    }
}
