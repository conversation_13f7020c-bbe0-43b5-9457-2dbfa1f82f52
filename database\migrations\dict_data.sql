-- 表结构: dict_data
CREATE TABLE `dict_data` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `dict_type_id` bigint(20) DEFAULT NULL COMMENT '字典类型id',
  `dict_item` varchar(100) DEFAULT NULL COMMENT '字典项',
  `dict_value` varchar(255) DEFAULT NULL COMMENT '字典值',
  `dict_css` varchar(100) DEFAULT NULL COMMENT 'css样式',
  `order_num` int(11) DEFAULT NULL COMMENT '排序号',
  `remark` varchar(100) DEFAULT NULL COMMENT '备注',
  `status` char(1) DEFAULT NULL COMMENT '状态：1-启用 0-禁用',
  `create_user` bigint(20) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` bigint(20) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='数据字典';
