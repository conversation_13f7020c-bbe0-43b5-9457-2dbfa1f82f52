-- 表结构: system_permission_api
CREATE TABLE `system_permission_api` (
  `permission_id` int(11) NOT NULL DEFAULT '0' COMMENT '节点id',
  `name` varchar(50) NOT NULL DEFAULT '' COMMENT '接口名称',
  `url` varchar(255) NOT NULL DEFAULT '' COMMENT '接口地址',
  `method` varchar(255) NOT NULL DEFAULT '' COMMENT '请求方式',
  `params` varchar(255) NOT NULL DEFAULT '' COMMENT '请求参数',
  `create_at` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='系统权限节点API关联表';
