<?php
declare(strict_types = 1);

namespace app\tenant\service;

use app\common\service\CommonService;

/**
 * 租户服务基类
 * 为租户系统中的所有服务类提供基础功能和通用方法
 * 包含以下主要功能：
 * - 提供应用前缀常量
 * - 继承通用服务类的功能
 * - 初始化租户服务的基础配置
 * <AUTHOR> <<EMAIL>>
 * @package app\tenant\service
 */
class BaseService extends CommonService
{
    public const APP_PREFIX = 'tenant:'; // 当前应用前缀

    public function __construct()
    {
        parent::__construct();
    }
}
