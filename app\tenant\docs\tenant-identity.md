# 租户身份属性设计评估
您提出的给租户增加身份属性(identity)的设计思路非常合理，这是一种灵活且可扩展的方案。这种设计有以下几个优点：

## 优点
1. 灵活性 ：允许一个租户拥有多个身份，适应不同行业和场景需求
2. 可扩展性 ：未来添加新的身份类型时，不需要修改核心代码，只需在数据库中添加配置
3. 统一管理 ：在system应用后台统一配置不同身份的功能和菜单，便于管理
4. 用户体验 ：不同身份的用户登录后看到的功能和菜单符合其工作需求
## 实现建议
### 1. 数据库设计
建议添加以下表结构：

```sql
-- 租户身份表
CREATE TABLE `helio_tenant_identity` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL COMMENT '身份名称',
  `code` varchar(50) NOT NULL COMMENT '身份代码',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `create_at` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_at` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  `status` tinyint(2) NOT NULL DEFAULT '1' COMMENT '状态 -1删除 0禁用 1启用',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='租户身份表';

-- 租户-身份关联表
CREATE TABLE `helio_tenant_identity_rel` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `tenant_id` int(11) NOT NULL COMMENT '租户ID',
  `identity_id` int(11) NOT NULL COMMENT '身份ID',
  `create_at` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_tenant_identity` (`tenant_id`,`identity_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='租户-身份关联表';

-- 用户-身份关联表
CREATE TABLE `helio_tenant_user_identity` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `identity_id` int(11) NOT NULL COMMENT '身份ID',
  `tenant_id` int(11) NOT NULL COMMENT '租户ID',
  `create_at` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_identity` (`user_id`,`identity_id`,`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户-身份关联表';

-- 身份-菜单关联表
CREATE TABLE `helio_tenant_identity_menu` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `identity_id` int(11) NOT NULL COMMENT '身份ID',
  `menu_id` int(11) NOT NULL COMMENT '菜单ID',
  `create_at` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_identity_menu` (`identity_id`,`menu_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='身份-菜单关联表';
```

### 2. 修改Tenant模型

```php
<?php
declare(strict_types = 1);

namespace app\tenant\model;

/**
 * 租户模型类
 * <AUTHOR> <<EMAIL>>
 * @package app\tenant\model
 */
class Tenant extends BaseModel
{
    protected $name = 'tenant';

    protected $field = [
        'code',
        'contact_person',
        'contact_phone',
        'description',
        'domain',
        'expire_time',
        'max_users',
        'name',
        'sort',
        'status',
    ];

    /**
     * 添加租户
     * @param $data
     * @return int
     */
    public static function addTenant($data): int
    {
        if ($res = self::create($data)) {
            return $res->id;
        }

        return 0;
    }

    // ... existing code ...

    /**
     * 获取租户关联的身份列表
     * @return \think\model\relation\HasMany
     */
    public function identities()
    {
        return $this->hasMany('TenantIdentityRel', 'tenant_id', 'id');
    }

    /**
     * 分配身份给租户
     * @param array $identityIds 身份ID数组
     * @return bool
     */
    public function assignIdentities(array $identityIds): bool
    {
        // 删除原有关联
        TenantIdentityRel::where('tenant_id', $this->id)->delete();

        if (empty($identityIds)) {
            return true;
        }

        // 创建新关联
        $data = [];
        foreach ($identityIds as $identityId) {
            $data[] = [
                'tenant_id' => $this->id,
                'identity_id' => $identityId,
                'create_at' => date('Y-m-d H:i:s')
            ];
        }

        return (new TenantIdentityRel)->insertAll($data) > 0;
    }
}
```

### 3. 创建身份相关模型

```php
// app/tenant/model/Identity.php
<?php
declare(strict_types = 1);

namespace app\tenant\model;

class Identity extends BaseModel
{
    protected $name = 'tenant_identity';

    // 身份与菜单的多对多关联
    public function menus()
    {
        return $this->belongsToMany('Menu', 'identity_menu', 'identity_id', 'menu_id');
    }
}

// app/tenant/model/TenantIdentityRel.php
<?php
declare(strict_types = 1);

namespace app\tenant\model;

class TenantIdentityRel extends BaseModel
{
    protected $name = 'tenant_identity_rel';
}

// app/tenant/model/UserIdentity.php
<?php
declare(strict_types = 1);

namespace app\tenant\model;

class UserIdentity extends BaseModel
{
    protected $name = 'user_identity';
}

// app/tenant/model/IdentityMenu.php
<?php
declare(strict_types = 1);

namespace app\tenant\model;

class IdentityMenu extends BaseModel
{
    protected $name = 'identity_menu';
}

```

### 4. 用户登录逻辑修改
在用户登录成功后，需要获取用户的身份信息，并根据身份加载对应的菜单和权限：

```php
// 在登录成功后
$userIdentities = UserIdentity::where('user_id', $userId)
    ->where('tenant_id', $tenantId)
    ->column('identity_id');

// 如果用户有多个身份，可以让用户选择当前要使用的身份
// 或者默认使用第一个身份
$currentIdentityId = $userIdentities[0] ?? 0;

// 根据身份ID获取菜单
$menus = [];
if ($currentIdentityId) {
    $menus = IdentityMenu::where('identity_id', $currentIdentityId)
        ->join('menu', 'identity_menu.menu_id = menu.id')
        ->select()
        ->toArray();
}

// 将身份信息和菜单信息存入会话
session('current_identity_id', $currentIdentityId);
session('menus', $menus);
```

## 其他考虑因素
1. 默认身份 ：可以设置一个默认身份，确保每个租户至少有一个身份
2. 身份切换 ：为用户提供在不同身份间切换的功能
3. 权限继承 ：考虑身份之间的权限继承关系，例如管理员身份可以继承操作员身份的所有权限
4. 数据隔离 ：不同身份可能需要看到不同的数据，需要在数据访问层面做好隔离
5. 审计日志 ：记录用户身份切换和操作的日志，便于追踪
## 结论
您提出的设计思路非常合理，通过增加身份属性，可以灵活地适应不同行业和场景的需求，同时也便于后期的扩展和维护。建议在实现过程中注意数据隔离和权限控制，确保系统的安全性和可用性。
