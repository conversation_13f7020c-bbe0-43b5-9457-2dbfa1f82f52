<?php
namespace app\tenant\repository;

use app\common\repository\CommonRepository;
use app\tenant\context\TenantContext;

/**
 * 租户模块仓储基类
 * 继承自CommonRepository，为租户模块的数据访问层提供统一的基础实现
 * <AUTHOR> <<EMAIL>>
 * @package app\tenant\repository
 */
abstract class BaseRepository extends CommonRepository
{
    /**
     * 获取当前租户用户信息
     * @return array
     */
    protected function getUserInfo(): array
    {
        return TenantContext::getInstance()->getUserInfo();
    }

    /**
     * 获取租户用户是否是创始人
     * @return int
     */
    protected function isFounder(): int
    {
        return TenantContext::getInstance()->isFounder();
    }

    /**
     * 获取当前租户ID
     * @return int
     */
    protected function getTenantId(): int
    {
        return TenantContext::getInstance()->getTenantId();
    }

    /**
     * 获取当前租户编码
     * @return string|null
     */
    protected function getTenantCode(): ?string
    {
        return TenantContext::getInstance()->getTenantCode();
    }

    /**
     * 获取当前用户ID
     * @return int
     */
    protected function getUserId(): int
    {
        return TenantContext::getInstance()->getUserId();
    }
}
