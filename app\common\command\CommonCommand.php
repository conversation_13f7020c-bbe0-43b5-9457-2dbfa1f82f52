<?php
namespace app\common\command;

use app\common\traits\ConsoleTrait;
use think\console\Command;
use think\console\Input;
use think\console\Output;

/**
 * 命令行基类
 * @package app\common\command
 */
class CommonCommand extends Command
{
    // 控制台输出、日志综合实现
    use ConsoleTrait;

    /**
     * 配置指令
     */
    protected function configure()
    {
        // 由子类实现
    }

    /**
     * 执行指令
     * @param Input $input
     * @param Output $output
     * @return void
     */
    protected function execute(Input $input, Output $output)
    {
        $this->setOutput($output);

        // 由子类实现
    }
}
