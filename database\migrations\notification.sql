-- 表结构: notification
CREATE TABLE `notification` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `tenant_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '租户ID',
  `title` varchar(255) NOT NULL COMMENT '标题',
  `content` text NOT NULL COMMENT '内容',
  `type` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '消息类型：1系统消息 2公告通知 3私信',
  `receive_range` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '接收范围：1全体 2部门 3个人',
  `target_ids` json DEFAULT NULL COMMENT '目标ID(部门id或用户id列表)',
  `sender_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '发送者ID',
  `create_at` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_at` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  `status` tinyint(2) unsigned NOT NULL DEFAULT '1' COMMENT '消息状态(1:启用,0:禁用)',
  PRIMARY KEY (`id`),
  KEY `idx_type` (`type`),
  KEY `idx_sender` (`sender_id`),
  KEY `idx_tenant_id` (`tenant_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='消息通知表';
