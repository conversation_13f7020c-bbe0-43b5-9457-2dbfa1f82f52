<?php
declare(strict_types = 1);

namespace app\tenant\command;

use app\common\service\mqtt\MqttService;
use Swoole\Coroutine;

use function Swoole\Coroutine\run;

use Swoole\Runtime;
use think\console\Input;
use think\console\input\Option;
use think\console\Output;

/**
 * 客户端状态监控命令
 * 用于监听客户端状态变化（上线/离线）
 * <AUTHOR> <<EMAIL>>
 * @package app\tenant\command
 */
class ClientStatusMonitorCommand extends BaseCommand
{
    /**
     * MQTT配置
     * @var array
     */
    private array $config = [];

    /**
     * 进程锁文件路径
     * @var string
     */
    private string $lockFile = '';

    /**
     * MQTT服务实例
     * @var MqttService
     */
    protected MqttService $mqttService;

    /**
     * 是否继续运行
     * @var bool
     */
    protected bool $running = true;

    /**
     * 在线客户端列表
     * @var array
     */
    protected array $onlineClients = [];

    public function __construct()
    {
        parent::__construct();

        // 合并全局配置和tenant模块配置
        $this->config = getAppConfig('mqtt', 'tenant');
        // 初始化进程锁文件路径
        $this->lockFile = runtime_path() . 'client_status_monitor.lock';
    }

    /**
     * 配置指令
     * php think tenant:clientStatusMonitor
     * php think tenant:clientStatusMonitor -t 10001
     * php think tenant:clientStatusMonitor --tenant 租户编码
     * @return void
     */
    protected function configure(): void
    {
        $this->setName('tenant:clientStatusMonitor')
            ->setDescription('启动客户端状态监控服务')
            ->addOption('tenant', 't', Option::VALUE_OPTIONAL, '指定租户编码，不指定则监听所有租户', null);
    }

    /**
     * 执行指令
     * @param Input $input
     * @param Output $output
     * @return void
     */
    protected function execute(Input $input, Output $output): void
    {
        // 检查是否已有实例在运行
        if (file_exists($this->lockFile)) {
            $pid = file_get_contents($this->lockFile);

            if ($pid && posix_kill((int)$pid, 0)) {
                $this->logError("已有一个客户端状态监控服务实例正在运行，进程ID：{$pid}");

                return;
            }

            // 如果进程不存在，删除锁文件
            unlink($this->lockFile);
        }

        // 创建进程锁
        file_put_contents($this->lockFile, getmypid());

        $config = $this->config;

        Runtime::enableCoroutine();

        // 在协程环境中运行MQTT客户端
        run(function() use ($input, $output, $config) {
            // 初始化MQTT服务
            $this->mqttService = MqttService::getInstance();

            if (!$this->mqttService->connect()) {
                $this->logError('MQTT服务连接失败，失败原因请查看Log');

                return;
            }

            // 获取租户编码参数
            $tenantCode = $input->getOption('tenant');

            // 主题名称，指定租户编码，不指定则监听所有租户
            $statusTopic = $this->mqttService->buildTopic($config['topic_patterns']['client_status'], [
                'tenant_code' => $tenantCode ?: '+',
                'user_id'     => '+',
            ]);

            // 按协议版本组合成数组
            $topics = [
                $statusTopic => ['qos' => 0, 'no_local' => true, 'retain' => false],
            ];

            // 订阅主题
            if (!$this->mqttService->subscribe($topics)) {
                $this->logError('订阅主题失败');

                return;
            }

            if ($tenantCode) {
                $this->logInfo("开始监听租户【{$tenantCode}】的客户端状态主题[{$statusTopic}]");
            } else {
                $this->logInfo("开始监听所有租户的客户端状态主题[{$statusTopic}]");
            }

            // 注册信号处理
            pcntl_signal(SIGTERM, [$this, 'handleSignal']);
            pcntl_signal(SIGINT, [$this, 'handleSignal']);

            // 创建信号处理协程
            Coroutine::create(function() {
                while ($this->running) {
                    pcntl_signal_dispatch();
                    Coroutine::sleep(0.1); // 每100ms检查一次信号
                }
            });

            // 监听循环
            while ($this->running) {
                // 检查连接状态并自动重连
                if (!$this->mqttService->isConnected()) {
                    $this->logWarning('检测到MQTT连接断开，尝试重新连接...');

                    if (!$this->mqttService->connect()) {
                        $this->logError('MQTT重连失败，5秒后重试');
                        Coroutine::sleep(5);
                        continue;
                    }
                    $this->logInfo('MQTT重连成功');

                    // 重新订阅主题
                    if (!$this->mqttService->subscribe($topics)) {
                        $this->logError('重新订阅主题失败');
                        continue;
                    }
                }

                // 接收消息
                $result = $this->mqttService->receive();

                if (!$result) {
                    // 发送心跳包保持连接
                    if (!$this->mqttService->ping()) {
                        $this->logError('发送心跳包失败，标记连接为断开状态');
                        $this->mqttService->disconnect();
                    }
                    continue;
                }

                // 说明是keepalive心跳
                if ($result === true) {
                    continue;
                }

                // 调试模式下打印消息到屏幕
                if ($config['debug']) {
                    $this->logDebug('收到客户端状态消息: ' . (!is_array($result) ? $result : ''));

                    if (is_array($result)) {
                        dump($result);
                    }
                }

                try {
                    // 解析消息
                    $payload = json_decode($result['message'] ?? '', true);

                    // 检查必要字段
                    if (empty($payload['tenantCode']) || empty($payload['userId']) || !isset($payload['status'])) {
                        $this->logWarning('客户端状态消息缺少必要字段: ' . json_encode($payload, JSON_UNESCAPED_UNICODE));
                        continue;
                    }

                    // 处理客户端状态消息
                    $this->processClientStatus($payload);
                } catch (\Exception $e) {
                    $this->logError('处理客户端状态消息异常: ' . $e->getMessage());
                }
            }

            // 优雅退出
            $this->mqttService->unsubscribe(array_keys($topics));

            $this->logWarning('客户端状态监控服务已停止');

            // 删除进程锁
            if (file_exists($this->lockFile)) {
                unlink($this->lockFile);
            }
        });
    }

    /**
     * 处理客户端状态消息
     * @param array $payload 消息负载
     */
    protected function processClientStatus(array $payload): void
    {
        $tenantCode = $payload['tenantCode'];
        $userId     = $payload['userId'];
        $status     = $payload['status'];
        $timestamp  = $payload['timestamp']  ?? time() * 1000;
        $clientType = $payload['clientType'] ?? 'unknown';

        // 生成客户端唯一标识
        $clientId = "{$tenantCode}_{$userId}_{$clientType}";

        // 根据状态处理
        if ($status === 'online') {
            // 客户端上线
            $this->onlineClients[$clientId] = [
                'tenantCode' => $tenantCode,
                'userId'     => $userId,
                'clientType' => $clientType,
                'lastSeen'   => $timestamp,
                'onlineTime' => $timestamp,
            ];

            $onlineTimeStr = date('Y-m-d H:i:s', intval($timestamp / 1000));

            $this->logInfo("客户端上线: 租户[{$tenantCode}] 用户[{$userId}] 上线时间[{$onlineTimeStr}] 客户端类型[{$clientType}]");
        } elseif ($status === 'offline') {
            // 客户端离线
            if (isset($this->onlineClients[$clientId])) {
                // 切记离线或者异常断线时不要取传过来的timestamp，因为遗嘱消息的时间戳是连接时生成的
                $timestamp = time();

                $onlineTime     = intval($this->onlineClients[$clientId]['onlineTime'] / 1000 ?? 0);
                $onlineDuration = format_seconds($timestamp - $onlineTime); // 将秒数转换为最合适的时间单位表示

                $onlineTimeStr  = date('Y-m-d H:i:s', $onlineTime);
                $offlineTimeStr = date('Y-m-d H:i:s', $timestamp);

                $this->logInfo("客户端离线: 租户[{$tenantCode}] 用户[{$userId}] 类型[{$clientType}] 上线时间[{$onlineTimeStr}] 下线时间[{$offlineTimeStr}] 在线时长: {$onlineDuration}");

                unset($this->onlineClients[$clientId]);
            } else {
                $this->logWarning("收到未知客户端的离线消息: 租户[{$tenantCode}] 用户[{$userId}] 类型[{$clientType}]" . json_encode($payload, JSON_UNESCAPED_UNICODE));
            }
        } elseif ($status === 'alive') {
            // 客户端心跳
            if (isset($this->onlineClients[$clientId])) {
                // 更新最后活跃时间
                $this->onlineClients[$clientId]['lastSeen'] = $timestamp;

                // 计算当前在线时长
                $onlineTime        = intval($this->onlineClients[$clientId]['onlineTime'] / 1000);
                $currentDuration   = time() - $onlineTime; // 当前在线时长（秒）
                $formattedDuration = format_seconds($currentDuration); // 格式化在线时长

                $this->logInfo("客户端心跳: 租户[{$tenantCode}] 用户[{$userId}] 类型[{$clientType}] 当前在线时长: {$formattedDuration}");
            } else {
                // 如果收到未知客户端的心跳，将其视为上线
                $this->onlineClients[$clientId] = [
                    'tenantCode' => $tenantCode,
                    'userId'     => $userId,
                    'clientType' => $clientType,
                    'lastSeen'   => $timestamp,
                    'onlineTime' => $timestamp,
                ];

                $onlineTimeStr = date('Y-m-d H:i:s', intval($timestamp / 1000));
                $this->logWarning("收到未知客户端的心跳，将其标记为上线: 租户[{$tenantCode}] 用户[{$userId}] 上线时间[{$onlineTimeStr}] 类型[{$clientType}]");
            }
        } else {
            // 未知状态
            $this->logWarning("收到未知状态的客户端消息: 租户[{$tenantCode}] 用户[{$userId}] 类型[{$clientType}] 状态[{$status}]");
        }
    }

    /**
     * 处理指定信号
     * @param int $signal
     */
    public function handleSignal(int $signal): void
    {
        switch ($signal) {
            case SIGTERM:
            case SIGINT:
                $this->running = false;
                break;
        }
    }
}
