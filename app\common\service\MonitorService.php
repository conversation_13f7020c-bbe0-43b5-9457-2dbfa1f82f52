<?php
namespace app\common\service;

use think\facade\Cache;
use think\facade\Db;

/**
 * 服务监控服务类
 * <AUTHOR> <<EMAIL>>
 * @package app\common\service
 */
class MonitorService extends CommonService
{
    /**
     * 获取php信息
     * @param bool $specific 是否显示详细
     * @return array
     */
    public function php(bool $specific = false): array
    {
        return [
            'status'  => PHP_VERSION ? 1 : 0,
            'version' => PHP_VERSION ?? null,
            'message' => PHP_VERSION ? '连接正常' : '连接异常',
        ];
    }

    /**
     * 获取mysql信息
     * @param bool $specific 是否显示详细
     * @return array
     */
    public function mysql(bool $specific = false): array
    {
        try {
            $variables      = Db::query('SHOW VARIABLES;');
            $variables_data = [];

            if ($variables) {
                foreach ($variables as $var) {
                    $variables_data[$var['Variable_name']] = $var['Value'];
                }
            }
            $is_alive = !empty($variables_data['version']) ? 1 : 0;
            $data     = [];

            if ($specific === true) {
                if ($is_alive) {
                    $status      = Db::query('SHOW GLOBAL STATUS;');
                    $status_data = [];

                    if ($status) {
                        foreach ($status as $state) {
                            $status_data[$state['Variable_name']] = $state['Value'];
                        }
                    }
                    $lock_tables              = Db::query('SHOW OPEN TABLES WHERE In_use > 0;');
                    $data['show_status']      = $status_data    ?? [];
                    $data['show_lock_tables'] = $lock_tables    ?? [];
                    $data['show_variables']   = $variables_data ?? [];
                }
            }
            $result = [
                'status'  => $is_alive,
                'version' => $variables_data['version'] ?? null,
                'message' => $is_alive ? '连接正常' : '连接异常',
            ];

            if ($specific === true) {
                $result['detail'] = $data;
            }

            return $result;
        } catch (\Exception $exception) {
            $result = [
                'status'  => 0,
                'version' => null,
                'message' => $exception->getMessage(),
            ];

            if ($specific === true) {
                $result['show'] = [];
            }

            return $result;
        }
    }

    /**
     * 获取redis信息
     * @param bool $specific 是否显示详细
     * @return array
     */
    public function redis(bool $specific = false): array
    {
        try {
            $info   = Cache::store('redis')->info();
            $result = [
                'status'  => isset($info['redis_version']) && $info['redis_version'] ? 1 : 0,
                'version' => $info['redis_version'] ?? null,
                'message' => $info['redis_version'] ? '连接正常' : '连接异常',
            ];

            if ($specific === true) {
                $result['info'] = $info ?: [];
            }

            return $result;
        } catch (\Exception $exception) {
            $result = [
                'status'  => 0,
                'version' => null,
                'message' => $exception->getMessage(),
            ];

            if ($specific === true) {
                $result['info'] = [];
            }

            return $result;
        }
    }

    /**
     * 获取应用服务器信息
     * @param array $server
     * @param bool $specific 是否显示详细
     * @return array
     */
    public function app(array $server = [], bool $specific = false): array
    {
        return [
            'host'            => $server['HTTP_HOST']       ?? $_SERVER['HTTP_HOST'],
            'server_name'     => $server['SERVER_NAME']     ?? $_SERVER['SERVER_NAME'],
            'server_addr'     => $server['SERVER_ADDR']     ?? ($_SERVER['SERVER_ADDR'] ?? $_SERVER['REMOTE_ADDR']),
            'server_port'     => $server['SERVER_PORT']     ?? ($_SERVER['SERVER_PORT'] ?? $_SERVER['REMOTE_PORT']),
            'server_protocol' => $server['SERVER_PROTOCOL'] ?? $_SERVER['SERVER_PROTOCOL'],
            'remote_addr'     => $server['REMOTE_ADDR']     ?? $_SERVER['REMOTE_ADDR'],
            'remote_port'     => $server['REMOTE_PORT']     ?? $_SERVER['REMOTE_PORT'],
            'request_method'  => $server['REQUEST_METHOD']  ?? $_SERVER['REQUEST_METHOD'],
            'server_software' => $server['SERVER_SOFTWARE'] ?? $_SERVER['SERVER_SOFTWARE'],
            'document_root'   => $server['DOCUMENT_ROOT']   ?? $_SERVER['DOCUMENT_ROOT'],
            'request_status'  => $server['REDIRECT_STATUS'] ?? ($_SERVER['REDIRECT_STATUS'] ?? 200),
        ];
    }
}
