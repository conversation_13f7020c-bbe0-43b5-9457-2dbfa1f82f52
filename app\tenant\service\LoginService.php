<?php
declare(strict_types = 1);

namespace app\tenant\service;

use app\tenant\constant\LoginType as LoginTypeConstant;
use app\tenant\context\TenantContext;
use app\tenant\model\User as UserModel;
use app\tenant\repository\PermissionRepository;
use app\tenant\repository\TenantRepository;
use app\tenant\service\login\LoginStrategyInterface;

/**
 * 租户登录服务类
 * 负责租户系统中的用户登录认证和管理
 * 包含以下主要功能：
 * - 处理用户登录请求
 * - 管理登录重试次数和锁定机制
 * - 支持多种登录策略（账号密码、手机验证码等）
 * - 维护用户登录状态
 * <AUTHOR> <<EMAIL>>
 * @package app\tenant\service
 */
class LoginService
{
    /**
     * 登录重试次数
     */
    public const LOGIN_RETRY_COUNT = 5;

    /**
     * 登录失败锁定时长（单位：秒）
     */
    public const LOGIN_FAIL_LOCK_DURATION = 900;

    /**
     * @var LogService
     */
    protected LogService $logService;

    public function __construct()
    {
        $this->logService = new LogService();

        // 自动注册所有配置的登录策略
        foreach (LoginTypeConstant::$config as $type => $config) {
            if (isset($config['strategy']) && class_exists($config['strategy'])) {
                $strategyClass = $config['strategy'];
                $this->registerStrategy(new $strategyClass());
            }
        }
    }

    /**
     * @var array<string, LoginStrategyInterface>
     */
    protected array $strategies = [];

    /**
     * 执行登录
     * @param string $type 登录类型
     * @param array $credentials 登录凭证
     * @return UserModel|null
     */
    public function doLogin(string $type, array $credentials): ?UserModel
    {
        $this->strategies = $this->strategies ?: $this->getStrategies();

        if (!isset($this->strategies[$type])) {
            return null;
        }

        return $this->strategies[$type]->login($credentials);
    }

    /**
     * 验证并执行登录
     * @param string $loginType 登录类型
     * @param array $params 登录参数
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \Random\RandomException
     * @return array 返回验证结果，包含status、message和user字段
     */
    public function validateLogin(string $loginType, array $params): array
    {
        // 获取当前登录类型的账号标识字段
        $identifierKey = LoginTypeConstant::getIdentifierKey($loginType);

        $tenantCode  = $params['tenant_code']  ?? '';
        $accountName = $params[$identifierKey] ?? '';

        $tokenService = new TokenService();

        // 如果是refresh_token登录，从token中获取租户和用户信息
        if ($loginType === 'refresh_token') {
            $refreshToken = $params['refresh_token'] ?? '';

            if (empty($refreshToken)) {
                return [
                    'status'  => 0,
                    'message' => 'refresh_token不能为空',
                ];
            }

            try {
                $tokenData  = $tokenService->verifyToken($refreshToken, 'refresh_token');
                $tenantCode = $tokenData['tenant_code'] ?? '';
            } catch (\Exception $e) {
                return [
                    'status'  => 0,
                    'message' => $e->getMessage(),
                ];
            }
        }

        // 检查登录失败次数
        $failKey   = LoginTypeConstant::getFailKey($loginType, $tenantCode, $accountName);
        $failCount = cache($failKey) ?: 0;

        if ($failCount >= self::LOGIN_RETRY_COUNT) {
            return [
                'status'  => 0,
                'message' => '账号已被锁定，请' . (self::LOGIN_FAIL_LOCK_DURATION / 60) . '分钟后再试',
            ];
        }

        // 检查租户状态
        $checkTenant = (new TenantRepository())->checkTenantStatus($tenantCode, false);

        if ($checkTenant['status'] === 0) {
            return [
                'status'  => 0,
                'message' => $checkTenant['message'],
            ];
        }

        // 检查成功，将租户ID传到登录方法
        $params['tenant_id'] = $checkTenant['tenant_id'];

        // 执行登录验证
        $user = $this->doLogin($loginType, $params);

        if (!$user) {
            // 记录登录失败次数
            cache($failKey, $failCount + 1, self::LOGIN_FAIL_LOCK_DURATION);

            return [
                'status'  => 0,
                'message' => '登录凭证验证失败',
            ];
        }

        // 检查用户状态
        if ($user->status != 1) {
            return [
                'status'  => 0,
                'message' => '账号已被禁用',
            ];
        }

        // 清除登录失败记录
        cache($failKey, null);

        // 要缓存的用户信息
        $cacheUserInfo = [
            'id'          => $user['id'],
            'username'    => $user['username'],
            'tenant_id'   => $user['tenant_id'],
            'tenant_code' => $tenantCode,
            'is_founder'  => (bool)$user['founder'], // 是否创始人（默认管理员）
        ];

        $cacheService = new CacheService();

        // 签发access_token，并放入缓存
        $accessTokenData = $tokenService->issueToken($cacheUserInfo, 'access_token', true);
        // 签发refresh_token，并放入缓存
        $refreshTokenData = $tokenService->issueToken($cacheUserInfo, 'refresh_token', true);

        // 设置用户信息缓存
        $cacheService->setUserCacheInfo($cacheUserInfo, $refreshTokenData['expires_in']);

        // 将租户用户信息存入Context
        TenantContext::getInstance()->setUserInfo($cacheUserInfo);

        // 更新用户登录信息并记录日志
        $this->updateLoginInfo($user, $loginType);

        // 获取用户权限菜单数据
        $permissionMenu = (new PermissionRepository())->getUserPermissionMenu($user);

        // 要返回的用户信息
        $userInfo = $user->hidden(['tenant_id', 'password', 'salt', 'description', 'create_at', 'update_at', 'delete_at', 'status']);

        // 带上商户编码
        $userInfo['tenant_code'] = $tenantCode;

        return [
            'status'  => 1,
            'message' => $loginType === 'refresh_token' ? '刷新成功' : '登录成功',
            'data'    => [
                'userInfo'           => $userInfo,
                'access_token'       => $accessTokenData['token'],
                'access_expires_in'  => $accessTokenData['expires_in'],
                'refresh_token'      => $refreshTokenData['token'],
                'refresh_expires_in' => $refreshTokenData['expires_in'],
                'menu_data'          => $permissionMenu['menu_data'],
                'permission_data'    => $permissionMenu['permission_data'],
            ],
        ];
    }

    /**
     * 更新用户登录信息并记录日志
     * @param UserModel $user 用户模型
     * @param string $loginType 登录类型
     * @return void
     */
    public function updateLoginInfo(UserModel $user, string $loginType): void
    {
        // 更新用户最后登录信息
        $user->save([
            'last_login_time' => time(),
            'last_login_ip'   => request()->ip(),
        ]);

        // 记录登录日志
        $this->logService->recordLoginLog($loginType, '登录成功', 1);

        // 同时记录到操作日志
        $this->logService->recordOperationLog('用户登录成功');
    }

    /**
     * 注册登录策略
     * @param LoginStrategyInterface $strategy
     * @return void
     */
    public function registerStrategy(LoginStrategyInterface $strategy): void
    {
        $this->strategies[$strategy->getType()] = $strategy;
    }

    /**
     * 获取所有登录策略
     * @return array
     */
    public function getStrategies(): array
    {
        return $this->strategies;
    }

    /**
     * 获取所有支持的登录类型
     * @return array
     */
    public function getSupportedTypes(): array
    {
        return array_keys($this->strategies);
    }
}
