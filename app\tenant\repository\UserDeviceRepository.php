<?php
declare(strict_types = 1);

namespace app\tenant\repository;

use app\tenant\model\UserDevice as UserDeviceModel;
use think\facade\Db;

/**
 * 租户用户设备关联数据仓库类
 * 负责租户用户色和设备之间关联数据的访问和操作
 * <AUTHOR> <<EMAIL>>
 * @package app\tenant\controller
 */
class UserDeviceRepository extends BaseRepository
{
    public function __construct()
    {
        // 指定当前模型类名而非实例，避免条件累积问题
        parent::__construct(UserDeviceModel::class);
    }

    /**
     * 分配设备到用户
     * @param int $userId 用户Id
     * @param array $deviceIds 设备Ids
     * @throws \Exception
     * @return bool
     */
    public function assignDevices(int $userId, array $deviceIds): bool
    {
        // 开启事务
        Db::startTrans();

        try {
            // 删除用户原有的设备关联
            $this->model->where(['user_id' => $userId])->delete();

            // 如果没有设备需要分配，直接提交事务并返回
            if (empty($deviceIds)) {
                Db::commit();

                return true;
            }

            // 准备批量插入数据
            $insertData = [];

            foreach ($deviceIds as $deviceId) {
                $insertData[] = [
                    'user_id'   => $userId,
                    'device_id' => $deviceId,
                    'tenant_id' => $this->getTenantId(),
                ];
            }

            // 批量插入新的设备关联
            $this->model->insertAll($insertData);

            // 提交事务
            Db::commit();

            return true;
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();

            throw $e;
        }
    }

    /**
     * 获取用户直接分配的设备ID列表
     * @param int $userId 用户ID
     * @return array
     */
    public function getAssignedDeviceIds(int $userId): array
    {
        return $this->model
            ->where(['user_id' => $userId])
            ->column('device_id');
    }
}
