<?php
declare(strict_types = 1);
namespace app\system\repository;

use app\common\repository\CommonRepository;
use app\system\model\PermissionRole as PermissionRoleModel;
use app\system\model\Role as RoleModel;
use app\system\service\AuthService;
use think\Model;

/**
 * 角色数据仓库类
 * 负责系统角色相关的数据访问和操作
 * <AUTHOR> <<EMAIL>>
 * @package app\system\repository
 */
class RoleRepository extends CommonRepository
{
    /**
     * 构造方法
     */
    public function __construct()
    {
        // 指定当前模型类名而非实例，避免条件累积问题
        parent::__construct(RoleModel::class);
    }

    /**
     * 获取角色列表
     * @param array $params 查询参数
     * @param int $pageNo 页码
     * @param int $pageSize 每页数量
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return array
     */
    public function getList(array $params = [], int $pageNo = 1, int $pageSize = 20): array
    {
        // 设置基础查询条件 - 非删除数据
        $this->setCondition([
            ['status', '>=', 0],
        ]);

        // 设置搜索条件
        if (!empty($params['keyword'])) {
            $this->setCondition([
                function($query) use ($params) {
                    $query->whereOr([
                        ['name', 'LIKE', "%{$params['keyword']}%"],
                        ['alias', 'LIKE', "%{$params['keyword']}%"],
                        ['description', 'LIKE', "%{$params['keyword']}%"],
                    ]);
                },
            ]);
        }

        // 复用通用分页方法
        $result = $this->pageList($params, $pageNo, $pageSize);

        // 处理状态文本
        if (!empty($result['list'])) {
            $result['list'] = $result['list']->each(function($item) {
                $item->status_cn   = $this->getStatusText($item->status);
                $item->user_counts = (new RoleUserRepository())->getUserCountsByRoleId($item->id, 0);
                $item->permissions = $this->getPermissions($item->id);

                return $item;
            });
        }

        return $result;
    }

    /**
     * 创建角色
     * @param array $data
     * @return \think\Model
     */
    public function createRole(array $data): Model
    {
        $data['create_at'] = time();
        $data['is_system'] = 0;

        return $this->createData($data);
    }

    /**
     * 更新角色
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function updateRole(int $id, array $data): bool
    {
        $data['update_at'] = time();

        return $this->updateData($id, $data);
    }

    /**
     * 获取角色权限
     * @param mixed $roleId
     * @return string
     */
    public function getPermissions(mixed $roleId): string
    {
        $permissions = PermissionRoleModel::getPermissions($roleId);

        return $permissions ? implode(',', $permissions) : '';
    }

    /**
     * 设置角色状态并更新缓存
     * @param mixed $ids
     * @param int $status
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return array
     */
    public function setStatusWithCache(mixed $ids, int $status): array
    {
        $result = $this->setStatus($ids, $status);

        if ($result) {
            // 根据角色id获取角色组下的所有正常用户Ids
            $user_ids = $this->getUserIdsByRoleIds($ids, 1);
            // 根据用户ids更新已登录用户的节点权限缓存
            AuthService::updateCacheUserNodesByUserIds($user_ids);
        }

        return [
            'code'    => $result ? 0 : 1,
            'message' => $result ? '更新成功' : '更新失败',
        ];
    }

    /**
     * 设置角色权限
     * @param int $roleId
     * @param mixed $permissions
     * @param array $data
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return bool
     */
    public function setPermissions(int $roleId, mixed $permissions, array $data = []): bool
    {
        // 设置角色组节点授权
        $result = PermissionRoleModel::setPermission($roleId, $permissions);

        if ($result && !empty($data)) {
            // 更新角色信息
            $this->updateData($roleId, array_merge($data, ['update_at' => time()]));
        }

        if ($result) {
            // 根据角色id获取角色组下的所有正常用户Ids
            $user_ids = $this->getUserIdsByRoleIds($roleId, 1);
            // 根据用户ids更新已登录用户的节点权限缓存
            AuthService::updateCacheUserNodesByUserIds($user_ids);
        }

        return $result;
    }

    /**
     * 启用角色
     * @param $ids
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return bool
     */
    public function enable($ids): bool
    {
        return $this->setStatusWithCache($ids, 1)['code'] === 0;
    }

    /**
     * 禁用角色
     * @param $ids
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return bool
     */
    public function disable($ids): bool
    {
        return $this->setStatusWithCache($ids, 0)['code'] === 0;
    }

    /**
     * 删除角色（软删除）
     * @param mixed $ids
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return bool
     */
    public function deleteData(mixed $ids): bool
    {
        return $this->setStatusWithCache($ids, -1)['code'] === 0;
    }

    /**
     * 根据角色ids获取对应角色组下的所有正常用户列表
     * @param array $role_ids
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return array
     */
    public function getActiveRoleIds(array $role_ids = []): array
    {
        if ($role_ids) {
            $map[] = ['id', 'IN', $role_ids];
            $map[] = ['status', '=', 1]; // 只筛选出正常角色组

            // 根据角色ID获取返回对应的角色列表
            $data = $this->model->where($map)->field('id')->select()->toArray();

            return $data ? array_unique(array_column($data, 'id')) : [];
        }

        return [];
    }

    /**
     * 根据角色ids获取对应角色组下的所有正常用户Ids
     * @param mixed $role_ids
     * @param int $is_active 是否正常用户（如冻结、已删除）
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return array
     */
    public function getUserIdsByRoleIds(mixed $role_ids, int $is_active = 1): array
    {
        // 转成数组
        $role_ids = is_array($role_ids) ? $role_ids : [$role_ids];

        // 为空时查询所有用户数据
        if (empty($role_ids)) {
            return [];
        }

        // 根据角色ID获取返回对应的用户ids
        return (new RoleUserRepository())->getUserIdByRoleId($role_ids, $is_active);
    }
}
