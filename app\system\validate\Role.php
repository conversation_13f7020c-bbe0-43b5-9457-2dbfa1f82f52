<?php
namespace app\system\validate;

use app\system\repository\RoleRepository;

/**
 * 角色验证器类
 * <AUTHOR> <<EMAIL>>
 * @package app\system\validate
 */
class Role extends BaseValidate
{
    protected $rule = [
        'id'     => 'require',
        'name'   => 'require|max:25|checkNameExist',
        'alias'  => 'require|checkAliasFormat|checkAliasExist',
        'sort'   => 'between:-1000,1000',
        'status' => 'in:0,1',
    ];

    protected $message = [
        'id.require'    => 'ID不能为空',
        'name.require'  => '角色名称不能为空',
        'name.max'      => '角色名称最多不能超过25个字符',
        'alias.require' => '角色别名不能为空',
        'sort.between'  => '排序号只能是-1000到1000之间的整数值',
        'status.in'     => '状态值错误',
    ];

    protected $scene = [
        'add_role'  => ['name','alias','sort'],
        'edit_role' => ['id', 'name','alias','sort'],
        'sort'      => ['id', 'sort'],
        'status'    => ['id', 'status'],
    ];

    /**
     * 验证角色名称唯一性
     * @param mixed $value
     * @param mixed $rule
     * @param array $data
     * @return bool|string
     */
    protected function checkNameExist(mixed $value, mixed $rule, array $data)
    {
        $roleRepository = new RoleRepository();
        $id             = isset($data['id']) ? intval($data['id']) : 0;

        if ($roleRepository->checkExist('name', $value, $id)) {
            return '角色【' . $value . '】已存在';
        }

        return true;
    }

    /**
     * 验证角色别名格式
     * @param string $value
     * @return bool|string
     */
    protected function checkAliasFormat($value)
    {
        if (!$this->checkStringValid($value)) {
            return '角色别名格式错误（只能为字母、数字、中间可以用空格、@-_）';
        }

        return true;
    }

    /**
     * 验证角色别名唯一性
     * @param mixed $value
     * @param mixed $rule
     * @param array $data
     * @return bool|string
     */
    protected function checkAliasExist(mixed $value, mixed $rule, array $data)
    {
        $roleRepository = new RoleRepository();

        $id = isset($data['id']) ? intval($data['id']) : 0;

        if ($roleRepository->checkExist('alias', $value, $id)) {
            return '角色别名【' . $value . '】已存在';
        }

        return true;
    }
}
