<?php
declare(strict_types = 1);

namespace app\tenant\service;

use app\tenant\model\LoginLog as LoginLogModel;
use app\tenant\model\OperationLog as OperationLogModel;

/**
 * 租户日志服务类
 * 负责租户系统中的日志记录和管理
 * 包含以下主要功能：
 * - 记录用户登录日志
 * - 记录用户操作日志
 * - 管理日志的存储和查询
 * <AUTHOR> <<EMAIL>>
 * @package app\tenant\service
 */
class LogService extends BaseService
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 记录登录日志
     * @param string $loginType
     * @param string $title
     * @param int $status
     * @return void
     */
    public function recordLoginLog(string $loginType = '', string $title = '登录成功', int $status = 1): void
    {
        LoginLogModel::recordLogin($loginType, $title, $status);
    }

    /**
     * 记录操作日志
     * @param string $title 操作消息
     * @param array $data 相关数据
     */
    public function recordOperationLog(string $title = '操作成功', array $data = []): void
    {
        OperationLogModel::record($title, $data);
    }
}
