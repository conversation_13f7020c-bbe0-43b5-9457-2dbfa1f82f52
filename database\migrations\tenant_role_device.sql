-- 表结构: tenant_role_device
CREATE TABLE `tenant_role_device` (
  `tenant_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '租户ID',
  `role_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '角色ID',
  `device_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '设备ID',
  PRIMARY KEY (`tenant_id`,`role_id`,`device_id`),
  KEY `idx_device` (`device_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='租户角色-设备关联表';
