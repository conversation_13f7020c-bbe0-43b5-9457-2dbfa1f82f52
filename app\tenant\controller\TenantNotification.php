<?php
declare(strict_types = 1);

namespace app\tenant\controller;

use app\tenant\context\TenantContext;
use think\App;

/**
 * 租户模块消息通知控制器
 * <AUTHOR> <<EMAIL>>
 * @package app\tenant\controller
 */
class TenantNotification extends AuthBase
{
    public function __construct(App $app)
    {
        parent::__construct($app);
    }

    /**
     * 获取当前用户ID
     * @return int
     */
    protected function getCurrentUserId(): int
    {
        return TenantContext::getInstance()->getUserId();
    }
}
