# 项目介绍

当前项目是基于thinkphp8开发的，开启了多应用模式，目前app下有common（主要定义全局模块依赖，其中也有controller、model、service等）、system（系统基础模块，如基础权限框架、用户管理等等一些常用后台的管理功能）、api（主要用于前台客户端接口）、tenent（租户模块，主要用于saas租户的管理）。其他模块的controller、model、service等模块则是继承自common模块的。

# 项目目录结构

```
├───app
│   ├───api
│   │   └───controller
│   ├───common
│   │   ├───constant
│   │   ├───exception
│   │   ├───middleware
│   │   ├───model
│   │   ├───service
│   │   ├───traits
│   │   └───view
│   ├───demo
│   │   ├───config
│   │   ├───controller
│   │   └───database
│   │       └───upgrade
│   ├───system
│   │   ├───config
│   │   ├───constant
│   │   ├───controller
│   │   ├───exception
│   │   ├───job
│   │   ├───middleware
│   │   ├───model
│   │   ├───repository
│   │   ├───route
│   │   ├───service
│   │   ├───traits
│   │   └───validate
│   ├───task
│   └───tenant
│       ├───controller
│       └───model
├───config
├───database
│   └───migrations
├───deploy
│   └───supervisor
├───docs
│   └───knowledge
├───extend
│   ├───helio
│   └───owns
│       └───library
├───public
├───route
├───runtime
└───vendor
```