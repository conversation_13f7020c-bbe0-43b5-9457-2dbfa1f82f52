# 登录鉴权模式说明文档

## 1. 配置说明

在项目根目录 `.env` 文件中通过以下配置设置登录模式:

```env
[JWT]
LOGIN_MODE = "single" # 单点登录
# 或
LOGIN_MODE = "multiple_separate" # 多端独立登录
# 或  
LOGIN_MODE = "multiple_share" # 多端共享登录
```

## 2. 登录模式详细说明

### 2.1 单点登录模式 (single)
- **特点**: 一个账号只允许在一个地方登录,新登录会踢掉旧登录
- **使用场景**: 对安全性要求高的系统 
- **实现逻辑**:
  - 用户登录时先清除该用户已有的所有token缓存
  - 生成新的token并缓存
  - 每次请求都会验证token是否与缓存中的一致
  - 当同一账号在其他地方登录时,之前的token会失效

### 2.2 多端独立登录模式 (multiple_separate) 
- **特点**: 同一账号可以在多个终端同时登录,每个终端使用独立的token
- **使用场景**: 需要支持多端但又要区分每个终端的场景
- **实现逻辑**:
  - 登录时携带设备信息生成唯一标识
  - 每个设备生成独立的token并按 user_id:finger_id 缓存
  - 验证时根据当前设备标识获取对应token验证
  - 不同设备间的登录互不影响

### 2.3 多端共享登录模式 (multiple_share)
- **特点**: 同一账号可以在多个终端同时登录,共用同一个token
- **使用场景**: 对用户体验要求高的系统
- **实现逻辑**: 
  - 首次登录时生成token并缓存
  - 其他终端登录时复用已缓存的token
  - 验证时只检查token本身合法性
  - 所有终端共享同一个token,互不影响

## 3. Token机制说明

系统采用双token机制:
- `access_token`: 用于接口访问认证,默认48小时过期
- `refresh_token`: 用于刷新access_token,默认7天过期

Token相关配置:
```php
const TOKEN_EXPIRE = [
    'access_token'  => 2 * 24 * 60 * 60,  // 48小时
    'refresh_token' => 7 * 24 * 60 * 60   // 7天
];
```

## 4. 缓存说明

系统使用Redis缓存以下信息:

### 4.1 基础缓存
```
backend_user:{user_id}:info          # 用户基本信息
backend_user:{user_id}:nodes         # 用户权限节点
backend_user:{user_id}:access_token  # access_token
backend_user:{user_id}:refresh_token # refresh_token  
```

### 4.2 多端独立模式额外缓存
```
backend_user:{user_id}:{device_id}:access_token  # 设备access_token
backend_user:{user_id}:{device_id}:refresh_token # 设备refresh_token
```

## 5. 切换模式注意事项

1. 切换登录模式会导致已登录用户的token失效
2. 建议在系统维护期间进行切换
3. 切换后需要所有用户重新登录
4. 多端共享模式下token泄露风险较大,建议:
   - 缩短token有效期
   - 加强token签名验证
   - 增加IP限制等额外安全措施
```

这个文档详细说明了三种登录模式的:
1. 配置方法
2. 实现原理 
3. 使用场景
4. 注意事项

方便开发和运维人员理解和使用系统的登录鉴权机制。

已进行更改。

teng315: 还是没有写入对应的文档中，请把输出的内容都写入对应的文档

GitHub Copilot: 好的,我会创建并写入登录鉴权相关的文档:

```markdown


# 用户登录鉴权机制说明文档

## 一、登录鉴权模式

系统支持三种登录鉴权模式,可在 `.env` 中配置:

```env
[JWT]
LOGIN_MODE = "single"            # 单点登录模式
LOGIN_MODE = "multiple_separate" # 多端独立登录模式  
LOGIN_MODE = "multiple_share"    # 多端共享登录模式
```

### 1. 单点登录模式 (single)
- 特点: 一个账号只能同时在一个端登录,新登录会踢掉之前的登录
- 实现细节:
  - 用户登录时清除该用户已有的 token 缓存
  - 生成新的 token 并缓存
  - 每次请求验证 token 是否与缓存中的一致
- 使用场景: 对安全性要求高的系统

### 2. 多端独立登录模式 (multiple_separate)  
- 特点: 支持同一账号在多个终端同时登录,每个终端使用独立的 token
- 实现细节:
  - 用户登录时根据设备信息生成唯一标识
  - 为每个设备生成独立的 token 并缓存
  - 验证时根据设备标识获取对应的 token 进行验证
- 使用场景: 需要区分不同终端登录的系统

### 3. 多端共享登录模式 (multiple_share)
- 特点: 支持同一账号在多个终端同时登录,所有终端共用同一个 token
- 实现细节:
  - 首次登录时生成 token 并缓存
  - 其他终端登录时复用已缓存的 token
  - 只验证 token 本身的合法性,不验证缓存
- 使用场景: 对用户体验要求高的系统

## 二、Token 机制说明

系统采用双 token 机制:
1. access_token: 业务接口访问凭证
   - 默认有效期: 48小时
   - 用途: 接口调用权限验证
   
2. refresh_token: 刷新令牌  
   - 默认有效期: 7天
   - 用途: 用于刷新过期的 access_token

## 三、缓存说明

系统使用 Redis 缓存以下信息:

```bash
# 基础缓存
backend_user:{user_id}:info           # 用户基本信息
backend_user:{user_id}:nodes          # 用户权限节点
backend_user:{user_id}:access_token   # access_token
backend_user:{user_id}:refresh_token  # refresh_token

# 多端独立模式额外缓存
backend_user:{user_id}:{device_id}:access_token   # 设备access_token
backend_user:{user_id}:{device_id}:refresh_token  # 设备refresh_token
```

## 四、配置说明

1. Token 相关配置:
```php
// token 有效期配置
const TOKEN_EXPIRE = [
    'access_token'  => 2 * 24 * 60 * 60,  // access_token 48小时
    'refresh_token' => 7 * 24 * 60 * 60   // refresh_token 7天
];
```

2. JWT 配置:
```env
[JWT]
JWT_KEY = "your-secret-key"    # JWT密钥
LOGIN_MODE = "single"          # 登录模式
```

## 五、切换登录模式注意事项

1. 切换时会导致已登录用户的 token 失效
2. 建议在系统维护期间进行切换
3. 切换后需要所有用户重新登录
4. 各模式安全建议:
   - 单点登录: 可适当延长 token 有效期
   - 多端独立: 可限制最大设备数 
   - 多端共享: 建议缩短 token 有效期,加强安全控制

## 六、登录流程

```mermaid
graph TD
A[客户端请求登录] --> B{验证账号密码}
B -->|验证失败| C[返回错误信息]
B -->|验证成功| D[根据登录模式处理token]
D -->|单点登录| E[清除旧token,生成新token]
D -->|多端独立| F[生成设备独立token]
D -->|多端共享| G[复用或生成共享token]
E --> H[返回token给客户端]
F --> H
G --> H
```
