<?php
namespace app\system\validate;

/**
 * 用户验证器类
 * <AUTHOR> <<EMAIL>>
 * @package app\system\validate
 */
class User extends BaseValidate
{
    protected $rule = [
        'id'       => 'require',
        'username' => 'require|max:25',
        'realname' => 'require',
        'password' => 'require',
        'birthday' => 'date',
        'phone'    => 'mobile',
        'email'    => 'email',
        'gender'   => 'in:0,1,2',
        'depts'    => 'require',
        'roles'    => 'require',
        'status'   => 'in:0,1',
    ];

    protected $message = [
        'id.require'       => 'ID不能为空',
        'username.require' => '用户名不能为空',
        'username.max'     => '用户名最多不能超过25个字符',
        'realname.require' => '姓名不能为空',
        'password.require' => '密码不能为空',
        'birthday'         => '生日格式错误',
        'phone'            => '手机号错误',
        'email'            => '邮箱格式错误',
        'gender'           => '性别数值错误',
        'depts.require'    => '所属部门不能为空',
        'roles.require'    => '角色组不能为空',
        'status.in'        => '状态值错误',
    ];

    protected $scene = [
        'add'     => ['username','realname','birthday','phone','email','gender','depts','roles'],
        'edit'    => ['id', 'username','realname','birthday','phone','email','gender','depts','roles'],
        'profile' => ['username','realname','birthday','phone','email','gender'],
        'status'  => ['id', 'status'],
    ];
}
