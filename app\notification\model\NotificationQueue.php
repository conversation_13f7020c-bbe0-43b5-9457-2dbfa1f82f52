<?php
declare(strict_types = 1);

namespace app\notification\model;

use app\notification\service\NotificationCounterService;

/**
 * 消息通知队列模型
 * 功能：
 * 1. 实现读扩散策略
 * 2. 管理用户消息的接收状态
 * 3. 支持消息的软删除
 * 4. 记录消息的阅读时间
 * <AUTHOR> <<EMAIL>>
 * @package app\notification\model
 */
class NotificationQueue extends BaseModel
{
    protected $name = 'notification_queue';

    // 字段类型
    protected $type = [
        'id'              => 'integer',
        'tenant_id'       => 'integer',
        'notification_id' => 'integer',
        'user_id'         => 'integer',
        'is_read'         => 'integer',
        'interact_status' => 'integer',
        'is_deleted'      => 'integer',
    ];

    /**
     * 软删除消息
     * @param int $notificationId
     * @param int $userId
     * @return bool
     */
    public function softDelete(int $notificationId, int $userId): bool
    {
        return $this->batchSoftDelete([$notificationId], $userId);
    }

    /**
     * 批量软删除消息
     * @param array $notificationIds 消息ID数组
     * @param int $userId 用户ID
     * @return bool
     */
    public function batchSoftDelete(array $notificationIds, int $userId): bool
    {
        try {
            $result = $this->where([
                ['notification_id', 'in', $notificationIds],
                ['user_id', '=', $userId],
            ])->update([
                'is_deleted' => 1,
                'delete_at'  => time(),
                'update_at'  => time(),
            ]);

            return (bool)$result;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 批量更新阅读状态
     * @param array $notificationIds 消息ID数组
     * @param int $userId 用户ID
     * @param int $status 阅读状态 0=未读 1=已读
     * @return bool
     */
    public function batchUpdateReadStatus(array $notificationIds, int $userId, int $status): bool
    {
        try {
            $updateData = [
                'is_read'   => $status,
                'update_at' => time(),
            ];

            // 如果是标记已读，则记录阅读时间
            if ($status === 1) {
                $updateData['read_at'] = time();
            } else {
                $updateData['read_at'] = 0;
            }

            $result = $this->where([
                ['notification_id', 'in', $notificationIds],
                ['user_id', '=', $userId],
                ['is_deleted', '=', 0],
            ])->update($updateData);

            return (bool)$result;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 获取用户的互动状态
     * @param int $notificationId
     * @param int $userId
     * @return int
     */
    public function getInteractStatus(int $notificationId, int $userId): int
    {
        try {
            $queue = $this->where([
                'notification_id' => $notificationId,
                'user_id'         => $userId,
                'is_deleted'      => 0,
            ])->find();

            if (!$queue) {
                return 0;
            }

            return $queue->interact_status;
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * 更新互动状态
     * @param int $notificationId 消息ID
     * @param int $userId 用户ID
     * @param int $status 互动状态 1=点赞 -1=踩 0=取消
     * @return bool
     */
    public function updateInteractStatus(int $notificationId, int $userId, int $status): bool
    {
        try {
            $result = $this->where([
                'notification_id' => $notificationId,
                'user_id'         => $userId,
                'is_deleted'      => 0,
            ])->update([
                'interact_status' => $status,
                'interact_at'     => $status ? time() : 0,
                'update_at'       => time(),
            ]);
            // 更新成功后，立即更新缓存
            $counterService = new NotificationCounterService();
            $counterService->cacheInteractionStatus($notificationId, $userId, $status);

            return (bool)$result;
        } catch (\Exception $e) {
            return false;
        }
    }
}
