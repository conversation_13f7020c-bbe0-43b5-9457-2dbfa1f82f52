<?php
declare(strict_types = 1);

namespace app\tenant\controller;

use app\notification\repository\NotificationRepository;
use app\notification\traits\NotificationTrait;
use app\tenant\context\TenantContext;
use think\App;
use think\Request;
use think\Response;

/**
 * 租户消息通知控制器
 * 提供租户级别的消息通知功能
 * <AUTHOR> <<EMAIL>>
 * @package app\tenant\controller
 */
class Notification extends AuthBase
{
    use NotificationTrait;

    /**
     * @var NotificationRepository
     */
    protected NotificationRepository $repository;

    public function __construct(App $app, NotificationRepository $repository)
    {
        parent::__construct($app);
        $this->repository = $repository;
    }

    /**
     * 获取当前用户ID
     * @return int
     */
    protected function getCurrentUserId(): int
    {
        return $this->getUserId();
    }

    /**
     * 获取租户用户是否是创始人
     * @return int
     */
    protected function isFounder(): int
    {
        return TenantContext::getInstance()->isFounder();
    }

    /**
     * 获取当前租户ID
     * 在tenant模块中返回当前租户ID
     * @return int
     */
    protected function getTenantId(): int
    {
        return TenantContext::getInstance()->getTenantId();
    }

    /**
     * 获取接收者列表
     * @return \think\Response
     */
    public function receivers(): Response
    {
        // 获取当前租户下的用户
        return $this->success([]);
    }

    /**
     * 获取部门列表
     * @return \think\Response
     */
    public function departments(): Response
    {
        // 获取当前租户下的部门
        return $this->success([]);
    }

    /**
     * 获取已发送消息列表
     * @param \think\Request $request
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return \think\Response
     */
    public function sentList(Request $request): Response
    {
        $params = $request->param();
        // 添加租户ID参数
        $params['tenant_id'] = $this->getTenantId();

        $result = $this->repository->getSentList($this->getUserId(), $params);

        return $this->success($result);
    }

    /**
     * 重写发送消息方法，添加租户ID
     * @param \think\Request $request
     * @return \think\Response
     */
    public function send(Request $request): Response
    {
        $validate = new \app\notification\validate\Notification();
        $data     = $request->param();

        // 确保 target_ids 是数组
        $data['target_ids'] = empty($data['target_ids']) ? [] : (is_array($data['target_ids']) ? $data['target_ids'] : json_decode($data['target_ids'], true));

        // 添加租户ID
        $data['tenant_id'] = $this->getTenantId();

        if (!$validate->scene('send')->check($data)) {
            return $this->error($validate->getError());
        }

        $result = $this->repository->send($data, $this->getCurrentUserId());

        if ($result['status'] === 1) {
            return $this->success();
        }

        return $this->error($result['message']);
    }

    /**
     * 重写获取消息列表方法，添加租户ID
     * @param \think\Request $request
     * @return \think\Response
     */
    public function list(Request $request): Response
    {
        $validate = new \app\notification\validate\Notification();
        $params   = $request->param();

        if (!$validate->scene('list')->check($params)) {
            return $this->error($validate->getError());
        }

        // 添加租户ID参数
        $params['tenant_id'] = $this->getTenantId();

        $result = $this->repository->getReceivedList($this->getCurrentUserId(), $params);

        return $this->success($result);
    }
}
