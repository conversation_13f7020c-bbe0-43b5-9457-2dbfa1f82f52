<?php
declare(strict_types = 1);

namespace app\demo\controller;

use app\BaseController;
use think\facade\Db;
use think\Response;

/**
 * 示例控制器
 * @package app\demo\controller
 */
class Index extends BaseController
{
    /**
     * 列表
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return \think\Response
     */
    public function list(): Response
    {
        $page     = input('page/d', 1);
        $pageSize = input('pageSize/d', 15);

        $list = Db::name('demo_data')
            ->where('status', 1)
            ->order('id', 'desc')
            ->page($page, $pageSize)
            ->select();

        return $this->success($list);
    }

    /**
     * 添加
     * @return \think\Response
     */
    public function add(): Response
    {
        $data = input('post.');

        $id = Db::name('demo_data')->insertGetId([
            'title'     => $data['title'],
            'content'   => $data['content'],
            'create_at' => time(),
            'update_at' => time(),
        ]);

        return $this->success(['id' => $id]);
    }
}
