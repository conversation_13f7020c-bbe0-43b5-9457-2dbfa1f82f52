<?php
declare(strict_types = 1);

namespace app\tenant\model;

/**
 * 设备模型类
 * <AUTHOR> <<EMAIL>>
 * @package app\tenant\model
 */
class Device extends BaseModel
{
    protected $name = 'tenant_device';

    // 字段类型
    protected $type = [
        'id'        => 'integer',
        'tenant_id' => 'integer',
        'code'      => 'string',
        'name'      => 'string',
        'location'  => 'string',
        'depth'     => 'float',
        'level'     => 'integer',
        'create_at' => 'integer',
        'update_at' => 'integer',
        'delete_at' => 'integer',
        'status'    => 'integer',
    ];

    // 状态常量定义
    public const STATUS_DELETED = -1; // 已删除

    public const STATUS_DISABLE = 0; // 停用

    public const STATUS_NORMAL = 1; // 正常

    public const STATUS_ERROR = 2; // 异常

    public const STATUS_REPAIR = 3; // 维修中

    /**
     * 获取状态文本
     * @param int $status
     * @return string
     */
    public static function getStatusText(int $status): string
    {
        $statusMap = [
            self::STATUS_DELETED => '已删除',
            self::STATUS_DISABLE => '停用',
            self::STATUS_NORMAL  => '正常',
            self::STATUS_ERROR   => '异常',
            self::STATUS_REPAIR  => '维修中',
        ];

        return $statusMap[$status] ?? '未知';
    }
}
